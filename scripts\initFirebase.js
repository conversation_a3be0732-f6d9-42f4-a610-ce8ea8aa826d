// Script para inicializar Firebase con datos de ejemplo
// Ejecutar con: node scripts/initFirebase.js

import { initializeApp } from 'firebase/app'
import { getFirestore, collection, addDoc } from 'firebase/firestore'
import { sampleProducts } from '../src/data/sampleProducts.js'

// Configuración de Firebase (reemplazar con tus valores)
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
}

// Inicializar Firebase
const app = initializeApp(firebaseConfig)
const db = getFirestore(app)

async function initializeProducts() {
  console.log('🚀 Inicializando productos en Firebase...')
  
  try {
    for (const product of sampleProducts) {
      const docRef = await addDoc(collection(db, 'products'), product)
      console.log(`✅ Producto agregado: ${product.name} (ID: ${docRef.id})`)
    }
    
    console.log('🎉 ¡Todos los productos han sido agregados exitosamente!')
    console.log(`📊 Total de productos: ${sampleProducts.length}`)
    
  } catch (error) {
    console.error('❌ Error al agregar productos:', error)
  }
}

// Ejecutar la inicialización
initializeProducts()
  .then(() => {
    console.log('✨ Inicialización completada')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Error en la inicialización:', error)
    process.exit(1)
  })
