import {
  signInWithPopup,
  GoogleAuthProvider,
  signOut as firebaseSignOut,
  onAuthStateChanged as firebaseOnAuthStateChanged
} from 'firebase/auth'
import { auth } from '../config/firebase'

// Para Capacitor, usamos el plugin nativo
import { FirebaseAuthentication } from '@capacitor-firebase/authentication'
import { Capacitor } from '@capacitor/core'

const provider = new GoogleAuthProvider()

export const authService = {
  async signInWithGoogle() {
    try {
      if (Capacitor.isNativePlatform()) {
        // Usar plugin de Capacitor en móviles
        const result = await FirebaseAuthentication.signInWithGoogle()
        return {
          user: result.user,
          credential: result.credential
        }
      } else {
        // Usar Firebase Web SDK en navegador
        const result = await signInWithPopup(auth, provider)
        return {
          user: result.user,
          credential: GoogleAuthProvider.credentialFromResult(result)
        }
      }
    } catch (error) {
      console.error('Error signing in with Google:', error)
      throw error
    }
  },

  async signOut() {
    try {
      if (Capacitor.isNativePlatform()) {
        await FirebaseAuthentication.signOut()
      } else {
        await firebaseSignOut(auth)
      }
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    }
  },

  async getCurrentUser() {
    try {
      if (Capacitor.isNativePlatform()) {
        const result = await FirebaseAuthentication.getCurrentUser()
        return result.user
      } else {
        return auth.currentUser
      }
    } catch (error) {
      console.error('Error getting current user:', error)
      return null
    }
  },

  onAuthStateChanged(callback) {
    if (Capacitor.isNativePlatform()) {
      return FirebaseAuthentication.addListener('authStateChange', callback)
    } else {
      return firebaseOnAuthStateChanged(auth, (user) => {
        callback({ user })
      })
    }
  }
}
