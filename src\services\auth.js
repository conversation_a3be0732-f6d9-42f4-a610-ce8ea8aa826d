import { CapacitorFirebaseAuth } from '@capacitor-firebase/authentication'

export const authService = {
  async signInWithGoogle() {
    try {
      const result = await CapacitorFirebaseAuth.signInWithGoogle()
      return {
        user: result.user,
        credential: result.credential
      }
    } catch (error) {
      console.error('Error signing in with Google:', error)
      throw error
    }
  },

  async signOut() {
    try {
      await CapacitorFirebaseAuth.signOut()
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    }
  },

  async getCurrentUser() {
    try {
      const result = await CapacitorFirebaseAuth.getCurrentUser()
      return result.user
    } catch (error) {
      console.error('Error getting current user:', error)
      return null
    }
  },

  onAuthStateChanged(callback) {
    return CapacitorFirebaseAuth.addListener('authStateChange', callback)
  }
}
