import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, CreditCard, MapPin, Package, CheckCircle } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Badge } from '../components/ui/Badge'
import { AddressSelector } from '../components/AddressSelector'
import { useCart } from '../context/CartContext'
import { useAuth } from '../context/AuthContext'
import { stripeService } from '../services/stripe'
import { ordersService } from '../services/firestore'
import { formatPrice } from '../lib/utils'

export const CheckoutPage = () => {
  const navigate = useNavigate()
  const { items, getTotalPrice, clearCart } = useCart()
  const { user, isAuthenticated } = useAuth()
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedAddress, setSelectedAddress] = useState(null)
  const [processing, setProcessing] = useState(false)
  const [orderComplete, setOrderComplete] = useState(false)
  const [orderId, setOrderId] = useState(null)

  const totalPrice = getTotalPrice()
  const shippingCost = totalPrice > 100 ? 0 : 15
  const finalTotal = totalPrice + shippingCost

  useEffect(() => {
    // Redirect if cart is empty
    if (items.length === 0 && !orderComplete) {
      navigate('/cart')
    }
  }, [items, navigate, orderComplete])

  useEffect(() => {
    // Initialize Stripe
    stripeService.initialize().catch(console.error)
  }, [])

  const handleContinueToPayment = () => {
    if (!selectedAddress) {
      alert('Por favor selecciona una dirección de envío')
      return
    }
    setCurrentStep(2)
  }

  const handlePayment = async () => {
    try {
      setProcessing(true)

      // Create payment sheet
      const paymentIntent = await stripeService.createPaymentSheet(finalTotal)
      
      // Present payment sheet
      await stripeService.presentPaymentSheet()
      
      // Confirm payment
      const result = await stripeService.confirmPaymentSheet()
      
      if (result.paymentResult === 'Completed') {
        // Create order in Firestore
        const orderData = {
          userId: user?.uid || null,
          products: items.map(item => ({
            id: item.id,
            name: item.name,
            price: item.price,
            quantity: item.quantity,
            imageUrl: item.imageUrl
          })),
          shippingAddress: selectedAddress,
          totalAmount: finalTotal,
          stripeTransactionId: paymentIntent.id,
          status: 'awaiting_admin_validation'
        }

        const newOrderId = await ordersService.create(orderData)
        setOrderId(newOrderId)
        setOrderComplete(true)
        clearCart()
        setCurrentStep(3)
      }
    } catch (error) {
      console.error('Payment error:', error)
      alert('Error al procesar el pago. Por favor intenta de nuevo.')
    } finally {
      setProcessing(false)
    }
  }

  const steps = [
    { number: 1, title: 'Dirección de envío', icon: MapPin },
    { number: 2, title: 'Método de pago', icon: CreditCard },
    { number: 3, title: 'Confirmación', icon: CheckCircle }
  ]

  if (orderComplete) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-lime-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="h-8 w-8 text-lime-green-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              ¡Pedido confirmado!
            </h2>
            <p className="text-gray-600 mb-6">
              Tu pedido #{orderId?.slice(-8)} ha sido recibido y está siendo procesado.
            </p>
            <div className="space-y-3">
              <Button 
                className="w-full"
                onClick={() => navigate('/my-profile/orders')}
              >
                Ver mis pedidos
              </Button>
              <Button 
                variant="outline"
                className="w-full"
                onClick={() => navigate('/')}
              >
                Continuar comprando
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/cart')}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Volver al carrito</span>
          </Button>
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-8">
            {steps.map((step) => {
              const Icon = step.icon
              const isActive = currentStep === step.number
              const isCompleted = currentStep > step.number
              
              return (
                <div key={step.number} className="flex items-center space-x-2">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    isCompleted 
                      ? 'bg-lime-green-500 text-white' 
                      : isActive 
                        ? 'bg-electric-orange-500 text-white' 
                        : 'bg-gray-200 text-gray-500'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <span className={`text-sm font-medium ${
                    isActive ? 'text-electric-orange-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </span>
                </div>
              )
            })}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <MapPin className="h-5 w-5" />
                    <span>Dirección de envío</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <AddressSelector
                    selectedAddress={selectedAddress}
                    onAddressSelect={setSelectedAddress}
                    allowGuest={true}
                  />
                  
                  {selectedAddress && (
                    <div className="mt-6">
                      <Button 
                        onClick={handleContinueToPayment}
                        className="w-full"
                      >
                        Continuar al pago
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {currentStep === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CreditCard className="h-5 w-5" />
                    <span>Método de pago</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Selected Address Summary */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">
                      Enviar a:
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {selectedAddress?.name}<br />
                      {selectedAddress?.street}<br />
                      {selectedAddress?.city}, {selectedAddress?.state} {selectedAddress?.zipCode}
                    </p>
                  </div>

                  {/* Payment Method */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900">
                      Método de pago
                    </h4>
                    <Card className="border-electric-orange-200 bg-electric-orange-50">
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <CreditCard className="h-6 w-6 text-electric-orange-600" />
                          <div>
                            <p className="font-medium text-gray-900">
                              Tarjeta de crédito/débito
                            </p>
                            <p className="text-sm text-gray-600">
                              Procesado de forma segura por Stripe
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Button 
                    onClick={handlePayment}
                    disabled={processing}
                    className="w-full"
                    size="lg"
                  >
                    {processing ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Procesando...</span>
                      </div>
                    ) : (
                      `Pagar ${formatPrice(finalTotal)}`
                    )}
                  </Button>

                  <p className="text-xs text-gray-500 text-center">
                    🔒 Tu información de pago está protegida con encriptación SSL
                  </p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="h-5 w-5" />
                  <span>Resumen del pedido</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Items */}
                <div className="space-y-3">
                  {items.map((item) => (
                    <div key={item.id} className="flex items-center space-x-3">
                      <img 
                        src={item.imageUrl || `https://placehold.co/50x50/f3f4f6/9ca3af/PNG?text=${encodeURIComponent(item.name)}`}
                        alt={item.name}
                        className="w-12 h-12 object-cover rounded-lg"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {item.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          Cantidad: {item.quantity}
                        </p>
                      </div>
                      <p className="text-sm font-medium text-gray-900">
                        {formatPrice(item.price * item.quantity)}
                      </p>
                    </div>
                  ))}
                </div>

                <hr />

                {/* Totals */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>{formatPrice(totalPrice)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Envío</span>
                    <span className={shippingCost === 0 ? 'text-green-600 font-medium' : ''}>
                      {shippingCost === 0 ? 'Gratis' : formatPrice(shippingCost)}
                    </span>
                  </div>
                </div>

                <hr />

                <div className="flex justify-between text-lg font-bold">
                  <span>Total</span>
                  <span className="text-electric-orange-600">
                    {formatPrice(finalTotal)}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
