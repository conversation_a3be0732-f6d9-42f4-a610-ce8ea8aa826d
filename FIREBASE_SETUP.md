# 🔥 Configuración de Firebase para FuXion App

## 1. <PERSON><PERSON>r Proyecto en Firebase

1. Ve a [Firebase Console](https://console.firebase.google.com/)
2. Haz clic en "Crear un proyecto"
3. Nombra tu proyecto (ej: "fuxion-ecommerce")
4. Habilita Google Analytics (opcional)
5. Crea el proyecto

## 2. Configurar Authentication

1. En el panel izquierdo, ve a **Authentication**
2. Haz clic en **Comenzar**
3. Ve a la pestaña **Sign-in method**
4. Habilita **Google** como proveedor
5. Configura el email de soporte del proyecto

## 3. Configurar Firestore Database

1. En el panel izquierdo, ve a **Firestore Database**
2. Haz clic en **Crear base de datos**
3. Selecciona **Comenzar en modo de prueba** (por ahora)
4. Elige una ubicación (recomendado: us-central1)

### Reglas de Seguridad de Firestore

Reemplaza las reglas por defecto con estas:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Products - lectura pública, escritura solo admin
    match /products/{document} {
      allow read: if true;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    // Users - solo el usuario autenticado
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Addresses subcollection
      match /addresses/{addressId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Orders - crear por cualquier usuario autenticado
    match /orders/{orderId} {
      allow create: if request.auth != null;
      allow read: if request.auth != null && 
        (resource.data.userId == request.auth.uid || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
      allow update, delete: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
  }
}
```

## 4. Obtener Configuración del Proyecto

1. Ve a **Configuración del proyecto** (ícono de engranaje)
2. En la pestaña **General**, baja hasta **Tus apps**
3. Haz clic en **</> Web**
4. Registra tu app con un nombre
5. Copia la configuración de Firebase

## 5. Configurar Variables de Entorno

1. Crea un archivo `.env` en la raíz del proyecto:

```bash
# Firebase Configuration
VITE_FIREBASE_API_KEY=tu-api-key-aqui
VITE_FIREBASE_AUTH_DOMAIN=tu-proyecto.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=tu-proyecto-id
VITE_FIREBASE_STORAGE_BUCKET=tu-proyecto.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=tu-app-id

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_tu_stripe_key_aqui

# Environment
VITE_NODE_ENV=development
```

## 6. Inicializar Datos de Productos

1. Actualiza `scripts/initFirebase.js` con tu configuración
2. Ejecuta el script:

```bash
node scripts/initFirebase.js
```

## 7. Crear Usuario Administrador

1. Regístrate en la app usando Google
2. Ve a Firestore Database
3. Crea una colección `users`
4. Crea un documento con tu UID de usuario
5. Agrega el campo: `isAdmin: true`

## 8. Configurar Capacitor para Firebase

### Para iOS

1. Ve a **Configuración del proyecto** > **General**
2. En **Tus apps**, agrega una app iOS
3. Usa el Bundle ID: `com.fuxion.app`
4. Descarga `GoogleService-Info.plist`
5. Colócalo en `ios/App/App/`

### Para Android

1. Ve a **Configuración del proyecto** > **General**
2. En **Tus apps**, agrega una app Android
3. Usa el Package name: `com.fuxion.app`
4. Descarga `google-services.json`
5. Colócalo en `android/app/`

## 9. Verificar Configuración

1. Ejecuta la app: `npm run dev`
2. Verifica que los productos se cargan
3. Prueba el login con Google
4. Verifica que el carrito funciona

## 🔧 Comandos Útiles

```bash
# Instalar dependencias
npm install

# Ejecutar en desarrollo
npm run dev

# Construir para producción
npm run build

# Sincronizar con Capacitor
npx cap sync

# Abrir en iOS
npx cap open ios

# Abrir en Android
npx cap open android
```

## 🚨 Notas Importantes

- **Nunca** subas el archivo `.env` a Git
- Las reglas de Firestore son críticas para la seguridad
- Configura dominios autorizados en Firebase Auth
- Para producción, cambia las reglas de Firestore a modo producción
- Configura límites de facturación en Firebase

## 📞 Soporte

Si tienes problemas con la configuración:

1. Verifica que todas las variables de entorno estén configuradas
2. Revisa la consola del navegador para errores
3. Verifica las reglas de Firestore
4. Asegúrate de que los dominios estén autorizados en Firebase Auth
