// node_modules/@capacitor-community/stripe/dist/esm/applepay/apple-pay-events.enum.js
var ApplePayEventsEnum;
(function(ApplePayEventsEnum2) {
  ApplePayEventsEnum2["Loaded"] = "applePayLoaded";
  ApplePayEventsEnum2["FailedToLoad"] = "applePayFailedToLoad";
  ApplePayEventsEnum2["Completed"] = "applePayCompleted";
  ApplePayEventsEnum2["Canceled"] = "applePayCanceled";
  ApplePayEventsEnum2["Failed"] = "applePayFailed";
  ApplePayEventsEnum2["DidSelectShippingContact"] = "applePayDidSelectShippingContact";
  ApplePayEventsEnum2["DidCreatePaymentMethod"] = "applePayDidCreatePaymentMethod";
})(ApplePayEventsEnum || (ApplePayEventsEnum = {}));

// node_modules/@capacitor-community/stripe/dist/esm/googlepay/google-pay-events.enum.js
var GooglePayEventsEnum;
(function(GooglePayEventsEnum2) {
  GooglePayEventsEnum2["Loaded"] = "googlePayLoaded";
  GooglePayEventsEnum2["FailedToLoad"] = "googlePayFailedToLoad";
  GooglePayEventsEnum2["Completed"] = "googlePayCompleted";
  GooglePayEventsEnum2["Canceled"] = "googlePayCanceled";
  GooglePayEventsEnum2["Failed"] = "googlePayFailed";
})(GooglePayEventsEnum || (GooglePayEventsEnum = {}));

// node_modules/@capacitor-community/stripe/dist/esm/paymentflow/payment-flow-events.enum.js
var PaymentFlowEventsEnum;
(function(PaymentFlowEventsEnum2) {
  PaymentFlowEventsEnum2["Loaded"] = "paymentFlowLoaded";
  PaymentFlowEventsEnum2["FailedToLoad"] = "paymentFlowFailedToLoad";
  PaymentFlowEventsEnum2["Opened"] = "paymentFlowOpened";
  PaymentFlowEventsEnum2["Created"] = "paymentFlowCreated";
  PaymentFlowEventsEnum2["Completed"] = "paymentFlowCompleted";
  PaymentFlowEventsEnum2["Canceled"] = "paymentFlowCanceled";
  PaymentFlowEventsEnum2["Failed"] = "paymentFlowFailed";
})(PaymentFlowEventsEnum || (PaymentFlowEventsEnum = {}));

// node_modules/@capacitor-community/stripe/dist/esm/paymentsheet/payment-sheet-events.enum.js
var PaymentSheetEventsEnum;
(function(PaymentSheetEventsEnum2) {
  PaymentSheetEventsEnum2["Loaded"] = "paymentSheetLoaded";
  PaymentSheetEventsEnum2["FailedToLoad"] = "paymentSheetFailedToLoad";
  PaymentSheetEventsEnum2["Completed"] = "paymentSheetCompleted";
  PaymentSheetEventsEnum2["Canceled"] = "paymentSheetCanceled";
  PaymentSheetEventsEnum2["Failed"] = "paymentSheetFailed";
})(PaymentSheetEventsEnum || (PaymentSheetEventsEnum = {}));

export {
  ApplePayEventsEnum,
  GooglePayEventsEnum,
  PaymentFlowEventsEnum,
  PaymentSheetEventsEnum
};
//# sourceMappingURL=chunk-Q6Z3JMON.js.map
