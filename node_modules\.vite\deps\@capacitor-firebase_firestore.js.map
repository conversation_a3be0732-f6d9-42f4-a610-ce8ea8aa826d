{"version": 3, "sources": ["../../@capacitor-firebase/firestore/src/index.ts"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { FirebaseFirestorePlugin } from './definitions';\n\nconst FirebaseFirestore = registerPlugin<FirebaseFirestorePlugin>(\n  'FirebaseFirestore',\n  {\n    web: () => import('./web').then(m => new m.FirebaseFirestoreWeb()),\n  },\n);\n\nexport * from './definitions';\nexport { FirebaseFirestore };\n"], "mappings": ";;;;;;AAIA,IAAM,oBAAoB,eACxB,qBACA;EACE,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,qBAAoB,CAAE;CAClE;", "names": []}