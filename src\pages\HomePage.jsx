import React from 'react'
import { HealthNeedCard } from '../components/HealthNeedCard'
import { healthNeeds } from '../data/healthNeeds'

export const HomePage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-electric-orange-500 via-soft-magenta-500 to-cobalt-blue-500 py-20">
        <div className="absolute inset-0 bg-black/10" />
        <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-black text-white mb-6 animate-fade-in">
            ¿Qué te gustaría
            <br />
            <span className="text-lime-green-400">mejorar</span> en tu
            <br />
            salud hoy?
          </h1>
          <p className="text-xl md:text-2xl text-white/90 font-medium max-w-3xl mx-auto animate-slide-up">
            Descubre productos naturales que transformarán tu bienestar
          </p>
        </div>
        
        {/* Decorative Elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full animate-pulse-soft" />
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/5 rounded-full animate-bounce-gentle" />
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-lime-green-400/20 rounded-full animate-pulse-soft" />
      </section>

      {/* Health Needs Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Encuentra tu solución perfecta
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Selecciona la necesidad que más te identifique y descubre nuestros productos especializados
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {healthNeeds.map((healthNeed, index) => (
              <div 
                key={healthNeed.id}
                className="animate-enter-delayed"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <HealthNeedCard healthNeed={healthNeed} />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-16 bg-gradient-to-r from-cobalt-blue-500 to-electric-orange-500">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            ¿No encuentras lo que buscas?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Explora nuestro catálogo completo de productos naturales para tu bienestar
          </p>
          <button className="btn-success text-lg px-8 py-4">
            Ver todos los productos
          </button>
        </div>
      </section>
    </div>
  )
}
