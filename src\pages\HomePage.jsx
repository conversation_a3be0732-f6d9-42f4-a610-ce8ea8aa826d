import React from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowR<PERSON>, Spark<PERSON>, Heart, Shield } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent } from '../components/ui/Card'
import { HealthNeedCard } from '../components/HealthNeedCard'
import { healthNeeds } from '../data/healthNeeds'

export const HomePage = () => {
  const navigate = useNavigate()

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]" />
        <div className="relative">
          <div className="container px-4 py-24 sm:px-6 lg:px-8">
            <div className="mx-auto max-w-4xl text-center">
              <div className="mb-8 flex justify-center">
                <div className="relative rounded-full px-3 py-1 text-sm leading-6 text-gray-400 ring-1 ring-white/10 hover:ring-white/20">
                  Productos naturales para tu bienestar{' '}
                  <span className="font-semibold text-white">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Descubre más <span aria-hidden="true">&rarr;</span>
                  </span>
                </div>
              </div>

              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
                ¿Qué te gustaría{' '}
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  mejorar
                </span>{' '}
                en tu salud hoy?
              </h1>

              <p className="mt-6 text-lg leading-8 text-gray-300 max-w-2xl mx-auto">
                Descubre productos naturales que transformarán tu bienestar.
                Cada producto está diseñado para ayudarte a alcanzar tus objetivos de salud.
              </p>

              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button
                  variant="gradient"
                  size="lg"
                  onClick={() => navigate('/products')}
                  className="group"
                >
                  Explorar productos
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
                <Button variant="outline" size="lg">
                  Ver categorías
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Floating elements */}
        <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" />
        <div className="absolute top-1/3 right-1/4 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" style={{ animationDelay: '4s' }} />
      </section>

      {/* Features Section */}
      <section className="py-24 bg-background">
        <div className="container px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              ¿Por qué elegir FuXion?
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              Comprometidos con tu bienestar a través de productos naturales de la más alta calidad
            </p>
          </div>

          <div className="mx-auto mt-16 max-w-5xl">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <Card className="text-center">
                <CardContent className="pt-6">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-r from-purple-500 to-pink-500">
                    <Sparkles className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="mt-6 text-lg font-semibold">100% Natural</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Todos nuestros productos están elaborados con ingredientes naturales de la más alta calidad
                  </p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="pt-6">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500">
                    <Shield className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="mt-6 text-lg font-semibold">Calidad Garantizada</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Rigurosos controles de calidad y certificaciones internacionales respaldan nuestros productos
                  </p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="pt-6">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-r from-green-500 to-emerald-500">
                    <Heart className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="mt-6 text-lg font-semibold">Resultados Comprobados</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Miles de clientes satisfechos respaldan la efectividad de nuestros productos
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Health Needs Grid */}
      <section className="py-24 bg-muted/50">
        <div className="container px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Encuentra tu solución perfecta
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              Selecciona la necesidad que más te identifique y descubre nuestros productos especializados
            </p>
          </div>

          <div className="mx-auto mt-16 grid max-w-6xl grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {healthNeeds.map((healthNeed, index) => (
              <div
                key={healthNeed.id}
                className="animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <HealthNeedCard healthNeed={healthNeed} />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="container px-4 py-24 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              ¿Listo para transformar tu salud?
            </h2>
            <p className="mt-6 text-lg leading-8 text-purple-100">
              Únete a miles de personas que ya han mejorado su bienestar con FuXion
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button
                variant="secondary"
                size="lg"
                onClick={() => navigate('/products')}
              >
                Ver todos los productos
              </Button>
              <Button variant="outline" size="lg" className="text-white border-white hover:bg-white hover:text-purple-600">
                Contactar asesor
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
