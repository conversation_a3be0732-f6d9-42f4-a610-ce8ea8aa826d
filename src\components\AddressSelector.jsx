import React, { useState, useEffect } from 'react'
import { Plus, MapPin, Check } from 'lucide-react'
import { Button } from './ui/Button'
import { Card, CardContent } from './ui/Card'
import { Input } from './ui/Input'
import { useAuth } from '../context/AuthContext'
import { addressesService } from '../services/firestore'

export const AddressSelector = ({ selectedAddress, onAddressSelect, allowGuest = false }) => {
  const { user, isAuthenticated } = useAuth()
  const [addresses, setAddresses] = useState([])
  const [loading, setLoading] = useState(false)
  const [showNewAddressForm, setShowNewAddressForm] = useState(false)
  const [newAddress, setNewAddress] = useState({
    name: '',
    street: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'Perú'
  })

  useEffect(() => {
    if (isAuthenticated && user) {
      loadAddresses()
    }
  }, [isAuthenticated, user])

  const loadAddresses = async () => {
    try {
      setLoading(true)
      const data = await addressesService.getAll(user.uid)
      setAddresses(data)
      
      // Auto-select first address if none selected
      if (data.length > 0 && !selectedAddress) {
        onAddressSelect(data[0])
      }
    } catch (error) {
      console.error('Error loading addresses:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveNewAddress = async (e) => {
    e.preventDefault()
    try {
      if (isAuthenticated && user) {
        const addressId = await addressesService.create(user.uid, newAddress)
        const savedAddress = { id: addressId, ...newAddress }
        setAddresses([...addresses, savedAddress])
        onAddressSelect(savedAddress)
      } else {
        // For guest users, just use the address without saving
        const guestAddress = { id: 'guest', ...newAddress }
        onAddressSelect(guestAddress)
      }
      
      setShowNewAddressForm(false)
      setNewAddress({
        name: '',
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'Perú'
      })
    } catch (error) {
      console.error('Error saving address:', error)
    }
  }

  if (!isAuthenticated && !allowGuest) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Inicia sesión para usar direcciones guardadas
          </h3>
          <p className="text-gray-600 mb-4">
            O continúa como invitado e ingresa tu dirección
          </p>
          <Button onClick={() => setShowNewAddressForm(true)}>
            Ingresar dirección
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Saved Addresses */}
      {isAuthenticated && addresses.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900">
            Direcciones guardadas
          </h3>
          {addresses.map((address) => (
            <Card 
              key={address.id}
              className={`cursor-pointer transition-all ${
                selectedAddress?.id === address.id 
                  ? 'ring-2 ring-electric-orange-500 bg-electric-orange-50' 
                  : 'hover:shadow-md'
              }`}
              onClick={() => onAddressSelect(address)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">
                      {address.name}
                    </h4>
                    <p className="text-gray-600 text-sm mt-1">
                      {address.street}
                    </p>
                    <p className="text-gray-600 text-sm">
                      {address.city}, {address.state} {address.zipCode}
                    </p>
                    <p className="text-gray-600 text-sm">
                      {address.country}
                    </p>
                  </div>
                  {selectedAddress?.id === address.id && (
                    <Check className="h-5 w-5 text-electric-orange-600" />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* New Address Form */}
      {showNewAddressForm ? (
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {isAuthenticated ? 'Nueva dirección' : 'Dirección de envío'}
            </h3>
            <form onSubmit={handleSaveNewAddress} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  placeholder="Nombre completo"
                  value={newAddress.name}
                  onChange={(e) => setNewAddress({...newAddress, name: e.target.value})}
                  required
                />
                <Input
                  placeholder="Dirección completa"
                  value={newAddress.street}
                  onChange={(e) => setNewAddress({...newAddress, street: e.target.value})}
                  required
                />
                <Input
                  placeholder="Ciudad"
                  value={newAddress.city}
                  onChange={(e) => setNewAddress({...newAddress, city: e.target.value})}
                  required
                />
                <Input
                  placeholder="Departamento/Estado"
                  value={newAddress.state}
                  onChange={(e) => setNewAddress({...newAddress, state: e.target.value})}
                  required
                />
                <Input
                  placeholder="Código postal"
                  value={newAddress.zipCode}
                  onChange={(e) => setNewAddress({...newAddress, zipCode: e.target.value})}
                  required
                />
                <Input
                  placeholder="País"
                  value={newAddress.country}
                  onChange={(e) => setNewAddress({...newAddress, country: e.target.value})}
                  required
                />
              </div>
              
              <div className="flex justify-end space-x-4">
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => setShowNewAddressForm(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit">
                  {isAuthenticated ? 'Guardar y usar' : 'Usar esta dirección'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      ) : (
        <Button 
          variant="outline" 
          onClick={() => setShowNewAddressForm(true)}
          className="w-full flex items-center justify-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>
            {isAuthenticated ? 'Agregar nueva dirección' : 'Ingresar dirección de envío'}
          </span>
        </Button>
      )}
    </div>
  )
}
