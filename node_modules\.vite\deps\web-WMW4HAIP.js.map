{"version": 3, "sources": ["../../@capacitor-firebase/firestore/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nimport type {\n  QueryCompositeFilterConstraint as FirebaseQueryCompositeFilterConstraint,\n  QueryConstraint as FirebaseQueryConstraint,\n  QueryFieldFilterConstraint as FirebaseQueryFieldFilterConstraint,\n  QueryFilterConstraint as FirebaseQueryFilterConstraint,\n  QueryNonFilterConstraint as FirebaseQueryNonFilterConstraint,\n  Query,\n  Unsubscribe,\n} from 'firebase/firestore';\nimport {\n  addDoc,\n  and,\n  clearIndexedDbPersistence,\n  collection,\n  collectionGroup,\n  connectFirestoreEmulator,\n  deleteDoc,\n  disableNetwork,\n  doc,\n  enableNetwork,\n  endAt,\n  endBefore,\n  getCountFromServer,\n  getDoc,\n  getDocs,\n  getFirestore,\n  limit,\n  limitToLast,\n  onSnapshot,\n  or,\n  orderBy,\n  query,\n  setDoc,\n  startAfter,\n  startAt,\n  updateDoc,\n  where,\n  writeBatch,\n} from 'firebase/firestore';\n\nimport type {\n  AddCollectionGroupSnapshotListenerCallback,\n  AddCollectionGroupSnapshotListenerOptions,\n  AddCollectionSnapshotListenerCallback,\n  AddCollectionSnapshotListenerCallbackEvent,\n  AddCollectionSnapshotListenerOptions,\n  AddDocumentOptions,\n  AddDocumentResult,\n  AddDocumentSnapshotListenerCallback,\n  AddDocumentSnapshotListenerCallbackEvent,\n  AddDocumentSnapshotListenerOptions,\n  DocumentData,\n  FirebaseFirestorePlugin,\n  GetCollectionGroupOptions,\n  GetCollectionGroupResult,\n  GetCollectionOptions,\n  GetCollectionResult,\n  GetCountFromServerOptions,\n  GetCountFromServerResult,\n  GetDocumentOptions,\n  GetDocumentResult,\n  QueryCompositeFilterConstraint,\n  QueryConstraint,\n  QueryFieldFilterConstraint,\n  QueryFilterConstraint,\n  QueryNonFilterConstraint,\n  RemoveSnapshotListenerOptions,\n  SetDocumentOptions,\n  UseEmulatorOptions,\n  WriteBatchOptions,\n} from './definitions';\n\nexport class FirebaseFirestoreWeb\n  extends WebPlugin\n  implements FirebaseFirestorePlugin\n{\n  private readonly unsubscribesMap: Map<string, Unsubscribe> = new Map();\n\n  public async addDocument(\n    options: AddDocumentOptions,\n  ): Promise<AddDocumentResult> {\n    const firestore = getFirestore();\n    const { reference, data } = options;\n    const documentReference = await addDoc<DocumentData, DocumentData>(\n      collection(firestore, reference),\n      data,\n    );\n    return {\n      reference: {\n        id: documentReference.id,\n        path: documentReference.path,\n      },\n    };\n  }\n\n  public async setDocument(options: SetDocumentOptions): Promise<void> {\n    const firestore = getFirestore();\n    const { reference, data, merge } = options;\n    await setDoc<DocumentData, DocumentData>(doc(firestore, reference), data, {\n      merge,\n    });\n  }\n\n  public async getDocument<T extends DocumentData>(\n    options: GetDocumentOptions,\n  ): Promise<GetDocumentResult<T>> {\n    const firestore = getFirestore();\n    const { reference } = options;\n    const documentSnapshot = await getDoc(doc(firestore, reference));\n    const documentSnapshotData = documentSnapshot.data();\n    return {\n      snapshot: {\n        id: documentSnapshot.id,\n        path: documentSnapshot.ref.path,\n        data: (documentSnapshotData === undefined\n          ? null\n          : documentSnapshotData) as T | null,\n        metadata: {\n          hasPendingWrites: documentSnapshot.metadata.hasPendingWrites,\n          fromCache: documentSnapshot.metadata.fromCache,\n        },\n      },\n    };\n  }\n\n  public async updateDocument(options: SetDocumentOptions): Promise<void> {\n    const firestore = getFirestore();\n    const { reference, data } = options;\n    await updateDoc<DocumentData, DocumentData>(\n      doc(firestore, reference),\n      data,\n    );\n  }\n\n  public async deleteDocument(options: SetDocumentOptions): Promise<void> {\n    const firestore = getFirestore();\n    const { reference } = options;\n    await deleteDoc(doc(firestore, reference));\n  }\n\n  public async writeBatch(options: WriteBatchOptions): Promise<void> {\n    const firestore = getFirestore();\n    const { operations } = options;\n    const batch = writeBatch(firestore);\n    for (const operation of operations) {\n      const { type, reference, data } = operation;\n      const documentReference = doc(firestore, reference);\n      switch (type) {\n        case 'set':\n          batch.set(documentReference, data);\n          break;\n        case 'update':\n          batch.update(documentReference, data ?? {});\n          break;\n        case 'delete':\n          batch.delete(documentReference);\n          break;\n      }\n    }\n    await batch.commit();\n  }\n\n  public async getCollection<T extends DocumentData>(\n    options: GetCollectionOptions,\n  ): Promise<GetCollectionResult<T>> {\n    const collectionQuery = await this.buildCollectionQuery(\n      options,\n      'collection',\n    );\n    const collectionSnapshot = await getDocs(collectionQuery);\n    return {\n      snapshots: collectionSnapshot.docs.map(documentSnapshot => ({\n        id: documentSnapshot.id,\n        path: documentSnapshot.ref.path,\n        data: documentSnapshot.data() as T,\n        metadata: {\n          hasPendingWrites: documentSnapshot.metadata.hasPendingWrites,\n          fromCache: documentSnapshot.metadata.fromCache,\n        },\n      })),\n    };\n  }\n\n  public async getCollectionGroup<T extends DocumentData>(\n    options: GetCollectionGroupOptions,\n  ): Promise<GetCollectionGroupResult<T>> {\n    const collectionQuery = await this.buildCollectionQuery(\n      options,\n      'collectionGroup',\n    );\n    const collectionSnapshot = await getDocs(collectionQuery);\n    return {\n      snapshots: collectionSnapshot.docs.map(documentSnapshot => ({\n        id: documentSnapshot.id,\n        path: documentSnapshot.ref.path,\n        data: documentSnapshot.data() as T,\n        metadata: {\n          hasPendingWrites: documentSnapshot.metadata.hasPendingWrites,\n          fromCache: documentSnapshot.metadata.fromCache,\n        },\n      })),\n    };\n  }\n\n  public async getCountFromServer(\n    options: GetCountFromServerOptions,\n  ): Promise<GetCountFromServerResult> {\n    const firestore = getFirestore();\n    const { reference } = options;\n    const coll = collection(firestore, reference);\n    const snapshot = await getCountFromServer(coll);\n    return { count: snapshot.data().count };\n  }\n\n  public async clearPersistence(): Promise<void> {\n    const firestore = getFirestore();\n    await clearIndexedDbPersistence(firestore);\n  }\n\n  public async enableNetwork(): Promise<void> {\n    const firestore = getFirestore();\n    await enableNetwork(firestore);\n  }\n\n  public async disableNetwork(): Promise<void> {\n    const firestore = getFirestore();\n    await disableNetwork(firestore);\n  }\n\n  public async useEmulator(options: UseEmulatorOptions): Promise<void> {\n    const firestore = getFirestore();\n    const port = options.port || 8080;\n    connectFirestoreEmulator(firestore, options.host, port);\n  }\n\n  public async addDocumentSnapshotListener<\n    T extends DocumentData = DocumentData,\n  >(\n    options: AddDocumentSnapshotListenerOptions,\n    callback: AddDocumentSnapshotListenerCallback<T>,\n  ): Promise<string> {\n    const firestore = getFirestore();\n    const unsubscribe = onSnapshot(\n      doc(firestore, options.reference),\n      {\n        includeMetadataChanges: options.includeMetadataChanges,\n        source: options.source,\n      },\n      snapshot => {\n        const data = snapshot.data();\n        const event: AddDocumentSnapshotListenerCallbackEvent<T> = {\n          snapshot: {\n            id: snapshot.id,\n            path: snapshot.ref.path,\n            data: (data === undefined ? null : data) as T | null,\n            metadata: {\n              hasPendingWrites: snapshot.metadata.hasPendingWrites,\n              fromCache: snapshot.metadata.fromCache,\n            },\n          },\n        };\n        callback(event, undefined);\n      },\n      error => callback(null, error),\n    );\n    const id = Date.now().toString();\n    this.unsubscribesMap.set(id, unsubscribe);\n    return id;\n  }\n\n  public async addCollectionSnapshotListener<\n    T extends DocumentData = DocumentData,\n  >(\n    options: AddCollectionSnapshotListenerOptions,\n    callback: AddCollectionSnapshotListenerCallback<T>,\n  ): Promise<string> {\n    const collectionQuery = await this.buildCollectionQuery(\n      options,\n      'collection',\n    );\n    const unsubscribe = onSnapshot(\n      collectionQuery,\n      {\n        includeMetadataChanges: options.includeMetadataChanges,\n        source: options.source,\n      },\n      snapshot => {\n        const event: AddCollectionSnapshotListenerCallbackEvent<T> = {\n          snapshots: snapshot.docs.map(documentSnapshot => ({\n            id: documentSnapshot.id,\n            path: documentSnapshot.ref.path,\n            data: documentSnapshot.data() as T,\n            metadata: {\n              hasPendingWrites: documentSnapshot.metadata.hasPendingWrites,\n              fromCache: documentSnapshot.metadata.fromCache,\n            },\n          })),\n        };\n        callback(event, undefined);\n      },\n      error => callback(null, error),\n    );\n    const id = Date.now().toString();\n    this.unsubscribesMap.set(id, unsubscribe);\n    return id;\n  }\n\n  public async addCollectionGroupSnapshotListener<\n    T extends DocumentData = DocumentData,\n  >(\n    options: AddCollectionGroupSnapshotListenerOptions,\n    callback: AddCollectionGroupSnapshotListenerCallback<T>,\n  ): Promise<string> {\n    const collectionQuery = await this.buildCollectionQuery(\n      options,\n      'collectionGroup',\n    );\n    const unsubscribe = onSnapshot(\n      collectionQuery,\n      {\n        includeMetadataChanges: options.includeMetadataChanges,\n        source: options.source,\n      },\n      snapshot => {\n        const event: AddCollectionSnapshotListenerCallbackEvent<T> = {\n          snapshots: snapshot.docs.map(documentSnapshot => ({\n            id: documentSnapshot.id,\n            path: documentSnapshot.ref.path,\n            data: documentSnapshot.data() as T,\n            metadata: {\n              hasPendingWrites: documentSnapshot.metadata.hasPendingWrites,\n              fromCache: documentSnapshot.metadata.fromCache,\n            },\n          })),\n        };\n        callback(event, undefined);\n      },\n      error => callback(null, error),\n    );\n    const id = Date.now().toString();\n    this.unsubscribesMap.set(id, unsubscribe);\n    return id;\n  }\n\n  public async removeSnapshotListener(\n    options: RemoveSnapshotListenerOptions,\n  ): Promise<void> {\n    const unsubscribe = this.unsubscribesMap.get(options.callbackId);\n\n    if (!unsubscribe) {\n      return;\n    }\n\n    unsubscribe();\n    this.unsubscribesMap.delete(options.callbackId);\n  }\n\n  public async removeAllListeners(): Promise<void> {\n    this.unsubscribesMap.forEach(unsubscribe => unsubscribe());\n    this.unsubscribesMap.clear();\n    await super.removeAllListeners();\n  }\n\n  private async buildCollectionQuery(\n    options:\n      | GetCollectionOptions\n      | GetCollectionGroupOptions\n      | AddCollectionSnapshotListenerOptions,\n    type: 'collection' | 'collectionGroup',\n  ): Promise<Query<DocumentData, DocumentData>> {\n    const firestore = getFirestore();\n    let collectionQuery: Query;\n    if (options.compositeFilter) {\n      const compositeFilter = this.buildFirebaseQueryCompositeFilterConstraint(\n        options.compositeFilter,\n      );\n      const queryConstraints =\n        await this.buildFirebaseQueryNonFilterConstraints(\n          options.queryConstraints || [],\n        );\n      collectionQuery = query(\n        type === 'collection'\n          ? collection(firestore, options.reference)\n          : collectionGroup(firestore, options.reference),\n        compositeFilter,\n        ...queryConstraints,\n      );\n    } else {\n      const queryConstraints = await this.buildFirebaseQueryConstraints(\n        options.queryConstraints || [],\n      );\n      collectionQuery = query(\n        type === 'collection'\n          ? collection(firestore, options.reference)\n          : collectionGroup(firestore, options.reference),\n        ...queryConstraints,\n      );\n    }\n    return collectionQuery;\n  }\n\n  private buildFirebaseQueryCompositeFilterConstraint(\n    compositeFilter: QueryCompositeFilterConstraint,\n  ): FirebaseQueryCompositeFilterConstraint {\n    const queryConstraints = this.buildFirebaseQueryFilterConstraints(\n      compositeFilter.queryConstraints,\n    );\n    if (compositeFilter.type === 'and') {\n      return and(...queryConstraints);\n    } else {\n      return or(...queryConstraints);\n    }\n  }\n\n  private buildFirebaseQueryFilterConstraints(\n    queryfilterConstraints: QueryFilterConstraint[],\n  ): FirebaseQueryFilterConstraint[] {\n    const firebaseQueryFilterConstraints: FirebaseQueryFilterConstraint[] = [];\n    for (const queryfilterConstraint of queryfilterConstraints) {\n      const firebaseQueryFilterConstraint =\n        this.buildFirebaseQueryFilterConstraint(queryfilterConstraint);\n      firebaseQueryFilterConstraints.push(firebaseQueryFilterConstraint);\n    }\n    return firebaseQueryFilterConstraints;\n  }\n\n  private buildFirebaseQueryFilterConstraint(\n    queryFilterConstraints: QueryFilterConstraint,\n  ): FirebaseQueryFilterConstraint {\n    if (queryFilterConstraints.type === 'where') {\n      return this.buildFirebaseQueryFieldFilterConstraint(\n        queryFilterConstraints,\n      );\n    } else {\n      return this.buildFirebaseQueryCompositeFilterConstraint(\n        queryFilterConstraints,\n      );\n    }\n  }\n\n  private buildFirebaseQueryFieldFilterConstraint(\n    queryfilterConstraints: QueryFieldFilterConstraint,\n  ): FirebaseQueryFieldFilterConstraint {\n    return where(\n      queryfilterConstraints.fieldPath,\n      queryfilterConstraints.opStr,\n      queryfilterConstraints.value,\n    );\n  }\n\n  private async buildFirebaseQueryNonFilterConstraints(\n    queryConstraints: QueryNonFilterConstraint[],\n  ): Promise<FirebaseQueryNonFilterConstraint[]> {\n    const firebaseQueryNonFilterConstraints: FirebaseQueryNonFilterConstraint[] =\n      [];\n    for (const queryConstraint of queryConstraints) {\n      const firebaseQueryNonFilterConstraint =\n        await this.buildFirebaseQueryNonFilterConstraint(queryConstraint);\n      firebaseQueryNonFilterConstraints.push(firebaseQueryNonFilterConstraint);\n    }\n    return firebaseQueryNonFilterConstraints;\n  }\n\n  private async buildFirebaseQueryNonFilterConstraint(\n    queryConstraints: QueryNonFilterConstraint,\n  ): Promise<FirebaseQueryNonFilterConstraint> {\n    switch (queryConstraints.type) {\n      case 'orderBy':\n        return orderBy(\n          queryConstraints.fieldPath,\n          queryConstraints.directionStr,\n        );\n      case 'limit':\n        return limit(queryConstraints.limit);\n      case 'limitToLast':\n        return limitToLast(queryConstraints.limit);\n      case 'startAt':\n      case 'startAfter':\n      case 'endAt':\n      case 'endBefore': {\n        const firestore = getFirestore();\n        const documentSnapshot = await getDoc(\n          doc(firestore, queryConstraints.reference),\n        );\n        switch (queryConstraints.type) {\n          case 'startAt':\n            return startAt(documentSnapshot);\n          case 'startAfter':\n            return startAfter(documentSnapshot);\n          case 'endAt':\n            return endAt(documentSnapshot);\n          case 'endBefore':\n            return endBefore(documentSnapshot);\n        }\n      }\n    }\n  }\n\n  private async buildFirebaseQueryConstraints(\n    queryConstraints: QueryConstraint[],\n  ): Promise<FirebaseQueryConstraint[]> {\n    const firebaseQueryConstraints: FirebaseQueryConstraint[] = [];\n    for (const queryConstraint of queryConstraints) {\n      const firebaseQueryConstraint =\n        await this.buildFirebaseQueryConstraint(queryConstraint);\n      firebaseQueryConstraints.push(firebaseQueryConstraint);\n    }\n    return firebaseQueryConstraints;\n  }\n\n  private async buildFirebaseQueryConstraint(\n    queryConstraint: QueryConstraint,\n  ): Promise<FirebaseQueryConstraint> {\n    if (queryConstraint.type === 'where') {\n      return this.buildFirebaseQueryFieldFilterConstraint(queryConstraint);\n    } else {\n      return await this.buildFirebaseQueryNonFilterConstraint(queryConstraint);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEM,IAAO,uBAAP,cACI,UAAS;EADnB,cAAA;;AAImB,SAAA,kBAA4C,oBAAI,IAAG;EA2btE;EAzbS,MAAM,YACX,SAA2B;AAE3B,UAAM,YAAY,aAAY;AAC9B,UAAM,EAAE,WAAW,KAAI,IAAK;AAC5B,UAAM,oBAAoB,MAAM,OAC9B,WAAW,WAAW,SAAS,GAC/B,IAAI;AAEN,WAAO;MACL,WAAW;QACT,IAAI,kBAAkB;QACtB,MAAM,kBAAkB;;;EAG9B;EAEO,MAAM,YAAY,SAA2B;AAClD,UAAM,YAAY,aAAY;AAC9B,UAAM,EAAE,WAAW,MAAM,MAAK,IAAK;AACnC,UAAM,OAAmC,IAAI,WAAW,SAAS,GAAG,MAAM;MACxE;KACD;EACH;EAEO,MAAM,YACX,SAA2B;AAE3B,UAAM,YAAY,aAAY;AAC9B,UAAM,EAAE,UAAS,IAAK;AACtB,UAAM,mBAAmB,MAAM,OAAO,IAAI,WAAW,SAAS,CAAC;AAC/D,UAAM,uBAAuB,iBAAiB,KAAI;AAClD,WAAO;MACL,UAAU;QACR,IAAI,iBAAiB;QACrB,MAAM,iBAAiB,IAAI;QAC3B,MAAO,yBAAyB,SAC5B,OACA;QACJ,UAAU;UACR,kBAAkB,iBAAiB,SAAS;UAC5C,WAAW,iBAAiB,SAAS;;;;EAI7C;EAEO,MAAM,eAAe,SAA2B;AACrD,UAAM,YAAY,aAAY;AAC9B,UAAM,EAAE,WAAW,KAAI,IAAK;AAC5B,UAAM,UACJ,IAAI,WAAW,SAAS,GACxB,IAAI;EAER;EAEO,MAAM,eAAe,SAA2B;AACrD,UAAM,YAAY,aAAY;AAC9B,UAAM,EAAE,UAAS,IAAK;AACtB,UAAM,UAAU,IAAI,WAAW,SAAS,CAAC;EAC3C;EAEO,MAAM,WAAW,SAA0B;AAChD,UAAM,YAAY,aAAY;AAC9B,UAAM,EAAE,WAAU,IAAK;AACvB,UAAM,QAAQ,WAAW,SAAS;AAClC,eAAW,aAAa,YAAY;AAClC,YAAM,EAAE,MAAM,WAAW,KAAI,IAAK;AAClC,YAAM,oBAAoB,IAAI,WAAW,SAAS;AAClD,cAAQ,MAAM;QACZ,KAAK;AACH,gBAAM,IAAI,mBAAmB,IAAI;AACjC;QACF,KAAK;AACH,gBAAM,OAAO,mBAAmB,SAAI,QAAJ,SAAI,SAAJ,OAAQ,CAAA,CAAE;AAC1C;QACF,KAAK;AACH,gBAAM,OAAO,iBAAiB;AAC9B;;;AAGN,UAAM,MAAM,OAAM;EACpB;EAEO,MAAM,cACX,SAA6B;AAE7B,UAAM,kBAAkB,MAAM,KAAK,qBACjC,SACA,YAAY;AAEd,UAAM,qBAAqB,MAAM,QAAQ,eAAe;AACxD,WAAO;MACL,WAAW,mBAAmB,KAAK,IAAI,uBAAqB;QAC1D,IAAI,iBAAiB;QACrB,MAAM,iBAAiB,IAAI;QAC3B,MAAM,iBAAiB,KAAI;QAC3B,UAAU;UACR,kBAAkB,iBAAiB,SAAS;UAC5C,WAAW,iBAAiB,SAAS;;QAEvC;;EAEN;EAEO,MAAM,mBACX,SAAkC;AAElC,UAAM,kBAAkB,MAAM,KAAK,qBACjC,SACA,iBAAiB;AAEnB,UAAM,qBAAqB,MAAM,QAAQ,eAAe;AACxD,WAAO;MACL,WAAW,mBAAmB,KAAK,IAAI,uBAAqB;QAC1D,IAAI,iBAAiB;QACrB,MAAM,iBAAiB,IAAI;QAC3B,MAAM,iBAAiB,KAAI;QAC3B,UAAU;UACR,kBAAkB,iBAAiB,SAAS;UAC5C,WAAW,iBAAiB,SAAS;;QAEvC;;EAEN;EAEO,MAAM,mBACX,SAAkC;AAElC,UAAM,YAAY,aAAY;AAC9B,UAAM,EAAE,UAAS,IAAK;AACtB,UAAM,OAAO,WAAW,WAAW,SAAS;AAC5C,UAAM,WAAW,MAAM,mBAAmB,IAAI;AAC9C,WAAO,EAAE,OAAO,SAAS,KAAI,EAAG,MAAK;EACvC;EAEO,MAAM,mBAAgB;AAC3B,UAAM,YAAY,aAAY;AAC9B,UAAM,0BAA0B,SAAS;EAC3C;EAEO,MAAM,gBAAa;AACxB,UAAM,YAAY,aAAY;AAC9B,UAAM,cAAc,SAAS;EAC/B;EAEO,MAAM,iBAAc;AACzB,UAAM,YAAY,aAAY;AAC9B,UAAM,eAAe,SAAS;EAChC;EAEO,MAAM,YAAY,SAA2B;AAClD,UAAM,YAAY,aAAY;AAC9B,UAAM,OAAO,QAAQ,QAAQ;AAC7B,6BAAyB,WAAW,QAAQ,MAAM,IAAI;EACxD;EAEO,MAAM,4BAGX,SACA,UAAgD;AAEhD,UAAM,YAAY,aAAY;AAC9B,UAAM,cAAc,WAClB,IAAI,WAAW,QAAQ,SAAS,GAChC;MACE,wBAAwB,QAAQ;MAChC,QAAQ,QAAQ;OAElB,cAAW;AACT,YAAM,OAAO,SAAS,KAAI;AAC1B,YAAM,QAAqD;QACzD,UAAU;UACR,IAAI,SAAS;UACb,MAAM,SAAS,IAAI;UACnB,MAAO,SAAS,SAAY,OAAO;UACnC,UAAU;YACR,kBAAkB,SAAS,SAAS;YACpC,WAAW,SAAS,SAAS;;;;AAInC,eAAS,OAAO,MAAS;IAC3B,GACA,WAAS,SAAS,MAAM,KAAK,CAAC;AAEhC,UAAM,KAAK,KAAK,IAAG,EAAG,SAAQ;AAC9B,SAAK,gBAAgB,IAAI,IAAI,WAAW;AACxC,WAAO;EACT;EAEO,MAAM,8BAGX,SACA,UAAkD;AAElD,UAAM,kBAAkB,MAAM,KAAK,qBACjC,SACA,YAAY;AAEd,UAAM,cAAc,WAClB,iBACA;MACE,wBAAwB,QAAQ;MAChC,QAAQ,QAAQ;OAElB,cAAW;AACT,YAAM,QAAuD;QAC3D,WAAW,SAAS,KAAK,IAAI,uBAAqB;UAChD,IAAI,iBAAiB;UACrB,MAAM,iBAAiB,IAAI;UAC3B,MAAM,iBAAiB,KAAI;UAC3B,UAAU;YACR,kBAAkB,iBAAiB,SAAS;YAC5C,WAAW,iBAAiB,SAAS;;UAEvC;;AAEJ,eAAS,OAAO,MAAS;IAC3B,GACA,WAAS,SAAS,MAAM,KAAK,CAAC;AAEhC,UAAM,KAAK,KAAK,IAAG,EAAG,SAAQ;AAC9B,SAAK,gBAAgB,IAAI,IAAI,WAAW;AACxC,WAAO;EACT;EAEO,MAAM,mCAGX,SACA,UAAuD;AAEvD,UAAM,kBAAkB,MAAM,KAAK,qBACjC,SACA,iBAAiB;AAEnB,UAAM,cAAc,WAClB,iBACA;MACE,wBAAwB,QAAQ;MAChC,QAAQ,QAAQ;OAElB,cAAW;AACT,YAAM,QAAuD;QAC3D,WAAW,SAAS,KAAK,IAAI,uBAAqB;UAChD,IAAI,iBAAiB;UACrB,MAAM,iBAAiB,IAAI;UAC3B,MAAM,iBAAiB,KAAI;UAC3B,UAAU;YACR,kBAAkB,iBAAiB,SAAS;YAC5C,WAAW,iBAAiB,SAAS;;UAEvC;;AAEJ,eAAS,OAAO,MAAS;IAC3B,GACA,WAAS,SAAS,MAAM,KAAK,CAAC;AAEhC,UAAM,KAAK,KAAK,IAAG,EAAG,SAAQ;AAC9B,SAAK,gBAAgB,IAAI,IAAI,WAAW;AACxC,WAAO;EACT;EAEO,MAAM,uBACX,SAAsC;AAEtC,UAAM,cAAc,KAAK,gBAAgB,IAAI,QAAQ,UAAU;AAE/D,QAAI,CAAC,aAAa;AAChB;;AAGF,gBAAW;AACX,SAAK,gBAAgB,OAAO,QAAQ,UAAU;EAChD;EAEO,MAAM,qBAAkB;AAC7B,SAAK,gBAAgB,QAAQ,iBAAe,YAAW,CAAE;AACzD,SAAK,gBAAgB,MAAK;AAC1B,UAAM,MAAM,mBAAkB;EAChC;EAEQ,MAAM,qBACZ,SAIA,MAAsC;AAEtC,UAAM,YAAY,aAAY;AAC9B,QAAI;AACJ,QAAI,QAAQ,iBAAiB;AAC3B,YAAM,kBAAkB,KAAK,4CAC3B,QAAQ,eAAe;AAEzB,YAAM,mBACJ,MAAM,KAAK,uCACT,QAAQ,oBAAoB,CAAA,CAAE;AAElC,wBAAkB,MAChB,SAAS,eACL,WAAW,WAAW,QAAQ,SAAS,IACvC,gBAAgB,WAAW,QAAQ,SAAS,GAChD,iBACA,GAAG,gBAAgB;WAEhB;AACL,YAAM,mBAAmB,MAAM,KAAK,8BAClC,QAAQ,oBAAoB,CAAA,CAAE;AAEhC,wBAAkB,MAChB,SAAS,eACL,WAAW,WAAW,QAAQ,SAAS,IACvC,gBAAgB,WAAW,QAAQ,SAAS,GAChD,GAAG,gBAAgB;;AAGvB,WAAO;EACT;EAEQ,4CACN,iBAA+C;AAE/C,UAAM,mBAAmB,KAAK,oCAC5B,gBAAgB,gBAAgB;AAElC,QAAI,gBAAgB,SAAS,OAAO;AAClC,aAAO,IAAI,GAAG,gBAAgB;WACzB;AACL,aAAO,GAAG,GAAG,gBAAgB;;EAEjC;EAEQ,oCACN,wBAA+C;AAE/C,UAAM,iCAAkE,CAAA;AACxE,eAAW,yBAAyB,wBAAwB;AAC1D,YAAM,gCACJ,KAAK,mCAAmC,qBAAqB;AAC/D,qCAA+B,KAAK,6BAA6B;;AAEnE,WAAO;EACT;EAEQ,mCACN,wBAA6C;AAE7C,QAAI,uBAAuB,SAAS,SAAS;AAC3C,aAAO,KAAK,wCACV,sBAAsB;WAEnB;AACL,aAAO,KAAK,4CACV,sBAAsB;;EAG5B;EAEQ,wCACN,wBAAkD;AAElD,WAAO,MACL,uBAAuB,WACvB,uBAAuB,OACvB,uBAAuB,KAAK;EAEhC;EAEQ,MAAM,uCACZ,kBAA4C;AAE5C,UAAM,oCACJ,CAAA;AACF,eAAW,mBAAmB,kBAAkB;AAC9C,YAAM,mCACJ,MAAM,KAAK,sCAAsC,eAAe;AAClE,wCAAkC,KAAK,gCAAgC;;AAEzE,WAAO;EACT;EAEQ,MAAM,sCACZ,kBAA0C;AAE1C,YAAQ,iBAAiB,MAAM;MAC7B,KAAK;AACH,eAAO,QACL,iBAAiB,WACjB,iBAAiB,YAAY;MAEjC,KAAK;AACH,eAAO,MAAM,iBAAiB,KAAK;MACrC,KAAK;AACH,eAAO,YAAY,iBAAiB,KAAK;MAC3C,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK,aAAa;AAChB,cAAM,YAAY,aAAY;AAC9B,cAAM,mBAAmB,MAAM,OAC7B,IAAI,WAAW,iBAAiB,SAAS,CAAC;AAE5C,gBAAQ,iBAAiB,MAAM;UAC7B,KAAK;AACH,mBAAO,QAAQ,gBAAgB;UACjC,KAAK;AACH,mBAAO,WAAW,gBAAgB;UACpC,KAAK;AACH,mBAAO,MAAM,gBAAgB;UAC/B,KAAK;AACH,mBAAO,UAAU,gBAAgB;;;;EAI3C;EAEQ,MAAM,8BACZ,kBAAmC;AAEnC,UAAM,2BAAsD,CAAA;AAC5D,eAAW,mBAAmB,kBAAkB;AAC9C,YAAM,0BACJ,MAAM,KAAK,6BAA6B,eAAe;AACzD,+BAAyB,KAAK,uBAAuB;;AAEvD,WAAO;EACT;EAEQ,MAAM,6BACZ,iBAAgC;AAEhC,QAAI,gBAAgB,SAAS,SAAS;AACpC,aAAO,KAAK,wCAAwC,eAAe;WAC9D;AACL,aAAO,MAAM,KAAK,sCAAsC,eAAe;;EAE3E;;", "names": []}