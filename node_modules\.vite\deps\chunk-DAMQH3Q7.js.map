{"version": 3, "sources": ["../../@capacitor-firebase/authentication/src/definitions.ts"], "sourcesContent": ["/// <reference types=\"@capacitor/cli\" />\n\nimport type { PermissionState, PluginListenerHandle } from '@capacitor/core';\n\ndeclare module '@capacitor/cli' {\n  export interface PluginsConfig {\n    /**\n     * These configuration values are available:\n     *\n     * @since 0.1.0\n     */\n    FirebaseAuthentication?: {\n      /**\n       * Configure whether the plugin should skip the native authentication.\n       * Only needed if you want to use the Firebase JavaScript SDK.\n       * This configuration option has no effect on Firebase account linking.\n       *\n       * **Note that the plugin may behave differently across the platforms.**\n       *\n       * Only available for Android and iOS.\n       *\n       * @default false\n       * @example false\n       * @since 0.1.0\n       */\n      skipNativeAuth?: boolean;\n      /**\n       * Configure the providers that should be loaded by the plugin.\n       *\n       * Possible values: `[\"apple.com\", \"facebook.com\", \"gc.apple.com\", \"github.com\", \"google.com\", \"microsoft.com\", \"playgames.google.com\", \"twitter.com\", \"yahoo.com\", \"phone\"]`\n       *\n       * Only available for Android and iOS.\n       *\n       * @default []\n       * @example [\"apple.com\", \"facebook.com\"]\n       * @since 0.1.0\n       */\n      providers?: string[];\n    };\n  }\n}\n\nexport interface FirebaseAuthenticationPlugin {\n  /**\n   * Applies a verification code sent to the user by email.\n   *\n   * @since 0.2.2\n   */\n  applyActionCode(options: ApplyActionCodeOptions): Promise<void>;\n  /**\n   * Completes the password reset process.\n   *\n   * @since 0.2.2\n   */\n  confirmPasswordReset(options: ConfirmPasswordResetOptions): Promise<void>;\n  /**\n   * Finishes the phone number verification process.\n   *\n   * @since 5.0.0\n   */\n  confirmVerificationCode(\n    options: ConfirmVerificationCodeOptions,\n  ): Promise<SignInResult>;\n  /**\n   * Creates a new user account with email and password.\n   * If the new account was created, the user is signed in automatically.\n   *\n   * @since 0.2.2\n   */\n  createUserWithEmailAndPassword(\n    options: CreateUserWithEmailAndPasswordOptions,\n  ): Promise<SignInResult>;\n  /**\n   * Deletes and signs out the user.\n   *\n   * @since 1.3.0\n   */\n  deleteUser(): Promise<void>;\n  /**\n   * Fetches the sign-in methods for an email address.\n   *\n   * @since 6.0.0\n   * @deprecated Migrating off of this method is recommended as a security best-practice.\n   * Learn more in the Identity Platform documentation for [Email Enumeration Protection](https://cloud.google.com/identity-platform/docs/admin/email-enumeration-protection).\n   */\n  fetchSignInMethodsForEmail(\n    options: FetchSignInMethodsForEmailOptions,\n  ): Promise<FetchSignInMethodsForEmailResult>;\n  /**\n   * Fetches the currently signed-in user.\n   *\n   * @since 0.1.0\n   */\n  getCurrentUser(): Promise<GetCurrentUserResult>;\n  /**\n   * Returns the `SignInResult` if your app launched a web sign-in flow and the OS cleans up the app while in the background.\n   *\n   * Only available for Android.\n   *\n   * @since 6.0.0\n   */\n  getPendingAuthResult(): Promise<SignInResult>;\n  /**\n   * Fetches the Firebase Auth ID Token for the currently signed-in user.\n   *\n   * @since 0.1.0\n   */\n  getIdToken(options?: GetIdTokenOptions): Promise<GetIdTokenResult>;\n  /**\n   * Returns the `SignInResult` from the redirect-based sign-in flow.\n   *\n   * If sign-in was unsuccessful, fails with an error.\n   * If no redirect operation was called, returns a `SignInResult` with a null user.\n   *\n   * Only available for Web.\n   *\n   * @since 1.3.0\n   */\n  getRedirectResult(): Promise<SignInResult>;\n  /**\n   * Get the tenant id.\n   *\n   * @since 1.1.0\n   */\n  getTenantId(): Promise<GetTenantIdResult>;\n  /**\n   * Checks if an incoming link is a sign-in with email link suitable for `signInWithEmailLink`.\n   *\n   * @since 1.1.0\n   */\n  isSignInWithEmailLink(\n    options: IsSignInWithEmailLinkOptions,\n  ): Promise<IsSignInWithEmailLinkResult>;\n  /**\n   * Links the user account with Apple authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * @since 1.1.0\n   */\n  linkWithApple(options?: LinkWithOAuthOptions): Promise<LinkResult>;\n  /**\n   * Links the user account with Email authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * @since 1.1.0\n   */\n  linkWithEmailAndPassword(\n    options: LinkWithEmailAndPasswordOptions,\n  ): Promise<LinkResult>;\n  /**\n   * Links the user account with Email authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * @since 1.1.0\n   */\n  linkWithEmailLink(options: LinkWithEmailLinkOptions): Promise<LinkResult>;\n  /**\n   * Links the user account with Facebook authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * @since 1.1.0\n   */\n  linkWithFacebook(options?: LinkWithOAuthOptions): Promise<LinkResult>;\n  /**\n   * Links the user account with Game Center authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * Only available for iOS.\n   *\n   * @since 1.3.0\n   */\n  linkWithGameCenter(options?: LinkWithOAuthOptions): Promise<LinkResult>;\n  /**\n   * Links the user account with GitHub authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * @since 1.1.0\n   */\n  linkWithGithub(options?: LinkWithOAuthOptions): Promise<LinkResult>;\n  /**\n   * Links the user account with Google authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * @since 1.1.0\n   */\n  linkWithGoogle(options?: LinkWithOAuthOptions): Promise<LinkResult>;\n  /**\n   * Links the user account with Microsoft authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * @since 1.1.0\n   */\n  linkWithMicrosoft(options?: LinkWithOAuthOptions): Promise<LinkResult>;\n  /**\n   * Links the user account with an OpenID Connect provider.\n   *\n   * @since 6.1.0\n   */\n  linkWithOpenIdConnect(\n    options: LinkWithOpenIdConnectOptions,\n  ): Promise<LinkResult>;\n  /**\n   * Links the user account with Phone Number authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * Use the `phoneVerificationCompleted` listener to be notified when the verification is completed.\n   * Use the `phoneVerificationFailed` listener to be notified when the verification is failed.\n   * Use the `phoneCodeSent` listener to get the verification id.\n   *\n   * @since 1.1.0\n   */\n  linkWithPhoneNumber(options: LinkWithPhoneNumberOptions): Promise<void>;\n  /**\n   * Links the user account with Play Games authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * Only available for Android.\n   *\n   * @since 1.1.0\n   */\n  linkWithPlayGames(options?: LinkWithOAuthOptions): Promise<LinkResult>;\n  /**\n   * Links the user account with Twitter authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * @since 1.1.0\n   */\n  linkWithTwitter(options?: LinkWithOAuthOptions): Promise<LinkResult>;\n  /**\n   * Links the user account with Yahoo authentication provider.\n   *\n   * The user must be logged in on the native layer.\n   * The `skipNativeAuth` configuration option has no effect here.\n   *\n   * @since 1.1.0\n   */\n  linkWithYahoo(options?: LinkWithOAuthOptions): Promise<LinkResult>;\n  /**\n   * Reloads user account data, if signed in.\n   *\n   * @since 1.3.0\n   */\n  reload(): Promise<void>;\n  /**\n   * Revokes the given access token. Currently only supports Apple OAuth access tokens.\n   *\n   * @since 6.1.0\n   */\n  revokeAccessToken(options: RevokeAccessTokenOptions): Promise<void>;\n  /**\n   * Sends a verification email to the currently signed in user.\n   *\n   * @since 0.2.2\n   */\n  sendEmailVerification(options?: SendEmailVerificationOptions): Promise<void>;\n  /**\n   * Sends a password reset email.\n   *\n   * @since 0.2.2\n   */\n  sendPasswordResetEmail(options: SendPasswordResetEmailOptions): Promise<void>;\n  /**\n   * Sends a sign-in email link to the user with the specified email.\n   *\n   * To complete sign in with the email link, call `signInWithEmailLink` with the email address and the email link supplied in the email sent to the user.\n   *\n   * @since 1.1.0\n   */\n  sendSignInLinkToEmail(options: SendSignInLinkToEmailOptions): Promise<void>;\n  /**\n   * Sets the user-facing language code for auth operations.\n   *\n   * @since 0.1.0\n   */\n  setLanguageCode(options: SetLanguageCodeOptions): Promise<void>;\n  /**\n   * Sets the type of persistence for the currently saved auth session.\n   *\n   * Only available for Web.\n   *\n   * @since 5.2.0\n   */\n  setPersistence(options: SetPersistenceOptions): Promise<void>;\n  /**\n   * Sets the tenant id.\n   *\n   * @since 1.1.0\n   */\n  setTenantId(options: SetTenantIdOptions): Promise<void>;\n  /**\n   * Signs in as an anonymous user.\n   *\n   * @since 1.1.0\n   */\n  signInAnonymously(): Promise<SignInResult>;\n  /**\n   * Starts the Apple sign-in flow.\n   *\n   * @since 0.1.0\n   */\n  signInWithApple(options?: SignInWithOAuthOptions): Promise<SignInResult>;\n  /**\n   * Starts the Custom Token sign-in flow.\n   *\n   * This method cannot be used in combination with `skipNativeAuth` on Android and iOS.\n   * In this case you have to use the `signInWithCustomToken` interface of the Firebase JS SDK directly.\n   *\n   * @since 0.1.0\n   */\n  signInWithCustomToken(\n    options: SignInWithCustomTokenOptions,\n  ): Promise<SignInResult>;\n  /**\n   * Starts the sign-in flow using an email and password.\n   *\n   * @since 0.2.2\n   */\n  signInWithEmailAndPassword(\n    options: SignInWithEmailAndPasswordOptions,\n  ): Promise<SignInResult>;\n  /**\n   * Signs in using an email and sign-in email link.\n   *\n   * @since 1.1.0\n   */\n  signInWithEmailLink(\n    options: SignInWithEmailLinkOptions,\n  ): Promise<SignInResult>;\n  /**\n   * Starts the Facebook sign-in flow.\n   *\n   * @since 0.1.0\n   */\n  signInWithFacebook(\n    options?: SignInWithFacebookOptions,\n  ): Promise<SignInResult>;\n  /**\n   * Starts the Game Center sign-in flow.\n   *\n   * Only available for iOS.\n   *\n   * @since 1.3.0\n   */\n  signInWithGameCenter(\n    options?: SignInOptions | SignInWithOAuthOptions,\n  ): Promise<SignInResult>;\n  /**\n   * Starts the GitHub sign-in flow.\n   *\n   * @since 0.1.0\n   */\n  signInWithGithub(options?: SignInWithOAuthOptions): Promise<SignInResult>;\n  /**\n   * Starts the Google sign-in flow.\n   *\n   * @since 0.1.0\n   */\n  signInWithGoogle(options?: SignInWithGoogleOptions): Promise<SignInResult>;\n  /**\n   * Starts the Microsoft sign-in flow.\n   *\n   * @since 0.1.0\n   */\n  signInWithMicrosoft(options?: SignInWithOAuthOptions): Promise<SignInResult>;\n  /**\n   * Starts the OpenID Connect sign-in flow.\n   *\n   * @since 6.1.0\n   */\n  signInWithOpenIdConnect(\n    options: SignInWithOpenIdConnectOptions,\n  ): Promise<SignInResult>;\n  /**\n   * Starts the sign-in flow using a phone number.\n   *\n   * Use the `phoneVerificationCompleted` listener to be notified when the verification is completed.\n   * Use the `phoneVerificationFailed` listener to be notified when the verification is failed.\n   * Use the `phoneCodeSent` listener to get the verification id.\n   *\n   * Only available for Android and iOS.\n   *\n   * @since 0.1.0\n   */\n  signInWithPhoneNumber(options: SignInWithPhoneNumberOptions): Promise<void>;\n  /**\n   * Starts the Play Games sign-in flow.\n   *\n   * Only available for Android.\n   *\n   * @since 0.1.0\n   */\n  signInWithPlayGames(options?: SignInWithOAuthOptions): Promise<SignInResult>;\n  /**\n   * Starts the Twitter sign-in flow.\n   *\n   * @since 0.1.0\n   */\n  signInWithTwitter(options?: SignInWithOAuthOptions): Promise<SignInResult>;\n  /**\n   * Starts the Yahoo sign-in flow.\n   *\n   * @since 0.1.0\n   */\n  signInWithYahoo(options?: SignInWithOAuthOptions): Promise<SignInResult>;\n  /**\n   * Starts the sign-out flow.\n   *\n   * @since 0.1.0\n   */\n  signOut(): Promise<void>;\n  /**\n   * Unlinks a provider from a user account.\n   *\n   * @since 1.1.0\n   */\n  unlink(options: UnlinkOptions): Promise<UnlinkResult>;\n  /**\n   * Updates the email address of the currently signed in user.\n   *\n   * @since 0.1.0\n   */\n  updateEmail(options: UpdateEmailOptions): Promise<void>;\n  /**\n   * Updates the password of the currently signed in user.\n   *\n   * @since 0.1.0\n   */\n  updatePassword(options: UpdatePasswordOptions): Promise<void>;\n  /**\n   * Updates a user's profile data.\n   *\n   * @since 1.3.0\n   */\n  updateProfile(options: UpdateProfileOptions): Promise<void>;\n  /**\n   * Sets the user-facing language code to be the default app language.\n   *\n   * @since 0.1.0\n   */\n  useAppLanguage(): Promise<void>;\n  /**\n   * Instrument your app to talk to the Authentication emulator.\n   *\n   * @since 0.2.0\n   */\n  useEmulator(options: UseEmulatorOptions): Promise<void>;\n  /**\n   * Verifies the new email address before updating the email address of the currently signed in user.\n   *\n   * @since 6.3.0\n   */\n  verifyBeforeUpdateEmail(\n    options: VerifyBeforeUpdateEmailOptions,\n  ): Promise<void>;\n  /**\n   * Checks the current status of app tracking transparency.\n   *\n   * Only available on iOS.\n   *\n   * @since 7.2.0\n   */\n  checkAppTrackingTransparencyPermission(): Promise<CheckAppTrackingTransparencyPermissionResult>;\n  /**\n   * Opens the system dialog to authorize app tracking transparency.\n   *\n   * **Attention:** The user may have disabled the tracking request in the device settings, see [Apple's documentation](https://support.apple.com/guide/iphone/iph4f4cbd242/ios).\n   *\n   * Only available on iOS.\n   *\n   * @since 7.2.0\n   */\n  requestAppTrackingTransparencyPermission(): Promise<RequestAppTrackingTransparencyPermissionResult>;\n  /**\n   * Listen for the user's sign-in state changes.\n   *\n   * **Attention:** This listener is not triggered when the `skipNativeAuth` is used. Use the Firebase JavaScript SDK instead.\n   *\n   * @since 0.1.0\n   */\n  addListener(\n    eventName: 'authStateChange',\n    listenerFunc: AuthStateChangeListener,\n  ): Promise<PluginListenerHandle>;\n  /**\n   * Listen to ID token changes for the currently signed-in user.\n   *\n   * **Attention:** This listener is not triggered when the `skipNativeAuth` is used. Use the Firebase JavaScript SDK instead.\n   *\n   * @since 6.3.0\n   */\n  addListener(\n    eventName: 'idTokenChange',\n    listenerFunc: IdTokenChangeListener,\n  ): Promise<PluginListenerHandle>;\n  /**\n   * Listen for a completed phone verification.\n   *\n   * This listener only fires in two situations:\n   * 1. **Instant verification**: In some cases the phone number can be instantly\n   * verified without needing to send or enter a verification code.\n   * 2. **Auto-retrieval**: On some devices Google Play services can automatically\n   * detect the incoming verification SMS and perform verification without\n   * user action.\n   *\n   * Only available for Android.\n   *\n   * @since 1.3.0\n   */\n  addListener(\n    eventName: 'phoneVerificationCompleted',\n    listenerFunc: PhoneVerificationCompletedListener,\n  ): Promise<PluginListenerHandle>;\n  /**\n   * Listen for a failed phone verification.\n   *\n   * @since 1.3.0\n   */\n  addListener(\n    eventName: 'phoneVerificationFailed',\n    listenerFunc: PhoneVerificationFailedListener,\n  ): Promise<PluginListenerHandle>;\n  /**\n   * Listen for a phone verification code.\n   *\n   * @since 1.3.0\n   */\n  addListener(\n    eventName: 'phoneCodeSent',\n    listenerFunc: PhoneCodeSentListener,\n  ): Promise<PluginListenerHandle>;\n  /**\n   * Remove all listeners for this plugin.\n   *\n   * @since 0.1.0\n   */\n  removeAllListeners(): Promise<void>;\n}\n\n/**\n * @since 0.2.2\n */\nexport interface ApplyActionCodeOptions {\n  /**\n   * A verification code sent to the user.\n   *\n   * @since 0.2.2\n   */\n  oobCode: string;\n}\n\n/**\n * @since 0.2.2\n */\nexport interface ConfirmPasswordResetOptions {\n  /**\n   * A verification code sent to the user.\n   *\n   * @since 0.2.2\n   */\n  oobCode: string;\n  /**\n   * The new password.\n   *\n   * @since 0.2.2\n   */\n  newPassword: string;\n}\n\n/**\n * @since 5.0.0\n */\nexport interface ConfirmVerificationCodeOptions {\n  /**\n   * The verification ID received from the `phoneCodeSent` listener.\n   *\n   * The `verificationCode` option must also be provided.\n   *\n   * @since 5.0.0\n   */\n  verificationId: string;\n  /**\n   * The verification code either received from the `phoneCodeSent` listener or entered by the user.\n   *\n   * The `verificationId` option must also be provided.\n   *\n   * @since 5.0.0\n   */\n  verificationCode: string;\n}\n\n/**\n * @since 0.2.2\n */\nexport interface CreateUserWithEmailAndPasswordOptions {\n  /**\n   * @since 0.2.2\n   */\n  email: string;\n  /**\n   * @since 0.2.2\n   */\n  password: string;\n}\n\n/**\n * @since 6.0.0\n */\nexport interface FetchSignInMethodsForEmailOptions {\n  /**\n   * The user's email address.\n   *\n   * @since 6.0.0\n   */\n  email: string;\n}\n\n/**\n * @since 6.0.0\n */\nexport interface FetchSignInMethodsForEmailResult {\n  /**\n   * The sign-in methods for the specified email address.\n   *\n   * This list is empty when [Email Enumeration Protection](https://cloud.google.com/identity-platform/docs/admin/email-enumeration-protection)\n   * is enabled, irrespective of the number of authentication methods available for the given email.\n   *\n   * @since 6.0.0\n   */\n  signInMethods: string[];\n}\n\n/**\n * @since 0.1.0\n */\nexport interface GetCurrentUserResult {\n  /**\n   * The currently signed-in user, or null if there isn't any.\n   *\n   * @since 0.1.0\n   */\n  user: User | null;\n}\n\n/**\n * @since 0.1.0\n */\nexport interface GetIdTokenOptions {\n  /**\n   * Force refresh regardless of token expiration.\n   *\n   * @since 0.1.0\n   */\n  forceRefresh: boolean;\n}\n\n/**\n * @since 0.1.0\n */\nexport interface GetIdTokenResult {\n  /**\n   * The Firebase Auth ID token JWT string.\n   *\n   * @since 0.1.0\n   */\n  token: string;\n}\n\n/**\n * @since 1.1.0\n */\nexport interface GetTenantIdResult {\n  /**\n   * The tenant id.\n   * `null` if it has never been set.\n   *\n   * @since 1.1.0\n   */\n  tenantId: string | null;\n}\n\n/**\n * @since 6.1.0\n */\nexport interface RevokeAccessTokenOptions {\n  /**\n   * The access token to revoke.\n   *\n   * @since 6.1.0\n   */\n  token: string;\n}\n\n/**\n * @since 6.1.0\n */\nexport interface SendEmailVerificationOptions {\n  /**\n   * Structure that contains the required continue/state URL with optional Android and iOS bundle identifiers.\n   *\n   * @since 6.1.0\n   */\n  actionCodeSettings?: ActionCodeSettings;\n}\n\n/**\n * @since 0.2.2\n */\nexport interface SendPasswordResetEmailOptions {\n  /**\n   * @since 0.2.2\n   */\n  email: string;\n  /**\n   * Structure that contains the required continue/state URL with optional Android and iOS bundle identifiers.\n   *\n   * @since 6.1.0\n   */\n  actionCodeSettings?: ActionCodeSettings;\n}\n\n/**\n * @since 0.1.0\n */\nexport interface SetLanguageCodeOptions {\n  /**\n   * BCP 47 language code.\n   *\n   * @example \"en-US\"\n   * @since 0.1.0\n   */\n  languageCode: string;\n}\n\n/**\n * @since 5.2.0\n */\nexport interface SetPersistenceOptions {\n  /**\n   * The persistence types.\n   *\n   * @since 5.2.0\n   */\n  persistence: Persistence;\n}\n\n/**\n * @since 5.2.0\n */\nexport enum Persistence {\n  /**\n   * Long term persistence using IndexedDB.\n   *\n   * @since 5.2.0\n   */\n  IndexedDbLocal = 'INDEXED_DB_LOCAL',\n  /**\n   * No persistence.\n   *\n   * @since 5.2.0\n   */\n  InMemory = 'IN_MEMORY',\n  /**\n   * Long term persistence using local storage.\n   *\n   * @since 5.2.0\n   */\n  BrowserLocal = 'BROWSER_LOCAL',\n  /**\n   * Temporary persistence using session storage.\n   *\n   * @since 5.2.0\n   */\n  BrowserSession = 'BROWSER_SESSION',\n}\n\n/**\n * @since 1.1.0\n */\nexport interface SetTenantIdOptions {\n  /**\n   * The tenant id.\n   *\n   * @since 1.1.0\n   */\n  tenantId: string;\n}\n\n/**\n * @since 0.2.2\n */\nexport interface UpdateEmailOptions {\n  /**\n   * The new email address.\n   *\n   * @since 0.2.2\n   */\n  newEmail: string;\n}\n\n/**\n * @since 6.3.0\n */\nexport interface VerifyBeforeUpdateEmailOptions {\n  /**\n   * The new email address to be verified before update.\n   *\n   * @since 6.3.0\n   */\n  newEmail: string;\n  /**\n   * The action code settings\n   *\n   * @since 6.3.0\n   */\n  actionCodeSettings?: ActionCodeSettings;\n}\n\n/**\n * @since 0.2.2\n */\nexport interface UpdatePasswordOptions {\n  /**\n   * The new password.\n   *\n   * @since 0.2.2\n   */\n  newPassword: string;\n}\n\n/**\n * @since 1.3.0\n */\nexport interface UpdateProfileOptions {\n  /**\n   * The user's display name.\n   *\n   * @since 1.3.0\n   */\n  displayName?: string | null;\n  /**\n   * The user's photo URL.\n   *\n   * @since 1.3.0\n   */\n  photoUrl?: string | null;\n}\n\n/**\n * @since 1.1.0\n */\nexport type LinkOptions = SignInOptions;\n\n/**\n * @since 1.1.0\n */\nexport type LinkWithOAuthOptions = SignInWithOAuthOptions;\n\n/**\n * @since 1.1.0\n */\nexport interface LinkWithEmailAndPasswordOptions {\n  /**\n   * The user's email address.\n   *\n   * @since 1.1.0\n   */\n  email: string;\n  /**\n   * The user's password.\n   *\n   * @since 1.1.0\n   */\n  password: string;\n}\n\n/**\n * @since 1.1.0\n */\nexport interface LinkWithEmailLinkOptions {\n  /**\n   * The user's email address.\n   *\n   * @since 1.1.0\n   */\n  email: string;\n  /**\n   * The link sent to the user's email address.\n   *\n   * @since 1.1.0\n   */\n  emailLink: string;\n}\n\n/**\n * @since 6.1.0\n */\nexport type LinkWithOpenIdConnectOptions = SignInWithOpenIdConnectOptions;\n\n/**\n * @since 1.1.0\n */\nexport type LinkWithPhoneNumberOptions = SignInWithPhoneNumberOptions;\n\n/**\n * @since 1.1.0\n */\nexport type LinkWithCustomTokenOptions = SignInWithCustomTokenOptions;\n\n/**\n * @since 1.1.0\n */\nexport type LinkResult = SignInResult;\n\n/**\n * @since 0.1.0\n */\nexport interface SignInOptions {\n  /**\n   * Whether the plugin should skip the native authentication or not.\n   * Only needed if you want to use the Firebase JavaScript SDK.\n   * This value overwrites the configrations value of the `skipNativeAuth` option.\n   * If no value is set, the configuration value is used.\n   *\n   * **Note that the plugin may behave differently across the platforms.**\n   *\n   * `skipNativeAuth` cannot be used in combination with `signInWithCustomToken`, `createUserWithEmailAndPassword` or `signInWithEmailAndPassword`.\n   *\n   * Only available for Android and iOS.\n   *\n   * @since 1.1.0\n   */\n  skipNativeAuth?: boolean;\n}\n\n/**\n * @since 1.1.0\n */\nexport interface SignInWithOAuthOptions extends SignInOptions {\n  /**\n   * Configures custom parameters to be passed to the identity provider during the OAuth sign-in flow.\n   *\n   * Supports Apple, Facebook, GitHub, Google, Microsoft, Twitter and Yahoo on Web.\n   * Supports Apple, GitHub, Microsoft, Twitter and Yahoo on Android.\n   * Supports Facebook, GitHub, Microsoft, Twitter and Yahoo on iOS.\n   *\n   * @since 1.1.0\n   */\n  customParameters?: SignInCustomParameter[];\n  /**\n   * Whether to use the popup-based OAuth authentication flow or the full-page redirect flow.\n   * If you choose `redirect`, you will get the result of the call via the `authStateChange` listener after the redirect.\n   *\n   * Only available for Web.\n   *\n   * @default 'popup'\n   * @since 1.3.0\n   */\n  mode?: 'popup' | 'redirect';\n  /**\n   * Scopes to request from provider.\n   *\n   * Supports Apple, Facebook, GitHub, Google, Microsoft, Twitter and Yahoo on Web.\n   * Supports Apple, GitHub, Google, Microsoft, Twitter, Yahoo and Play Games on Android.\n   * Supports Facebook, GitHub, Google, Microsoft, Twitter and Yahoo on iOS.\n   *\n   * @since 1.1.0\n   */\n  scopes?: string[];\n}\n\n/**\n * @since 0.1.0\n */\nexport interface SignInCustomParameter {\n  /**\n   * The custom parameter key (e.g. `login_hint`).\n   *\n   * @since 0.1.0\n   */\n  key: string;\n  /**\n   * The custom parameter value (e.g. `<EMAIL>`).\n   *\n   * @since 0.1.0\n   */\n  value: string;\n}\n\n/**\n * @since 6.1.0\n */\nexport interface SignInWithOpenIdConnectOptions extends SignInWithOAuthOptions {\n  /**\n   * The OpenID Connect provider ID.\n   *\n   * @since 6.1.0\n   * @example oidc.example-provider\n   */\n  providerId: string;\n}\n\n/**\n * @since 7.2.0\n */\nexport interface SignInWithFacebookOptions extends SignInWithOAuthOptions {\n  /**\n   * Whether to use the Facebook Limited Login mode.\n   *\n   * If set to `true`, no access token will be returned but the user does not have to\n   * grant App Tracking Transparency permission.\n   * If set to `false`, the user has to grant App Tracking Transparency permission.\n   * You can request the permission with `requestAppTrackingTransparencyPermission()`.\n   *\n   * Only available for iOS.\n   *\n   * @default false\n   * @since 7.2.0\n   */\n  useLimitedLogin?: boolean;\n}\n\n/**\n * @since 7.2.0\n */\nexport interface SignInWithGoogleOptions extends SignInWithOAuthOptions {\n  /**\n   * Whether to use the Credential Manager API to sign in.\n   *\n   * Only available for Android.\n   *\n   * @since 7.2.0\n   * @default true\n   */\n  useCredentialManager?: boolean;\n}\n\n/**\n * @since 7.2.0\n */\nexport interface CheckAppTrackingTransparencyPermissionResult {\n  /**\n   * The permission status of App Tracking Transparency.\n   *\n   * @since 7.2.0\n   */\n  status: AppTrackingTransparencyPermissionState;\n}\n\nexport type AppTrackingTransparencyPermissionState =\n  | PermissionState\n  | 'restricted';\n\n/**\n * @since 7.2.0\n */\nexport type RequestAppTrackingTransparencyPermissionResult =\n  CheckAppTrackingTransparencyPermissionResult;\n\n/**\n * @since 0.1.0\n */\nexport interface SignInWithPhoneNumberOptions extends SignInOptions {\n  /**\n   * The phone number to be verified in E.164 format.\n   *\n   * @example \"+16505550101\"\n   * @since 0.1.0\n   */\n  phoneNumber: string;\n  /**\n   * The reCAPTCHA verifier.\n   * Must be an instance of `firebase.auth.RecaptchaVerifier`.\n   *\n   * Only available for Web.\n   *\n   * @since 5.2.0\n   */\n  recaptchaVerifier?: unknown;\n  /**\n   * Resend the verification code to the specified phone number.\n   * `signInWithPhoneNumber` must be called once before using this option.\n   *\n   * Only available for Android.\n   *\n   * @since 1.3.0\n   * @default false\n   */\n  resendCode?: boolean;\n  /**\n   * The maximum amount of time in seconds to wait for the SMS auto-retrieval.\n   *\n   * Use 0 to disable SMS-auto-retrieval.\n   *\n   * Only available for Android.\n   *\n   * @since 5.4.0\n   * @default 60\n   * @see https://firebase.google.com/docs/reference/android/com/google/firebase/auth/PhoneAuthOptions.Builder#setTimeout(java.lang.Long,java.util.concurrent.TimeUnit)\n   */\n  timeout?: number;\n}\n\n/**\n * @since 0.2.2\n */\nexport interface SignInWithEmailAndPasswordOptions extends SignInOptions {\n  /**\n   * The user's email address.\n   *\n   * @since 0.2.2\n   */\n  email: string;\n  /**\n   * The user's password.\n   *\n   * @since 0.2.2\n   */\n  password: string;\n}\n\n/**\n * @since 1.1.0\n */\nexport interface SendSignInLinkToEmailOptions {\n  /**\n   * The user's email address.\n   *\n   * @since 1.1.0\n   */\n  email: string;\n  /**\n   * Structure that contains the required continue/state URL with optional Android and iOS bundle identifiers.\n   *\n   * @since 1.1.0\n   */\n  actionCodeSettings: ActionCodeSettings;\n}\n\n/**\n * @since 1.1.0\n */\nexport interface IsSignInWithEmailLinkOptions {\n  /**\n   * The link sent to the user's email address.\n   *\n   * @since 1.1.0\n   */\n  emailLink: string;\n}\n/**\n * @since 1.1.0\n */\nexport interface IsSignInWithEmailLinkResult {\n  /**\n   * Whether an incoming link is a signup with email link suitable for `signInWithEmailLink(...)`.\n   */\n  isSignInWithEmailLink: boolean;\n}\n\n/**\n * @since 1.1.0\n */\nexport interface SignInWithEmailLinkOptions extends SignInOptions {\n  /**\n   * The user's email address.\n   *\n   * @since 1.1.0\n   */\n  email: string;\n  /**\n   * The link sent to the user's email address.\n   *\n   * @since 1.1.0\n   */\n  emailLink: string;\n}\n\n/**\n * @since 0.1.0\n */\nexport interface SignInWithCustomTokenOptions extends SignInOptions {\n  /**\n   * The custom token to sign in with.\n   *\n   * @since 0.1.0\n   */\n  token: string;\n}\n\n/**\n * @since 0.1.0\n */\nexport interface SignInResult {\n  /**\n   * The currently signed-in user, or null if there isn't any.\n   *\n   * @since 0.1.0\n   */\n  user: User | null;\n  /**\n   * Credentials returned by an auth provider.\n   *\n   * @since 0.1.0\n   */\n  credential: AuthCredential | null;\n  /**\n   * Additional user information from a federated identity provider.\n   *\n   * @since 0.5.1\n   */\n  additionalUserInfo: AdditionalUserInfo | null;\n}\n\n/**\n * @since 1.1.0\n */\nexport interface UnlinkOptions {\n  /**\n   * The provider to unlink.\n   *\n   * @since 1.1.0\n   */\n  providerId: ProviderId;\n}\n\n/**\n * @since 1.1.0\n */\nexport interface UnlinkResult {\n  /**\n   * The currently signed-in user, or null if there isn't any.\n   *\n   * @since 1.1.0\n   */\n  user: User | null;\n}\n\n/**\n * @since 0.2.0\n */\nexport interface UseEmulatorOptions {\n  /**\n   * The emulator host without any port or scheme.\n   *\n   * @since 0.2.0\n   * @example \"127.0.0.1\"\n   */\n  host: string;\n  /**\n   * The emulator port.\n   *\n   * @since 0.2.0\n   * @default 9099\n   * @example 9099\n   */\n  port?: number;\n  /**\n   * The emulator scheme.\n   *\n   * Only available for Web.\n   *\n   * @since 5.2.0\n   * @default \"http\"\n   * @example \"https\"\n   */\n  scheme?: string;\n}\n\n/**\n * @since 0.1.0\n * @see https://firebase.google.com/docs/reference/js/auth.user\n */\nexport interface User {\n  /**\n   * @since 0.1.0\n   */\n  displayName: string | null;\n  /**\n   * @since 0.1.0\n   */\n  email: string | null;\n  /**\n   * @since 0.1.0\n   */\n  emailVerified: boolean;\n  /**\n   * @since 0.1.0\n   */\n  isAnonymous: boolean;\n  /**\n   * The user's metadata.\n   *\n   * @since 5.2.0\n   */\n  metadata: UserMetadata;\n  /**\n   * @since 0.1.0\n   */\n  phoneNumber: string | null;\n  /**\n   * @since 0.1.0\n   */\n  photoUrl: string | null;\n  /**\n   * Additional per provider such as displayName and profile information.\n   *\n   * @since 5.2.0\n   */\n  providerData: UserInfo[];\n  /**\n   * @since 0.1.0\n   */\n  providerId: string;\n  /**\n   * @since 0.1.0\n   */\n  tenantId: string | null;\n  /**\n   * @since 0.1.0\n   */\n  uid: string;\n}\n\n/**\n * @since 5.2.0\n * @see https://firebase.google.com/docs/reference/js/auth.userinfo\n */\nexport interface UserInfo {\n  /**\n   * The display name of the user.\n   *\n   * @since 5.2.0\n   */\n  displayName: string | null;\n  /**\n   * The email of the user.\n   *\n   * @since 5.2.0\n   */\n  email: string | null;\n  /**\n   * The phone number normalized based on the E.164 standard (e.g. +16505550101) for the user.\n   *\n   * @since 5.2.0\n   */\n  phoneNumber: string | null;\n  /**\n   * The profile photo URL of the user.\n   *\n   * @since 5.2.0\n   */\n  photoUrl: string | null;\n  /**\n   * The provider used to authenticate the user.\n   *\n   * @since 5.2.0\n   */\n  providerId: string;\n  /**\n   * The user's unique ID.\n   *\n   * @since 5.2.0\n   */\n  uid: string;\n}\n\n/**\n * @since 5.2.0\n * @see https://firebase.google.com/docs/reference/js/auth.usermetadata\n */\nexport interface UserMetadata {\n  /**\n   * Time the user was created in milliseconds since the epoch.\n   *\n   * @since 5.2.0\n   * @example 1695130859034\n   */\n  creationTime?: number;\n  /**\n   * Time the user last signed in in milliseconds since the epoch.\n   *\n   * @since 5.2.0\n   * @example 1695130859034\n   */\n  lastSignInTime?: number;\n}\n\n/**\n * @since 0.1.0\n */\nexport interface AuthCredential {\n  /**\n   * The OAuth access token associated with the credential if it belongs to an OAuth provider.\n   *\n   * @since 0.1.0\n   */\n  accessToken?: string;\n  /**\n   * A token that the app uses to interact with the server.\n   *\n   * Only available for Apple Sign-in on iOS.\n   *\n   * @since 1.2.0\n   */\n  authorizationCode?: string;\n  /**\n   * The OAuth ID token associated with the credential if it belongs to an OIDC provider.\n   *\n   * @since 0.1.0\n   */\n  idToken?: string;\n  /**\n   * The random string used to make sure that the ID token you get was granted specifically in response to your app's authentication request.\n   *\n   * @since 0.1.0\n   */\n  nonce?: string;\n  /**\n   * The authentication provider ID for the credential.\n   *\n   * @example \"google.com\"\n   * @since 0.1.0\n   */\n  providerId: string;\n  /**\n   * The OAuth access token secret associated with the credential if it belongs to an OAuth 1.0 provider.\n   *\n   * @since 0.1.0\n   */\n  secret?: string;\n  /**\n   * The server auth code.\n   *\n   * Only available for Google Sign-in and Play Games Sign-In on Android and iOS.\n   *\n   * @since 5.2.0\n   */\n  serverAuthCode?: string;\n}\n\n/**\n * @since 0.5.1\n */\nexport interface AdditionalUserInfo {\n  /**\n   * Whether the user is new (sign-up) or existing (sign-in).\n   *\n   * @since 0.5.1\n   */\n  isNewUser: boolean;\n  /**\n   * Map containing IDP-specific user data.\n   *\n   * @since 0.5.1\n   */\n  profile?: { [key: string]: unknown };\n  /**\n   * Identifier for the provider used to authenticate this user.\n   *\n   * @since 0.5.1\n   */\n  providerId?: string;\n  /**\n   * The username if the provider is GitHub or Twitter.\n   *\n   * @since 0.5.1\n   */\n  username?: string;\n}\n\n/**\n * Callback to receive the user's sign-in state change notifications.\n *\n * @since 0.1.0\n */\nexport type AuthStateChangeListener = (change: AuthStateChange) => void;\n\n/**\n * Callback to receive the ID token change notifications.\n *\n * @since 6.3.0\n */\nexport type IdTokenChangeListener = (change: GetIdTokenResult) => void;\n\n/**\n * @since 0.1.0\n */\nexport interface AuthStateChange {\n  /**\n   * The currently signed-in user, or null if there isn't any.\n   *\n   * @since 0.1.0\n   */\n  user: User | null;\n}\n\n/**\n * Callback to receive the verification code sent to the user's phone number.\n *\n * @since 1.3.0\n */\nexport type PhoneVerificationCompletedListener = (\n  event: PhoneVerificationCompletedEvent,\n) => void;\n\n/**\n * @since 5.0.0\n */\nexport interface PhoneVerificationCompletedEvent extends SignInResult {\n  /**\n   * The verification code sent to the user's phone number.\n   *\n   * If instant verification is used, this property is not set.\n   *\n   * @since 5.0.0\n   */\n  verificationCode?: string;\n}\n\n/**\n * Callback to receive notifications of failed phone verification.\n *\n * @since 1.3.0\n */\nexport type PhoneVerificationFailedListener = (\n  event: PhoneVerificationFailedEvent,\n) => void;\n\n/**\n * @since 5.0.0\n */\nexport interface PhoneVerificationFailedEvent {\n  /**\n   * The error message.\n   *\n   * @since 1.3.0\n   */\n  message: string;\n}\n\n/**\n * Callback to receive the verification ID.\n *\n * @since 1.3.0\n */\nexport type PhoneCodeSentListener = (event: PhoneCodeSentEvent) => void;\n\n/**\n * @since 5.0.0\n */\nexport interface PhoneCodeSentEvent {\n  /**\n   * The verification ID, which is needed to identify the verification code.\n   *\n   * @since 1.3.0\n   */\n  verificationId: string;\n}\n\n/**\n * An interface that defines the required continue/state URL with optional Android and iOS\n * bundle identifiers.\n *\n * @since 1.1.0\n */\nexport interface ActionCodeSettings {\n  /**\n   * Sets the Android package name.\n   */\n  android?: {\n    installApp?: boolean;\n    minimumVersion?: string;\n    packageName: string;\n  };\n  /**\n   * When set to true, the action code link will be be sent as a Universal Link or Android App\n   * Link and will be opened by the app if installed.\n   */\n  handleCodeInApp?: boolean;\n  /**\n   * Sets the iOS bundle ID.\n   */\n  iOS?: {\n    bundleId: string;\n  };\n  /**\n   * Sets the link continue/state URL.\n   */\n  url: string;\n  /**\n   * When multiple custom dynamic link domains are defined for a project, specify which one to use\n   * when the link is to be opened via a specified mobile app (for example, `example.page.link`).\n   */\n  dynamicLinkDomain?: string;\n}\n\nexport enum ProviderId {\n  APPLE = 'apple.com',\n  FACEBOOK = 'facebook.com',\n  GAME_CENTER = 'gc.apple.com',\n  GITHUB = 'github.com',\n  GOOGLE = 'google.com',\n  MICROSOFT = 'microsoft.com',\n  PLAY_GAMES = 'playgames.google.com',\n  TWITTER = 'twitter.com',\n  YAHOO = 'yahoo.com',\n  PASSWORD = 'password',\n  PHONE = 'phone',\n}\n"], "mappings": ";AAkwBA,IAAY;CAAZ,SAAYA,cAAW;AAMrB,EAAAA,aAAA,gBAAA,IAAA;AAMA,EAAAA,aAAA,UAAA,IAAA;AAMA,EAAAA,aAAA,cAAA,IAAA;AAMA,EAAAA,aAAA,gBAAA,IAAA;AACF,GAzBY,gBAAA,cAAW,CAAA,EAAA;AAg1BvB,IAAY;CAAZ,SAAYC,aAAU;AACpB,EAAAA,YAAA,OAAA,IAAA;AACA,EAAAA,YAAA,UAAA,IAAA;AACA,EAAAA,YAAA,aAAA,IAAA;AACA,EAAAA,YAAA,QAAA,IAAA;AACA,EAAAA,YAAA,QAAA,IAAA;AACA,EAAAA,YAAA,WAAA,IAAA;AACA,EAAAA,YAAA,YAAA,IAAA;AACA,EAAAA,YAAA,SAAA,IAAA;AACA,EAAAA,YAAA,OAAA,IAAA;AACA,EAAAA,YAAA,UAAA,IAAA;AACA,EAAAA,YAAA,OAAA,IAAA;AACF,GAZY,eAAA,aAAU,CAAA,EAAA;", "names": ["Persistence", "ProviderId"]}