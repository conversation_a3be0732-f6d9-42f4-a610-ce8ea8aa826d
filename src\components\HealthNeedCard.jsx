import React from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowRight } from 'lucide-react'
import { Card, CardContent } from './ui/Card'
import { cn } from '../lib/utils'

export const HealthNeedCard = ({ healthNeed }) => {
  const navigate = useNavigate()

  const handleClick = () => {
    navigate(`/products?category=${healthNeed.category}`)
  }

  return (
    <Card
      className="cursor-pointer overflow-hidden group hover-lift transition-all duration-300"
      onClick={handleClick}
    >
      <div className="relative">
        {/* Background with gradient overlay */}
        <div className={cn("h-40 w-full relative", healthNeed.color)}>
          <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent" />
        </div>

        {/* Product Image */}
        <div className="absolute inset-0 flex items-center justify-center">
          <img
            src={healthNeed.image}
            alt={healthNeed.product}
            className="w-24 h-24 object-contain group-hover:scale-110 transition-transform duration-300 drop-shadow-lg"
          />
        </div>
      </div>

      <CardContent className="p-6">
        {/* Title */}
        <h3 className="text-sm font-medium text-gray-600 mb-2 uppercase tracking-wide">
          {healthNeed.title}
        </h3>

        {/* Product Name */}
        <h4 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-slate-900 transition-colors">
          {healthNeed.product}
        </h4>

        {/* Call to Action */}
        <div className="flex items-center text-sm font-medium text-slate-900 group-hover:text-slate-700 transition-colors">
          <span>Ver productos</span>
          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
        </div>
      </CardContent>
    </Card>
  )
}
