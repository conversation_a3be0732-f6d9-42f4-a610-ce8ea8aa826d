import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Card } from './ui/Card'
import { cn } from '../lib/utils'

export const HealthNeedCard = ({ healthNeed }) => {
  const navigate = useNavigate()

  const handleClick = () => {
    navigate(`/products?category=${healthNeed.category}`)
  }

  return (
    <Card 
      className="cursor-pointer overflow-hidden group animate-enter hover:scale-105 transition-all duration-300"
      onClick={handleClick}
    >
      <div className="relative">
        {/* Background Color */}
        <div className={cn("h-32 w-full", healthNeed.color)} />
        
        {/* Product Image */}
        <div className="absolute inset-0 flex items-center justify-center">
          <img 
            src={healthNeed.image} 
            alt={healthNeed.product}
            className="w-20 h-20 object-contain group-hover:scale-110 transition-transform duration-300"
          />
        </div>
      </div>
      
      <div className="p-6 space-y-3">
        {/* Title */}
        <h3 className="text-sm font-bold text-gray-800 leading-tight">
          {healthNeed.title}
        </h3>
        
        {/* Product Name */}
        <p className="text-2xl font-black text-gray-900 tracking-tight">
          {healthNeed.product}
        </p>
        
        {/* Call to Action */}
        <div className="pt-2">
          <span className="text-sm font-semibold text-electric-orange-500 group-hover:text-electric-orange-600 transition-colors">
            Ver productos →
          </span>
        </div>
      </div>
    </Card>
  )
}
