import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, Shield } from 'lucide-react'
import { Button } from '../../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card'
import { useAuth } from '../../context/AuthContext'

export const AdminLoginPage = () => {
  const navigate = useNavigate()
  const { signInWithGoogle, loading } = useAuth()
  const [error, setError] = useState('')

  const handleAdminSignIn = async () => {
    try {
      setError('')
      const result = await signInWithGoogle()
      
      // In a real app, you would check if the user has admin privileges
      // For demo purposes, we'll assume any Google sign-in grants admin access
      if (result.user) {
        navigate('/admin/orders')
      }
    } catch (error) {
      console.error('Error signing in:', error)
      setError('Error al iniciar sesión como administrador. Por favor, intenta de nuevo.')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Back Button */}
        <Button 
          variant="ghost" 
          onClick={() => navigate('/')}
          className="mb-6 flex items-center space-x-2 text-white hover:text-gray-300"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Volver al inicio</span>
        </Button>

        <Card className="shadow-2xl border-gray-700 bg-gray-800">
          <CardHeader className="text-center space-y-4">
            {/* Admin Icon */}
            <div className="bg-electric-orange-500 rounded-xl p-4 w-fit mx-auto">
              <Shield className="h-8 w-8 text-white" />
            </div>
            
            <CardTitle className="text-2xl font-bold text-white">
              Panel de Administración
            </CardTitle>
            <p className="text-gray-300">
              Acceso restringido solo para operadores autorizados
            </p>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Error Message */}
            {error && (
              <div className="bg-red-900/50 border border-red-700 rounded-lg p-3">
                <p className="text-red-300 text-sm">{error}</p>
              </div>
            )}

            {/* Admin Sign In */}
            <Button
              onClick={handleAdminSignIn}
              disabled={loading}
              className="w-full h-12 bg-electric-orange-500 hover:bg-electric-orange-600 text-white flex items-center justify-center space-x-3"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <>
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  <span>Iniciar sesión con Google</span>
                </>
              )}
            </Button>

            {/* Security Notice */}
            <div className="bg-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-white mb-2">
                🔒 Acceso Seguro
              </h4>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• Solo personal autorizado</li>
                <li>• Todas las acciones son registradas</li>
                <li>• Acceso monitoreado 24/7</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <p className="text-center text-sm text-gray-400 mt-6">
          Si no tienes acceso autorizado, por favor contacta al administrador del sistema
        </p>
      </div>
    </div>
  )
}
