import React from 'react'
import { useNavigate } from 'react-router-dom'
import { User, MapPin, Package, LogOut, Settings } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { useAuth } from '../context/AuthContext'

export const ProfilePage = () => {
  const navigate = useNavigate()
  const { user, signOut, isAuthenticated } = useAuth()

  const handleSignOut = async () => {
    try {
      await signOut()
      navigate('/')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Iniciar Sesión</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600 text-center">
              Inicia sesión para acceder a tu perfil
            </p>
            <Button 
              className="w-full"
              onClick={() => navigate('/auth/login')}
            >
              Iniciar Sesión
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-electric-orange-500 to-soft-magenta-500 py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-white">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              {user?.photoURL ? (
                <img 
                  src={user.photoURL} 
                  alt={user.displayName}
                  className="w-20 h-20 rounded-full object-cover"
                />
              ) : (
                <User className="h-12 w-12" />
              )}
            </div>
            <h1 className="text-3xl font-bold mb-2">
              {user?.displayName || 'Usuario'}
            </h1>
            <p className="text-white/90">
              {user?.email}
            </p>
          </div>
        </div>
      </div>

      {/* Profile Options */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Addresses */}
          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => navigate('/my-profile/addresses')}>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-electric-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="h-6 w-6 text-electric-orange-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Mis Direcciones
              </h3>
              <p className="text-gray-600 text-sm">
                Gestiona tus direcciones de envío
              </p>
            </CardContent>
          </Card>

          {/* Orders */}
          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => navigate('/my-profile/orders')}>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-cobalt-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="h-6 w-6 text-cobalt-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Mis Pedidos
              </h3>
              <p className="text-gray-600 text-sm">
                Revisa el estado de tus pedidos
              </p>
            </CardContent>
          </Card>

          {/* Settings */}
          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => navigate('/my-profile/settings')}>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-soft-magenta-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Settings className="h-6 w-6 text-soft-magenta-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Configuración
              </h3>
              <p className="text-gray-600 text-sm">
                Ajusta tus preferencias
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Sign Out */}
        <div className="mt-8 text-center">
          <Button 
            variant="outline"
            onClick={handleSignOut}
            className="flex items-center space-x-2"
          >
            <LogOut className="h-4 w-4" />
            <span>Cerrar Sesión</span>
          </Button>
        </div>
      </div>
    </div>
  )
}
