import React, { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { ArrowLeft, Package, MapPin, CreditCard, Trash2, Save } from 'lucide-react'
import { Button } from '../../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card'
import { Input } from '../../components/ui/Input'
import { Badge } from '../../components/ui/Badge'
import { useOrder } from '../../hooks/useOrders'
import { useOrders } from '../../hooks/useOrders'
import { formatPrice, formatDate } from '../../lib/utils'

const statusOptions = [
  { value: 'awaiting_admin_validation', label: 'Pendiente validación', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'paid', label: 'Pagado', color: 'bg-blue-100 text-blue-800' },
  { value: 'shipped', label: 'Enviado', color: 'bg-green-100 text-green-800' },
  { value: 'delivered', label: 'Entregado', color: 'bg-lime-100 text-lime-800' },
  { value: 'cancelled', label: 'Cancelado', color: 'bg-red-100 text-red-800' }
]

export const AdminOrderDetailPage = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { order, loading, error } = useOrder(id)
  const { updateOrderStatus, deleteOrder } = useOrders()
  const [selectedStatus, setSelectedStatus] = useState('')
  const [trackingNumber, setTrackingNumber] = useState('')
  const [updating, setUpdating] = useState(false)

  React.useEffect(() => {
    if (order) {
      setSelectedStatus(order.status)
      setTrackingNumber(order.trackingNumber || '')
    }
  }, [order])

  const handleUpdateStatus = async () => {
    try {
      setUpdating(true)
      await updateOrderStatus(id, selectedStatus, trackingNumber || null)
      alert('Estado actualizado correctamente')
    } catch (error) {
      console.error('Error updating status:', error)
      alert('Error al actualizar el estado')
    } finally {
      setUpdating(false)
    }
  }

  const handleDeleteOrder = async () => {
    if (window.confirm('¿Estás seguro de que quieres eliminar esta orden? Esta acción no se puede deshacer.')) {
      try {
        await deleteOrder(id)
        alert('Orden eliminada correctamente')
        navigate('/admin/orders')
      } catch (error) {
        console.error('Error deleting order:', error)
        alert('Error al eliminar la orden')
      }
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-electric-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando orden...</p>
        </div>
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Orden no encontrada</p>
          <Button onClick={() => navigate('/admin/orders')}>
            Volver a órdenes
          </Button>
        </div>
      </div>
    )
  }

  const currentStatus = statusOptions.find(s => s.value === order.status)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={() => navigate('/admin/orders')}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Volver a órdenes</span>
              </Button>
              
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Orden #{order.id?.slice(-8)}
                </h1>
                <p className="text-gray-600">
                  {order.orderDate ? formatDate(order.orderDate) : 'Fecha no disponible'}
                </p>
              </div>
            </div>
            
            <Button 
              variant="destructive"
              onClick={handleDeleteOrder}
              className="flex items-center space-x-2"
            >
              <Trash2 className="h-4 w-4" />
              <span>Eliminar orden</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Status Management */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="h-5 w-5" />
                  <span>Estado de la orden</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium text-gray-700">Estado actual:</span>
                  <Badge className={currentStatus?.color}>
                    {currentStatus?.label}
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Cambiar estado
                    </label>
                    <select
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-orange-500"
                    >
                      {statusOptions.map((status) => (
                        <option key={status.value} value={status.value}>
                          {status.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Número de seguimiento (opcional)
                    </label>
                    <Input
                      type="text"
                      placeholder="Ej: TRK123456789"
                      value={trackingNumber}
                      onChange={(e) => setTrackingNumber(e.target.value)}
                    />
                  </div>
                </div>

                <Button 
                  onClick={handleUpdateStatus}
                  disabled={updating || selectedStatus === order.status}
                  className="flex items-center space-x-2"
                >
                  {updating ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>Actualizar estado</span>
                </Button>
              </CardContent>
            </Card>

            {/* Products */}
            <Card>
              <CardHeader>
                <CardTitle>Productos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.products?.map((product, index) => (
                    <div key={index} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                      <img 
                        src={product.imageUrl || `https://placehold.co/80x80/f3f4f6/9ca3af/PNG?text=${encodeURIComponent(product.name)}`}
                        alt={product.name}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900">{product.name}</h4>
                        <p className="text-sm text-gray-600">Cantidad: {product.quantity}</p>
                        <p className="text-sm font-medium text-electric-orange-600">
                          {formatPrice(product.price)} c/u
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900">
                          {formatPrice(product.price * product.quantity)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Resumen</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Subtotal:</span>
                  <span>{formatPrice((order.totalAmount || 0) - 15)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Envío:</span>
                  <span>{formatPrice(15)}</span>
                </div>
                <hr />
                <div className="flex justify-between font-semibold">
                  <span>Total:</span>
                  <span className="text-electric-orange-600">
                    {formatPrice(order.totalAmount || 0)}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Shipping Address */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5" />
                  <span>Dirección de envío</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {order.shippingAddress ? (
                  <div className="text-sm text-gray-600 space-y-1">
                    <p className="font-semibold text-gray-900">{order.shippingAddress.name}</p>
                    <p>{order.shippingAddress.street}</p>
                    <p>{order.shippingAddress.city}, {order.shippingAddress.state}</p>
                    <p>{order.shippingAddress.zipCode}, {order.shippingAddress.country}</p>
                  </div>
                ) : (
                  <p className="text-gray-500">No hay dirección disponible</p>
                )}
              </CardContent>
            </Card>

            {/* Payment Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CreditCard className="h-5 w-5" />
                  <span>Información de pago</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-gray-600 space-y-2">
                  <div>
                    <span className="font-medium">ID de transacción Stripe:</span>
                    <p className="font-mono text-xs break-all">
                      {order.stripeTransactionId || 'No disponible'}
                    </p>
                  </div>
                  {order.trackingNumber && (
                    <div>
                      <span className="font-medium">Número de seguimiento:</span>
                      <p className="font-mono text-xs">
                        {order.trackingNumber}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
