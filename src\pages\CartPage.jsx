import React from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, ShoppingBag, Trash2 } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { CartItem } from '../components/CartItem'
import { useCart } from '../context/CartContext'
import { formatPrice } from '../lib/utils'

export const CartPage = () => {
  const navigate = useNavigate()
  const { items, clearCart, getTotalItems, getTotalPrice } = useCart()

  const totalItems = getTotalItems()
  const totalPrice = getTotalPrice()
  const shippingCost = totalPrice > 100 ? 0 : 15 // Free shipping over S/100
  const finalTotal = totalPrice + shippingCost

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <Button 
              variant="ghost" 
              onClick={() => navigate(-1)}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Continuar comprando</span>
            </Button>
          </div>
        </div>

        {/* Empty Cart */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center max-w-md mx-auto">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <ShoppingBag className="h-12 w-12 text-gray-400" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Tu carrito está vacío
            </h2>
            <p className="text-gray-600 mb-8">
              Agrega algunos productos para comenzar tu compra
            </p>
            <Button 
              size="lg"
              onClick={() => navigate('/products')}
            >
              Explorar productos
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              onClick={() => navigate(-1)}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Continuar comprando</span>
            </Button>
            
            <h1 className="text-2xl font-bold text-gray-900">
              Carrito de Compras ({totalItems})
            </h1>
            
            <Button 
              variant="outline"
              onClick={clearCart}
              className="flex items-center space-x-2 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
              <span className="hidden sm:inline">Vaciar carrito</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Cart Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Productos en tu carrito
            </h2>
            
            {items.map((item) => (
              <div key={item.id} className="animate-enter">
                <CartItem item={item} />
              </div>
            ))}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle>Resumen del pedido</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Items Summary */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal ({totalItems} productos)</span>
                    <span>{formatPrice(totalPrice)}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span>Envío</span>
                    <span className={shippingCost === 0 ? 'text-green-600 font-medium' : ''}>
                      {shippingCost === 0 ? 'Gratis' : formatPrice(shippingCost)}
                    </span>
                  </div>
                  
                  {shippingCost > 0 && (
                    <p className="text-xs text-gray-500">
                      Envío gratis en compras mayores a {formatPrice(100)}
                    </p>
                  )}
                </div>

                <hr />

                {/* Total */}
                <div className="flex justify-between text-lg font-bold">
                  <span>Total</span>
                  <span className="text-electric-orange-600">
                    {formatPrice(finalTotal)}
                  </span>
                </div>

                {/* Checkout Button */}
                <Button 
                  size="lg" 
                  className="w-full"
                  onClick={() => navigate('/checkout')}
                >
                  Proceder al pago
                </Button>

                {/* Security Info */}
                <div className="text-center">
                  <p className="text-xs text-gray-500">
                    🔒 Pago seguro con encriptación SSL
                  </p>
                </div>

                {/* Continue Shopping */}
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => navigate('/products')}
                >
                  Continuar comprando
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
