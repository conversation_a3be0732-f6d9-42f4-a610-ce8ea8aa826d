import{W as L,b as a,c as r,A as u,G as y}from"./index-CgszkiLG.js";const v=e=>E(e),q=(e,t)=>(typeof e=="string"&&(t=e,e=void 0),v(e).includes(t)),E=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let t=e.Ionic.platforms;return t==null&&(t=e.Ionic.platforms=K(e),t.forEach(i=>e.document.documentElement.classList.add(`plt-${i}`))),t},K=e=>Object.keys(S).filter(t=>S[t](e)),F=e=>p(e)&&!C(e),d=e=>!!(o(e,/iPad/i)||o(e,/Macintosh/i)&&p(e)),M=e=>o(e,/iPhone/i),R=e=>o(e,/iPhone|iPod/i)||d(e),A=e=>o(e,/android|sink/i),I=e=>A(e)&&!o(e,/mobile/i),N=e=>{const t=e.innerWidth,i=e.innerHeight,n=Math.min(t,i),s=Math.max(t,i);return n>390&&n<520&&s>620&&s<800},T=e=>{const t=e.innerWidth,i=e.innerHeight,n=Math.min(t,i),s=Math.max(t,i);return d(e)||I(e)||n>460&&n<820&&s>780&&s<1400},p=e=>_(e,"(any-pointer:coarse)"),G=e=>!p(e),C=e=>w(e)||g(e),w=e=>!!(e.cordova||e.phonegap||e.PhoneGap),g=e=>{const t=e.Capacitor;return!!t?.isNative},W=e=>o(e,/electron/i),k=e=>!!(e.matchMedia("(display-mode: standalone)").matches||e.navigator.standalone),o=(e,t)=>t.test(e.navigator.userAgent),_=(e,t)=>e.matchMedia(t).matches,S={ipad:d,iphone:M,ios:R,android:A,phablet:N,tablet:T,cordova:w,capacitor:g,electron:W,pwa:k,mobile:p,mobileweb:F,desktop:G,hybrid:C};class x extends L{async initialize(t){if(typeof t.publishableKey!="string"||t.publishableKey.trim().length===0)throw new Error("you must provide a valid key");this.publishableKey=t.publishableKey,t.stripeAccount&&(this.stripeAccount=t.stripeAccount)}async createPaymentSheet(t){var i;if(!this.publishableKey){this.notifyListeners(a.FailedToLoad,null);return}this.paymentSheet=document.createElement("stripe-payment-sheet"),(i=document.querySelector("body"))===null||i===void 0||i.appendChild(this.paymentSheet),await customElements.whenDefined("stripe-payment-sheet"),this.paymentSheet.publishableKey=this.publishableKey,this.stripeAccount&&(this.paymentSheet.stripeAccount=this.stripeAccount),this.paymentSheet.applicationName="@capacitor-community/stripe",this.paymentSheet.intentClientSecret=t.paymentIntentClientSecret,this.paymentSheet.intentType="payment",t.withZipCode!==void 0&&(this.paymentSheet.zip=t.withZipCode),this.notifyListeners(a.Loaded,null)}async presentPaymentSheet(){if(!this.paymentSheet)throw new Error;const t=await this.paymentSheet.present();if(t===void 0)return this.notifyListeners(a.Canceled,null),{paymentResult:a.Canceled};const{detail:{stripe:i,cardNumberElement:n}}=t,s=await i.createPaymentMethod({type:"card",card:n});return this.paymentSheet.updateProgress("success"),this.paymentSheet.remove(),s.error!==void 0?(this.notifyListeners(a.Failed,null),{paymentResult:a.Failed}):(this.notifyListeners(a.Completed,null),{paymentResult:a.Completed})}async createPaymentFlow(t){var i;if(!this.publishableKey){this.notifyListeners(r.FailedToLoad,null);return}this.paymentSheet=document.createElement("stripe-payment-sheet"),(i=document.querySelector("body"))===null||i===void 0||i.appendChild(this.paymentSheet),await customElements.whenDefined("stripe-payment-sheet"),this.paymentSheet.publishableKey=this.publishableKey,this.stripeAccount&&(this.paymentSheet.stripeAccount=this.stripeAccount),this.paymentSheet.applicationName="@capacitor-community/stripe",t.hasOwnProperty("paymentIntentClientSecret")?(this.paymentSheet.intentType="payment",this.paymentSheet.intentClientSecret=t.paymentIntentClientSecret):(this.paymentSheet.intentType="setup",this.paymentSheet.intentClientSecret=t.setupIntentClientSecret),t.withZipCode!==void 0&&(this.paymentSheet.zip=t.withZipCode),q(window,"ios")?(this.paymentSheet.buttonLabel="Add card",this.paymentSheet.sheetTitle="Add a card"):this.paymentSheet.buttonLabel="Add",this.notifyListeners(r.Loaded,null)}async presentPaymentFlow(){if(!this.paymentSheet)throw new Error;this.notifyListeners(r.Opened,null);const t=await this.paymentSheet.present().catch(()=>{});if(t===void 0)throw this.notifyListeners(r.Canceled,null),new Error;const{detail:{stripe:i,cardNumberElement:n}}=t,{token:s}=await i.createToken(n);if(s===void 0||s.card===void 0)throw new Error;return this.flowStripe=i,this.flowCardNumberElement=n,this.notifyListeners(r.Created,{cardNumber:s.card.last4}),{cardNumber:s.card.last4}}async confirmPaymentFlow(){if(!this.paymentSheet||!this.flowStripe||!this.flowCardNumberElement)throw new Error;return(await this.flowStripe.createPaymentMethod({type:"card",card:this.flowCardNumberElement})).error!==void 0&&this.notifyListeners(r.Failed,null),this.paymentSheet.updateProgress("success"),this.paymentSheet.remove(),this.notifyListeners(r.Completed,null),{paymentResult:r.Completed}}isApplePayAvailable(){return this.isAvailable("applePay")}async createApplePay(t){if(!this.publishableKey){this.notifyListeners(u.FailedToLoad,null);return}this.requestApplePay=await this.createPaymentRequestButton(),this.requestApplePayOptions=t,this.notifyListeners(u.Loaded,null)}presentApplePay(){return this.presentPaymentRequestButton("applePay",this.requestApplePay,this.requestApplePayOptions,u)}isGooglePayAvailable(){return this.isAvailable("googlePay")}async createGooglePay(t){if(!this.publishableKey){this.notifyListeners(y.FailedToLoad,null);return}this.requestGooglePay=await this.createPaymentRequestButton(),this.requestGooglePayOptions=t,this.notifyListeners(y.Loaded,null)}presentGooglePay(){return this.presentPaymentRequestButton("googlePay",this.requestGooglePay,this.requestGooglePayOptions,y)}async isAvailable(t){var i;const n=document.createElement("stripe-payment-request-button");return n.id=`isAvailable-${t}`,(i=document.querySelector("body"))===null||i===void 0||i.appendChild(n),await customElements.whenDefined("stripe-payment-request-button"),this.publishableKey&&(n.publishableKey=this.publishableKey),this.stripeAccount&&(n.stripeAccount=this.stripeAccount),n.applicationName="@capacitor-community/stripe",await n.isAvailable(t).finally(()=>n.remove())}async createPaymentRequestButton(){var t;const i=document.createElement("stripe-payment-request-button");return(t=document.querySelector("body"))===null||t===void 0||t.appendChild(i),await customElements.whenDefined("stripe-payment-request-button"),this.publishableKey&&(i.publishableKey=this.publishableKey),this.stripeAccount&&(i.stripeAccount=this.stripeAccount),i.applicationName="@capacitor-community/stripe",i}async presentPaymentRequestButton(t,i,n,s){return new Promise(async l=>{if(i===void 0||n===void 0||this.publishableKey===void 0)return this.notifyListeners(s.Failed,null),l({paymentResult:s.Failed});await i.setPaymentRequestOption({country:n.countryCode.toUpperCase(),currency:n.currency.toLowerCase(),total:n.paymentSummaryItems[n.paymentSummaryItems.length-1],disableWallets:t==="applePay"?["googlePay","browserCard"]:["applePay","browserCard"],requestPayerName:!0,requestPayerEmail:!0});const m=n.paymentIntentClientSecret;await i.setPaymentMethodEventHandler(async(c,f)=>{const{paymentIntent:h,error:b}=await f.confirmCardPayment(m,{payment_method:c.paymentMethod.id},{handleActions:!1});if(b)return c.complete("fail"),this.notifyListeners(s.Failed,b),l({paymentResult:s.Failed});if(h?.status==="requires_action"){const{error:P}=await f.confirmCardPayment(m);if(P)return c.complete("fail"),this.notifyListeners(s.Failed,P),l({paymentResult:s.Failed})}return c.complete("success"),this.notifyListeners(s.Completed,null),l({paymentResult:s.Completed})}),await i.initStripe(this.publishableKey,{stripeAccount:this.stripeAccount,showButton:!1})})}}export{x as StripeWeb};
