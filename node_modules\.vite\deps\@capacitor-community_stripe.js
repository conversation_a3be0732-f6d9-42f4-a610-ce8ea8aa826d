import {
  ApplePayEventsEnum,
  GooglePayEventsEnum,
  PaymentFlowEventsEnum,
  PaymentSheetEventsEnum
} from "./chunk-Q6Z3JMON.js";
import {
  registerPlugin
} from "./chunk-NXBGYFMC.js";
import "./chunk-G3PMV62Z.js";

// node_modules/@capacitor-community/stripe/dist/esm/index.js
var Stripe = registerPlugin("Stripe", {
  web: () => import("./web-Q7K72XWO.js").then((m) => new m.StripeWeb())
});
export {
  ApplePayEventsEnum,
  GooglePayEventsEnum,
  PaymentFlowEventsEnum,
  PaymentSheetEventsEnum,
  Stripe
};
//# sourceMappingURL=@capacitor-community_stripe.js.map
