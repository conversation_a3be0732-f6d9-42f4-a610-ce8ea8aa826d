import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { ShoppingCart, User, Search, Menu, X } from 'lucide-react'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { Input } from '../ui/Input'
import { useCart } from '../../context/CartContext'
import { useAuth } from '../../context/AuthContext'

export const Header = () => {
  const navigate = useNavigate()
  const { getTotalItems } = useCart()
  const { user, isAuthenticated } = useAuth()
  const totalItems = getTotalItems()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const handleUserClick = () => {
    if (isAuthenticated) {
      navigate('/my-profile')
    } else {
      navigate('/auth/login')
    }
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b border-gray-200 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="container flex h-16 items-center">
        {/* Logo */}
        <Link to="/" className="mr-6 flex items-center space-x-2">
          <div className="gradient-fuxion rounded-lg px-3 py-1.5">
            <span className="text-lg font-bold text-white">FuXion</span>
          </div>
        </Link>

        {/* Navigation Links - Desktop */}
        <nav className="hidden md:flex items-center space-x-6 text-sm font-medium">
          <Link
            to="/products"
            className="transition-colors hover:text-gray-900 text-gray-600"
          >
            Productos
          </Link>
          <Link
            to="/categories"
            className="transition-colors hover:text-gray-900 text-gray-600"
          >
            Categorías
          </Link>
          <Link
            to="/about"
            className="transition-colors hover:text-gray-900 text-gray-600"
          >
            Nosotros
          </Link>
        </nav>

        {/* Search Bar - Desktop */}
        <div className="hidden md:flex flex-1 max-w-sm mx-8">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Buscar productos..."
              className="pl-10"
            />
          </div>
        </div>

        {/* Right Side Actions */}
        <div className="flex flex-1 items-center justify-end space-x-2">
          {/* Mobile Search */}
          <Button variant="ghost" size="icon" className="md:hidden">
            <Search className="h-5 w-5" />
          </Button>

          {/* User Account */}
          <Button
            variant="ghost"
            size="icon"
            onClick={handleUserClick}
            className="relative"
          >
            {isAuthenticated && user?.photoURL ? (
              <img
                src={user.photoURL}
                alt={user.displayName}
                className="h-6 w-6 rounded-full object-cover"
              />
            ) : (
              <User className="h-5 w-5" />
            )}
            {isAuthenticated && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
            )}
          </Button>

          {/* Shopping Cart */}
          <Button
            variant="ghost"
            size="icon"
            className="relative"
            onClick={() => navigate('/cart')}
          >
            <ShoppingCart className="h-5 w-5" />
            {totalItems > 0 && (
              <Badge
                variant="default"
                className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
              >
                {totalItems}
              </Badge>
            )}
          </Button>

          {/* Mobile Menu Toggle */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden border-t border-gray-200 bg-white">
          <div className="container py-4 space-y-4">
            <div className="space-y-2">
              <Link
                to="/products"
                className="block px-3 py-2 text-sm font-medium transition-colors hover:text-gray-900 text-gray-600"
                onClick={() => setMobileMenuOpen(false)}
              >
                Productos
              </Link>
              <Link
                to="/categories"
                className="block px-3 py-2 text-sm font-medium transition-colors hover:text-gray-900 text-gray-600"
                onClick={() => setMobileMenuOpen(false)}
              >
                Categorías
              </Link>
              <Link
                to="/about"
                className="block px-3 py-2 text-sm font-medium transition-colors hover:text-gray-900 text-gray-600"
                onClick={() => setMobileMenuOpen(false)}
              >
                Nosotros
              </Link>
            </div>
            <div className="pt-2 border-t border-gray-200">
              <Input
                type="text"
                placeholder="Buscar productos..."
                className="w-full"
              />
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
