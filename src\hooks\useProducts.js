import { useState, useEffect } from 'react'
import { productsService } from '../services/firestore'
import { sampleProducts } from '../data/sampleProducts'

export const useProducts = (category = null) => {
  const [products, setProducts] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true)
        setError(null)

        let data
        try {
          // Intentar obtener datos de Firebase
          if (category) {
            data = await productsService.getByCategory(category)
          } else {
            data = await productsService.getAll()
          }

          // Si no hay datos de Firebase, usar datos de ejemplo
          if (!data || data.length === 0) {
            throw new Error('No Firebase data available')
          }
        } catch (firebaseError) {
          console.log('Using sample data:', firebaseError.message)
          // Usar datos de ejemplo
          if (category) {
            data = sampleProducts.filter(product => product.category === category)
          } else {
            data = sampleProducts
          }
        }

        setProducts(data)
      } catch (err) {
        setError(err.message)
        console.error('Error fetching products:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [category])

  return { products, loading, error }
}

export const useProduct = (id) => {
  const [product, setProduct] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchProduct = async () => {
      if (!id) return

      try {
        setLoading(true)
        setError(null)

        let data
        try {
          // Intentar obtener de Firebase
          data = await productsService.getById(id)
          if (!data) {
            throw new Error('Product not found in Firebase')
          }
        } catch (firebaseError) {
          console.log('Using sample data for product:', firebaseError.message)
          // Buscar en datos de ejemplo
          data = sampleProducts.find(product => product.id === id)
        }

        setProduct(data)
      } catch (err) {
        setError(err.message)
        console.error('Error fetching product:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchProduct()
  }, [id])

  return { product, loading, error }
}
