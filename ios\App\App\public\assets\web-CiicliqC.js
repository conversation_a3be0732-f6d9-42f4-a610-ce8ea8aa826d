import{W as m,U as o,V as F,X as d,Y as p,Z as u,_ as h,$ as w,a0 as Q,a1 as g,a2 as f,a3 as D,a4 as P,a5 as M,a6 as W,a7 as A,a8 as b,a9 as y,aa as C,ab as N,ac as v,ad as L,ae as k,af as B,ag as G,ah as x,ai as E,aj as I,ak as S}from"./index-Dlzwibkd.js";class j extends m{constructor(){super(...arguments),this.unsubscribesMap=new Map}async addDocument(e){const t=o(),{reference:r,data:a}=e,s=await F(d(t,r),a);return{reference:{id:s.id,path:s.path}}}async setDocument(e){const t=o(),{reference:r,data:a,merge:s}=e;await p(u(t,r),a,{merge:s})}async getDocument(e){const t=o(),{reference:r}=e,a=await h(u(t,r)),s=a.data();return{snapshot:{id:a.id,path:a.ref.path,data:s===void 0?null:s,metadata:{hasPendingWrites:a.metadata.hasPendingWrites,fromCache:a.metadata.fromCache}}}}async updateDocument(e){const t=o(),{reference:r,data:a}=e;await w(u(t,r),a)}async deleteDocument(e){const t=o(),{reference:r}=e;await Q(u(t,r))}async writeBatch(e){const t=o(),{operations:r}=e,a=g(t);for(const s of r){const{type:n,reference:c,data:i}=s,l=u(t,c);switch(n){case"set":a.set(l,i);break;case"update":a.update(l,i??{});break;case"delete":a.delete(l);break}}await a.commit()}async getCollection(e){const t=await this.buildCollectionQuery(e,"collection");return{snapshots:(await f(t)).docs.map(a=>({id:a.id,path:a.ref.path,data:a.data(),metadata:{hasPendingWrites:a.metadata.hasPendingWrites,fromCache:a.metadata.fromCache}}))}}async getCollectionGroup(e){const t=await this.buildCollectionQuery(e,"collectionGroup");return{snapshots:(await f(t)).docs.map(a=>({id:a.id,path:a.ref.path,data:a.data(),metadata:{hasPendingWrites:a.metadata.hasPendingWrites,fromCache:a.metadata.fromCache}}))}}async getCountFromServer(e){const t=o(),{reference:r}=e,a=d(t,r);return{count:(await D(a)).data().count}}async clearPersistence(){const e=o();await P(e)}async enableNetwork(){const e=o();await M(e)}async disableNetwork(){const e=o();await W(e)}async useEmulator(e){const t=o(),r=e.port||8080;A(t,e.host,r)}async addDocumentSnapshotListener(e,t){const r=o(),a=b(u(r,e.reference),{includeMetadataChanges:e.includeMetadataChanges,source:e.source},n=>{const c=n.data(),i={snapshot:{id:n.id,path:n.ref.path,data:c===void 0?null:c,metadata:{hasPendingWrites:n.metadata.hasPendingWrites,fromCache:n.metadata.fromCache}}};t(i,void 0)},n=>t(null,n)),s=Date.now().toString();return this.unsubscribesMap.set(s,a),s}async addCollectionSnapshotListener(e,t){const r=await this.buildCollectionQuery(e,"collection"),a=b(r,{includeMetadataChanges:e.includeMetadataChanges,source:e.source},n=>{const c={snapshots:n.docs.map(i=>({id:i.id,path:i.ref.path,data:i.data(),metadata:{hasPendingWrites:i.metadata.hasPendingWrites,fromCache:i.metadata.fromCache}}))};t(c,void 0)},n=>t(null,n)),s=Date.now().toString();return this.unsubscribesMap.set(s,a),s}async addCollectionGroupSnapshotListener(e,t){const r=await this.buildCollectionQuery(e,"collectionGroup"),a=b(r,{includeMetadataChanges:e.includeMetadataChanges,source:e.source},n=>{const c={snapshots:n.docs.map(i=>({id:i.id,path:i.ref.path,data:i.data(),metadata:{hasPendingWrites:i.metadata.hasPendingWrites,fromCache:i.metadata.fromCache}}))};t(c,void 0)},n=>t(null,n)),s=Date.now().toString();return this.unsubscribesMap.set(s,a),s}async removeSnapshotListener(e){const t=this.unsubscribesMap.get(e.callbackId);t&&(t(),this.unsubscribesMap.delete(e.callbackId))}async removeAllListeners(){this.unsubscribesMap.forEach(e=>e()),this.unsubscribesMap.clear(),await super.removeAllListeners()}async buildCollectionQuery(e,t){const r=o();let a;if(e.compositeFilter){const s=this.buildFirebaseQueryCompositeFilterConstraint(e.compositeFilter),n=await this.buildFirebaseQueryNonFilterConstraints(e.queryConstraints||[]);a=y(t==="collection"?d(r,e.reference):C(r,e.reference),s,...n)}else{const s=await this.buildFirebaseQueryConstraints(e.queryConstraints||[]);a=y(t==="collection"?d(r,e.reference):C(r,e.reference),...s)}return a}buildFirebaseQueryCompositeFilterConstraint(e){const t=this.buildFirebaseQueryFilterConstraints(e.queryConstraints);return e.type==="and"?N(...t):v(...t)}buildFirebaseQueryFilterConstraints(e){const t=[];for(const r of e){const a=this.buildFirebaseQueryFilterConstraint(r);t.push(a)}return t}buildFirebaseQueryFilterConstraint(e){return e.type==="where"?this.buildFirebaseQueryFieldFilterConstraint(e):this.buildFirebaseQueryCompositeFilterConstraint(e)}buildFirebaseQueryFieldFilterConstraint(e){return L(e.fieldPath,e.opStr,e.value)}async buildFirebaseQueryNonFilterConstraints(e){const t=[];for(const r of e){const a=await this.buildFirebaseQueryNonFilterConstraint(r);t.push(a)}return t}async buildFirebaseQueryNonFilterConstraint(e){switch(e.type){case"orderBy":return S(e.fieldPath,e.directionStr);case"limit":return I(e.limit);case"limitToLast":return E(e.limit);case"startAt":case"startAfter":case"endAt":case"endBefore":{const t=o(),r=await h(u(t,e.reference));switch(e.type){case"startAt":return x(r);case"startAfter":return G(r);case"endAt":return B(r);case"endBefore":return k(r)}}}}async buildFirebaseQueryConstraints(e){const t=[];for(const r of e){const a=await this.buildFirebaseQueryConstraint(r);t.push(a)}return t}async buildFirebaseQueryConstraint(e){return e.type==="where"?this.buildFirebaseQueryFieldFilterConstraint(e):await this.buildFirebaseQueryNonFilterConstraint(e)}}export{j as FirebaseFirestoreWeb};
