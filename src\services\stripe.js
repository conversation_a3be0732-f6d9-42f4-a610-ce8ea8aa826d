import { Stripe } from '@capacitor-community/stripe'

export const stripeService = {
  async initialize() {
    try {
      await Stripe.initialize({
        publishableKey: 'pk_test_your_publishable_key_here' // Replace with your actual publishable key
      })
    } catch (error) {
      console.error('Error initializing Stripe:', error)
      throw error
    }
  },

  async createPaymentSheet(amount, currency = 'usd') {
    try {
      // In a real app, you would call your backend to create a payment intent
      // For demo purposes, we'll simulate this
      const paymentIntent = {
        id: 'pi_' + Math.random().toString(36).substr(2, 9),
        client_secret: 'pi_' + Math.random().toString(36).substr(2, 9) + '_secret_' + Math.random().toString(36).substr(2, 9),
        amount: amount * 100, // Stripe expects amount in cents
        currency: currency
      }

      await Stripe.createPaymentSheet({
        paymentIntentClientSecret: paymentIntent.client_secret,
        merchantDisplayName: 'FuXion',
        style: 'alwaysDark',
        allowsDelayedPaymentMethods: true
      })

      return paymentIntent
    } catch (error) {
      console.error('Error creating payment sheet:', error)
      throw error
    }
  },

  async presentPaymentSheet() {
    try {
      const result = await Stripe.presentPaymentSheet()
      return result
    } catch (error) {
      console.error('Error presenting payment sheet:', error)
      throw error
    }
  },

  async confirmPaymentSheet() {
    try {
      const result = await Stripe.confirmPaymentSheet()
      return result
    } catch (error) {
      console.error('Error confirming payment:', error)
      throw error
    }
  }
}
