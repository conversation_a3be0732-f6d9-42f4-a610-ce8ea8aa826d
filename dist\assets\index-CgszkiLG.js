const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web-zIPXU54N.js","assets/index.esm2017-BXk3OJTf.js","assets/web-CkwtYyrc.js"])))=>i.map(i=>d[i]);
(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const f of document.querySelectorAll('link[rel="modulepreload"]'))o(f);new MutationObserver(f=>{for(const h of f)if(h.type==="childList")for(const x of h.addedNodes)x.tagName==="LINK"&&x.rel==="modulepreload"&&o(x)}).observe(document,{childList:!0,subtree:!0});function u(f){const h={};return f.integrity&&(h.integrity=f.integrity),f.referrerPolicy&&(h.referrerPolicy=f.referrerPolicy),f.crossOrigin==="use-credentials"?h.credentials="include":f.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function o(f){if(f.ep)return;f.ep=!0;const h=u(f);fetch(f.href,h)}})();function bp(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var su={exports:{}},ri={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zm;function jp(){if(zm)return ri;zm=1;var i=Symbol.for("react.transitional.element"),c=Symbol.for("react.fragment");function u(o,f,h){var x=null;if(h!==void 0&&(x=""+h),f.key!==void 0&&(x=""+f.key),"key"in f){h={};for(var v in f)v!=="key"&&(h[v]=f[v])}else h=f;return f=h.ref,{$$typeof:i,type:o,key:x,ref:f!==void 0?f:null,props:h}}return ri.Fragment=c,ri.jsx=u,ri.jsxs=u,ri}var Om;function Np(){return Om||(Om=1,su.exports=jp()),su.exports}var s=Np(),cu={exports:{}},se={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mm;function Sp(){if(Mm)return se;Mm=1;var i=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),x=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),E=Symbol.iterator;function j(N){return N===null||typeof N!="object"?null:(N=E&&N[E]||N["@@iterator"],typeof N=="function"?N:null)}var G={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,D={};function H(N,q,Q){this.props=N,this.context=q,this.refs=D,this.updater=Q||G}H.prototype.isReactComponent={},H.prototype.setState=function(N,q){if(typeof N!="object"&&typeof N!="function"&&N!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,N,q,"setState")},H.prototype.forceUpdate=function(N){this.updater.enqueueForceUpdate(this,N,"forceUpdate")};function z(){}z.prototype=H.prototype;function P(N,q,Q){this.props=N,this.context=q,this.refs=D,this.updater=Q||G}var I=P.prototype=new z;I.constructor=P,M(I,H.prototype),I.isPureReactComponent=!0;var ce=Array.isArray,F={H:null,A:null,T:null,S:null,V:null},ne=Object.prototype.hasOwnProperty;function pe(N,q,Q,Y,Z,fe){return Q=fe.ref,{$$typeof:i,type:N,key:q,ref:Q!==void 0?Q:null,props:fe}}function V(N,q){return pe(N.type,q,void 0,void 0,void 0,N.props)}function te(N){return typeof N=="object"&&N!==null&&N.$$typeof===i}function xe(N){var q={"=":"=0",":":"=2"};return"$"+N.replace(/[=:]/g,function(Q){return q[Q]})}var Ue=/\/+/g;function Se(N,q){return typeof N=="object"&&N!==null&&N.key!=null?xe(""+N.key):q.toString(36)}function Be(){}function lt(N){switch(N.status){case"fulfilled":return N.value;case"rejected":throw N.reason;default:switch(typeof N.status=="string"?N.then(Be,Be):(N.status="pending",N.then(function(q){N.status==="pending"&&(N.status="fulfilled",N.value=q)},function(q){N.status==="pending"&&(N.status="rejected",N.reason=q)})),N.status){case"fulfilled":return N.value;case"rejected":throw N.reason}}throw N}function Ce(N,q,Q,Y,Z){var fe=typeof N;(fe==="undefined"||fe==="boolean")&&(N=null);var re=!1;if(N===null)re=!0;else switch(fe){case"bigint":case"string":case"number":re=!0;break;case"object":switch(N.$$typeof){case i:case c:re=!0;break;case b:return re=N._init,Ce(re(N._payload),q,Q,Y,Z)}}if(re)return Z=Z(N),re=Y===""?"."+Se(N,0):Y,ce(Z)?(Q="",re!=null&&(Q=re.replace(Ue,"$&/")+"/"),Ce(Z,q,Q,"",function(yt){return yt})):Z!=null&&(te(Z)&&(Z=V(Z,Q+(Z.key==null||N&&N.key===Z.key?"":(""+Z.key).replace(Ue,"$&/")+"/")+re)),q.push(Z)),1;re=0;var be=Y===""?".":Y+":";if(ce(N))for(var Oe=0;Oe<N.length;Oe++)Y=N[Oe],fe=be+Se(Y,Oe),re+=Ce(Y,q,Q,fe,Z);else if(Oe=j(N),typeof Oe=="function")for(N=Oe.call(N),Oe=0;!(Y=N.next()).done;)Y=Y.value,fe=be+Se(Y,Oe++),re+=Ce(Y,q,Q,fe,Z);else if(fe==="object"){if(typeof N.then=="function")return Ce(lt(N),q,Q,Y,Z);throw q=String(N),Error("Objects are not valid as a React child (found: "+(q==="[object Object]"?"object with keys {"+Object.keys(N).join(", ")+"}":q)+"). If you meant to render a collection of children, use an array instead.")}return re}function _(N,q,Q){if(N==null)return N;var Y=[],Z=0;return Ce(N,Y,"","",function(fe){return q.call(Q,fe,Z++)}),Y}function X(N){if(N._status===-1){var q=N._result;q=q(),q.then(function(Q){(N._status===0||N._status===-1)&&(N._status=1,N._result=Q)},function(Q){(N._status===0||N._status===-1)&&(N._status=2,N._result=Q)}),N._status===-1&&(N._status=0,N._result=q)}if(N._status===1)return N._result.default;throw N._result}var B=typeof reportError=="function"?reportError:function(N){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof N=="object"&&N!==null&&typeof N.message=="string"?String(N.message):String(N),error:N});if(!window.dispatchEvent(q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",N);return}console.error(N)};function Ee(){}return se.Children={map:_,forEach:function(N,q,Q){_(N,function(){q.apply(this,arguments)},Q)},count:function(N){var q=0;return _(N,function(){q++}),q},toArray:function(N){return _(N,function(q){return q})||[]},only:function(N){if(!te(N))throw Error("React.Children.only expected to receive a single React element child.");return N}},se.Component=H,se.Fragment=u,se.Profiler=f,se.PureComponent=P,se.StrictMode=o,se.Suspense=p,se.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=F,se.__COMPILER_RUNTIME={__proto__:null,c:function(N){return F.H.useMemoCache(N)}},se.cache=function(N){return function(){return N.apply(null,arguments)}},se.cloneElement=function(N,q,Q){if(N==null)throw Error("The argument must be a React element, but you passed "+N+".");var Y=M({},N.props),Z=N.key,fe=void 0;if(q!=null)for(re in q.ref!==void 0&&(fe=void 0),q.key!==void 0&&(Z=""+q.key),q)!ne.call(q,re)||re==="key"||re==="__self"||re==="__source"||re==="ref"&&q.ref===void 0||(Y[re]=q[re]);var re=arguments.length-2;if(re===1)Y.children=Q;else if(1<re){for(var be=Array(re),Oe=0;Oe<re;Oe++)be[Oe]=arguments[Oe+2];Y.children=be}return pe(N.type,Z,void 0,void 0,fe,Y)},se.createContext=function(N){return N={$$typeof:x,_currentValue:N,_currentValue2:N,_threadCount:0,Provider:null,Consumer:null},N.Provider=N,N.Consumer={$$typeof:h,_context:N},N},se.createElement=function(N,q,Q){var Y,Z={},fe=null;if(q!=null)for(Y in q.key!==void 0&&(fe=""+q.key),q)ne.call(q,Y)&&Y!=="key"&&Y!=="__self"&&Y!=="__source"&&(Z[Y]=q[Y]);var re=arguments.length-2;if(re===1)Z.children=Q;else if(1<re){for(var be=Array(re),Oe=0;Oe<re;Oe++)be[Oe]=arguments[Oe+2];Z.children=be}if(N&&N.defaultProps)for(Y in re=N.defaultProps,re)Z[Y]===void 0&&(Z[Y]=re[Y]);return pe(N,fe,void 0,void 0,null,Z)},se.createRef=function(){return{current:null}},se.forwardRef=function(N){return{$$typeof:v,render:N}},se.isValidElement=te,se.lazy=function(N){return{$$typeof:b,_payload:{_status:-1,_result:N},_init:X}},se.memo=function(N,q){return{$$typeof:g,type:N,compare:q===void 0?null:q}},se.startTransition=function(N){var q=F.T,Q={};F.T=Q;try{var Y=N(),Z=F.S;Z!==null&&Z(Q,Y),typeof Y=="object"&&Y!==null&&typeof Y.then=="function"&&Y.then(Ee,B)}catch(fe){B(fe)}finally{F.T=q}},se.unstable_useCacheRefresh=function(){return F.H.useCacheRefresh()},se.use=function(N){return F.H.use(N)},se.useActionState=function(N,q,Q){return F.H.useActionState(N,q,Q)},se.useCallback=function(N,q){return F.H.useCallback(N,q)},se.useContext=function(N){return F.H.useContext(N)},se.useDebugValue=function(){},se.useDeferredValue=function(N,q){return F.H.useDeferredValue(N,q)},se.useEffect=function(N,q,Q){var Y=F.H;if(typeof Q=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Y.useEffect(N,q)},se.useId=function(){return F.H.useId()},se.useImperativeHandle=function(N,q,Q){return F.H.useImperativeHandle(N,q,Q)},se.useInsertionEffect=function(N,q){return F.H.useInsertionEffect(N,q)},se.useLayoutEffect=function(N,q){return F.H.useLayoutEffect(N,q)},se.useMemo=function(N,q){return F.H.useMemo(N,q)},se.useOptimistic=function(N,q){return F.H.useOptimistic(N,q)},se.useReducer=function(N,q,Q){return F.H.useReducer(N,q,Q)},se.useRef=function(N){return F.H.useRef(N)},se.useState=function(N){return F.H.useState(N)},se.useSyncExternalStore=function(N,q,Q){return F.H.useSyncExternalStore(N,q,Q)},se.useTransition=function(){return F.H.useTransition()},se.version="19.1.0",se}var _m;function Cu(){return _m||(_m=1,cu.exports=Sp()),cu.exports}var w=Cu();const ga=bp(w);var uu={exports:{}},si={},ou={exports:{}},du={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dm;function Ep(){return Dm||(Dm=1,function(i){function c(_,X){var B=_.length;_.push(X);e:for(;0<B;){var Ee=B-1>>>1,N=_[Ee];if(0<f(N,X))_[Ee]=X,_[B]=N,B=Ee;else break e}}function u(_){return _.length===0?null:_[0]}function o(_){if(_.length===0)return null;var X=_[0],B=_.pop();if(B!==X){_[0]=B;e:for(var Ee=0,N=_.length,q=N>>>1;Ee<q;){var Q=2*(Ee+1)-1,Y=_[Q],Z=Q+1,fe=_[Z];if(0>f(Y,B))Z<N&&0>f(fe,Y)?(_[Ee]=fe,_[Z]=B,Ee=Z):(_[Ee]=Y,_[Q]=B,Ee=Q);else if(Z<N&&0>f(fe,B))_[Ee]=fe,_[Z]=B,Ee=Z;else break e}}return X}function f(_,X){var B=_.sortIndex-X.sortIndex;return B!==0?B:_.id-X.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;i.unstable_now=function(){return h.now()}}else{var x=Date,v=x.now();i.unstable_now=function(){return x.now()-v}}var p=[],g=[],b=1,E=null,j=3,G=!1,M=!1,D=!1,H=!1,z=typeof setTimeout=="function"?setTimeout:null,P=typeof clearTimeout=="function"?clearTimeout:null,I=typeof setImmediate<"u"?setImmediate:null;function ce(_){for(var X=u(g);X!==null;){if(X.callback===null)o(g);else if(X.startTime<=_)o(g),X.sortIndex=X.expirationTime,c(p,X);else break;X=u(g)}}function F(_){if(D=!1,ce(_),!M)if(u(p)!==null)M=!0,ne||(ne=!0,Se());else{var X=u(g);X!==null&&Ce(F,X.startTime-_)}}var ne=!1,pe=-1,V=5,te=-1;function xe(){return H?!0:!(i.unstable_now()-te<V)}function Ue(){if(H=!1,ne){var _=i.unstable_now();te=_;var X=!0;try{e:{M=!1,D&&(D=!1,P(pe),pe=-1),G=!0;var B=j;try{t:{for(ce(_),E=u(p);E!==null&&!(E.expirationTime>_&&xe());){var Ee=E.callback;if(typeof Ee=="function"){E.callback=null,j=E.priorityLevel;var N=Ee(E.expirationTime<=_);if(_=i.unstable_now(),typeof N=="function"){E.callback=N,ce(_),X=!0;break t}E===u(p)&&o(p),ce(_)}else o(p);E=u(p)}if(E!==null)X=!0;else{var q=u(g);q!==null&&Ce(F,q.startTime-_),X=!1}}break e}finally{E=null,j=B,G=!1}X=void 0}}finally{X?Se():ne=!1}}}var Se;if(typeof I=="function")Se=function(){I(Ue)};else if(typeof MessageChannel<"u"){var Be=new MessageChannel,lt=Be.port2;Be.port1.onmessage=Ue,Se=function(){lt.postMessage(null)}}else Se=function(){z(Ue,0)};function Ce(_,X){pe=z(function(){_(i.unstable_now())},X)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(_){_.callback=null},i.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):V=0<_?Math.floor(1e3/_):5},i.unstable_getCurrentPriorityLevel=function(){return j},i.unstable_next=function(_){switch(j){case 1:case 2:case 3:var X=3;break;default:X=j}var B=j;j=X;try{return _()}finally{j=B}},i.unstable_requestPaint=function(){H=!0},i.unstable_runWithPriority=function(_,X){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var B=j;j=_;try{return X()}finally{j=B}},i.unstable_scheduleCallback=function(_,X,B){var Ee=i.unstable_now();switch(typeof B=="object"&&B!==null?(B=B.delay,B=typeof B=="number"&&0<B?Ee+B:Ee):B=Ee,_){case 1:var N=-1;break;case 2:N=250;break;case 5:N=1073741823;break;case 4:N=1e4;break;default:N=5e3}return N=B+N,_={id:b++,callback:X,priorityLevel:_,startTime:B,expirationTime:N,sortIndex:-1},B>Ee?(_.sortIndex=B,c(g,_),u(p)===null&&_===u(g)&&(D?(P(pe),pe=-1):D=!0,Ce(F,B-Ee))):(_.sortIndex=N,c(p,_),M||G||(M=!0,ne||(ne=!0,Se()))),_},i.unstable_shouldYield=xe,i.unstable_wrapCallback=function(_){var X=j;return function(){var B=j;j=X;try{return _.apply(this,arguments)}finally{j=B}}}}(du)),du}var Um;function wp(){return Um||(Um=1,ou.exports=Ep()),ou.exports}var fu={exports:{}},rt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lm;function Ap(){if(Lm)return rt;Lm=1;var i=Cu();function c(p){var g="https://react.dev/errors/"+p;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var b=2;b<arguments.length;b++)g+="&args[]="+encodeURIComponent(arguments[b])}return"Minified React error #"+p+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var o={d:{f:u,r:function(){throw Error(c(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},f=Symbol.for("react.portal");function h(p,g,b){var E=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:f,key:E==null?null:""+E,children:p,containerInfo:g,implementation:b}}var x=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function v(p,g){if(p==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return rt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,rt.createPortal=function(p,g){var b=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(c(299));return h(p,g,null,b)},rt.flushSync=function(p){var g=x.T,b=o.p;try{if(x.T=null,o.p=2,p)return p()}finally{x.T=g,o.p=b,o.d.f()}},rt.preconnect=function(p,g){typeof p=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,o.d.C(p,g))},rt.prefetchDNS=function(p){typeof p=="string"&&o.d.D(p)},rt.preinit=function(p,g){if(typeof p=="string"&&g&&typeof g.as=="string"){var b=g.as,E=v(b,g.crossOrigin),j=typeof g.integrity=="string"?g.integrity:void 0,G=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;b==="style"?o.d.S(p,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:E,integrity:j,fetchPriority:G}):b==="script"&&o.d.X(p,{crossOrigin:E,integrity:j,fetchPriority:G,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},rt.preinitModule=function(p,g){if(typeof p=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var b=v(g.as,g.crossOrigin);o.d.M(p,{crossOrigin:b,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&o.d.M(p)},rt.preload=function(p,g){if(typeof p=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var b=g.as,E=v(b,g.crossOrigin);o.d.L(p,b,{crossOrigin:E,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},rt.preloadModule=function(p,g){if(typeof p=="string")if(g){var b=v(g.as,g.crossOrigin);o.d.m(p,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:b,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else o.d.m(p)},rt.requestFormReset=function(p){o.d.r(p)},rt.unstable_batchedUpdates=function(p,g){return p(g)},rt.useFormState=function(p,g,b){return x.H.useFormState(p,g,b)},rt.useFormStatus=function(){return x.H.useHostTransitionStatus()},rt.version="19.1.0",rt}var Hm;function Cp(){if(Hm)return fu.exports;Hm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(c){console.error(c)}}return i(),fu.exports=Ap(),fu.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var km;function Tp(){if(km)return si;km=1;var i=wp(),c=Cu(),u=Cp();function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function h(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function x(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function v(e){if(h(e)!==e)throw Error(o(188))}function p(e){var t=e.alternate;if(!t){if(t=h(e),t===null)throw Error(o(188));return t!==e?null:e}for(var a=e,l=t;;){var n=a.return;if(n===null)break;var r=n.alternate;if(r===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===r.child){for(r=n.child;r;){if(r===a)return v(n),e;if(r===l)return v(n),t;r=r.sibling}throw Error(o(188))}if(a.return!==l.return)a=n,l=r;else{for(var d=!1,m=n.child;m;){if(m===a){d=!0,a=n,l=r;break}if(m===l){d=!0,l=n,a=r;break}m=m.sibling}if(!d){for(m=r.child;m;){if(m===a){d=!0,a=r,l=n;break}if(m===l){d=!0,l=r,a=n;break}m=m.sibling}if(!d)throw Error(o(189))}}if(a.alternate!==l)throw Error(o(190))}if(a.tag!==3)throw Error(o(188));return a.stateNode.current===a?e:t}function g(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=g(e),t!==null)return t;e=e.sibling}return null}var b=Object.assign,E=Symbol.for("react.element"),j=Symbol.for("react.transitional.element"),G=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),D=Symbol.for("react.strict_mode"),H=Symbol.for("react.profiler"),z=Symbol.for("react.provider"),P=Symbol.for("react.consumer"),I=Symbol.for("react.context"),ce=Symbol.for("react.forward_ref"),F=Symbol.for("react.suspense"),ne=Symbol.for("react.suspense_list"),pe=Symbol.for("react.memo"),V=Symbol.for("react.lazy"),te=Symbol.for("react.activity"),xe=Symbol.for("react.memo_cache_sentinel"),Ue=Symbol.iterator;function Se(e){return e===null||typeof e!="object"?null:(e=Ue&&e[Ue]||e["@@iterator"],typeof e=="function"?e:null)}var Be=Symbol.for("react.client.reference");function lt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Be?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case M:return"Fragment";case H:return"Profiler";case D:return"StrictMode";case F:return"Suspense";case ne:return"SuspenseList";case te:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case G:return"Portal";case I:return(e.displayName||"Context")+".Provider";case P:return(e._context.displayName||"Context")+".Consumer";case ce:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case pe:return t=e.displayName||null,t!==null?t:lt(e.type)||"Memo";case V:t=e._payload,e=e._init;try{return lt(e(t))}catch{}}return null}var Ce=Array.isArray,_=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,B={pending:!1,data:null,method:null,action:null},Ee=[],N=-1;function q(e){return{current:e}}function Q(e){0>N||(e.current=Ee[N],Ee[N]=null,N--)}function Y(e,t){N++,Ee[N]=e.current,e.current=t}var Z=q(null),fe=q(null),re=q(null),be=q(null);function Oe(e,t){switch(Y(re,t),Y(fe,e),Y(Z,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?nm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=nm(t),e=im(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}Q(Z),Y(Z,e)}function yt(){Q(Z),Q(fe),Q(re)}function xa(e){e.memoizedState!==null&&Y(be,e);var t=Z.current,a=im(t,e.type);t!==a&&(Y(fe,e),Y(Z,a))}function ya(e){fe.current===e&&(Q(Z),Q(fe)),be.current===e&&(Q(be),ti._currentValue=B)}var va=Object.prototype.hasOwnProperty,Jr=i.unstable_scheduleCallback,$r=i.unstable_cancelCallback,Ph=i.unstable_shouldYield,Ih=i.unstable_requestPaint,Yt=i.unstable_now,eg=i.unstable_getCurrentPriorityLevel,Lu=i.unstable_ImmediatePriority,Hu=i.unstable_UserBlockingPriority,xi=i.unstable_NormalPriority,tg=i.unstable_LowPriority,ku=i.unstable_IdlePriority,ag=i.log,lg=i.unstable_setDisableYieldValue,un=null,vt=null;function ba(e){if(typeof ag=="function"&&lg(e),vt&&typeof vt.setStrictMode=="function")try{vt.setStrictMode(un,e)}catch{}}var bt=Math.clz32?Math.clz32:rg,ng=Math.log,ig=Math.LN2;function rg(e){return e>>>=0,e===0?32:31-(ng(e)/ig|0)|0}var yi=256,vi=4194304;function Ja(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function bi(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var n=0,r=e.suspendedLanes,d=e.pingedLanes;e=e.warmLanes;var m=l&134217727;return m!==0?(l=m&~r,l!==0?n=Ja(l):(d&=m,d!==0?n=Ja(d):a||(a=m&~e,a!==0&&(n=Ja(a))))):(m=l&~r,m!==0?n=Ja(m):d!==0?n=Ja(d):a||(a=l&~e,a!==0&&(n=Ja(a)))),n===0?0:t!==0&&t!==n&&(t&r)===0&&(r=n&-n,a=t&-t,r>=a||r===32&&(a&4194048)!==0)?t:n}function on(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function sg(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function qu(){var e=yi;return yi<<=1,(yi&4194048)===0&&(yi=256),e}function Bu(){var e=vi;return vi<<=1,(vi&62914560)===0&&(vi=4194304),e}function Fr(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function dn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function cg(e,t,a,l,n,r){var d=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var m=e.entanglements,y=e.expirationTimes,T=e.hiddenUpdates;for(a=d&~a;0<a;){var U=31-bt(a),k=1<<U;m[U]=0,y[U]=-1;var R=T[U];if(R!==null)for(T[U]=null,U=0;U<R.length;U++){var O=R[U];O!==null&&(O.lane&=-536870913)}a&=~k}l!==0&&Gu(e,l,0),r!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=r&~(d&~t))}function Gu(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-bt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function Yu(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-bt(a),n=1<<l;n&t|e[l]&t&&(e[l]|=t),a&=~n}}function Wr(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Pr(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Vu(){var e=X.p;return e!==0?e:(e=window.event,e===void 0?32:Em(e.type))}function ug(e,t){var a=X.p;try{return X.p=e,t()}finally{X.p=a}}var ja=Math.random().toString(36).slice(2),nt="__reactFiber$"+ja,ft="__reactProps$"+ja,hl="__reactContainer$"+ja,Ir="__reactEvents$"+ja,og="__reactListeners$"+ja,dg="__reactHandles$"+ja,Xu="__reactResources$"+ja,fn="__reactMarker$"+ja;function es(e){delete e[nt],delete e[ft],delete e[Ir],delete e[og],delete e[dg]}function gl(e){var t=e[nt];if(t)return t;for(var a=e.parentNode;a;){if(t=a[hl]||a[nt]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=um(e);e!==null;){if(a=e[nt])return a;e=um(e)}return t}e=a,a=e.parentNode}return null}function pl(e){if(e=e[nt]||e[hl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function mn(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(o(33))}function xl(e){var t=e[Xu];return t||(t=e[Xu]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Fe(e){e[fn]=!0}var Qu=new Set,Zu={};function $a(e,t){yl(e,t),yl(e+"Capture",t)}function yl(e,t){for(Zu[e]=t,e=0;e<t.length;e++)Qu.add(t[e])}var fg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ku={},Ju={};function mg(e){return va.call(Ju,e)?!0:va.call(Ku,e)?!1:fg.test(e)?Ju[e]=!0:(Ku[e]=!0,!1)}function ji(e,t,a){if(mg(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Ni(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Wt(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var ts,$u;function vl(e){if(ts===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);ts=t&&t[1]||"",$u=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ts+e+$u}var as=!1;function ls(e,t){if(!e||as)return"";as=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var k=function(){throw Error()};if(Object.defineProperty(k.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(k,[])}catch(O){var R=O}Reflect.construct(e,[],k)}else{try{k.call()}catch(O){R=O}e.call(k.prototype)}}else{try{throw Error()}catch(O){R=O}(k=e())&&typeof k.catch=="function"&&k.catch(function(){})}}catch(O){if(O&&R&&typeof O.stack=="string")return[O.stack,R.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=l.DetermineComponentFrameRoot(),d=r[0],m=r[1];if(d&&m){var y=d.split(`
`),T=m.split(`
`);for(n=l=0;l<y.length&&!y[l].includes("DetermineComponentFrameRoot");)l++;for(;n<T.length&&!T[n].includes("DetermineComponentFrameRoot");)n++;if(l===y.length||n===T.length)for(l=y.length-1,n=T.length-1;1<=l&&0<=n&&y[l]!==T[n];)n--;for(;1<=l&&0<=n;l--,n--)if(y[l]!==T[n]){if(l!==1||n!==1)do if(l--,n--,0>n||y[l]!==T[n]){var U=`
`+y[l].replace(" at new "," at ");return e.displayName&&U.includes("<anonymous>")&&(U=U.replace("<anonymous>",e.displayName)),U}while(1<=l&&0<=n);break}}}finally{as=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?vl(a):""}function hg(e){switch(e.tag){case 26:case 27:case 5:return vl(e.type);case 16:return vl("Lazy");case 13:return vl("Suspense");case 19:return vl("SuspenseList");case 0:case 15:return ls(e.type,!1);case 11:return ls(e.type.render,!1);case 1:return ls(e.type,!0);case 31:return vl("Activity");default:return""}}function Fu(e){try{var t="";do t+=hg(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Tt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Wu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function gg(e){var t=Wu(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,r=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(d){l=""+d,r.call(this,d)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(d){l=""+d},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Si(e){e._valueTracker||(e._valueTracker=gg(e))}function Pu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=Wu(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function Ei(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var pg=/[\n"\\]/g;function Rt(e){return e.replace(pg,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function ns(e,t,a,l,n,r,d,m){e.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?e.type=d:e.removeAttribute("type"),t!=null?d==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Tt(t)):e.value!==""+Tt(t)&&(e.value=""+Tt(t)):d!=="submit"&&d!=="reset"||e.removeAttribute("value"),t!=null?is(e,d,Tt(t)):a!=null?is(e,d,Tt(a)):l!=null&&e.removeAttribute("value"),n==null&&r!=null&&(e.defaultChecked=!!r),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.name=""+Tt(m):e.removeAttribute("name")}function Iu(e,t,a,l,n,r,d,m){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.type=r),t!=null||a!=null){if(!(r!=="submit"&&r!=="reset"||t!=null))return;a=a!=null?""+Tt(a):"",t=t!=null?""+Tt(t):a,m||t===e.value||(e.value=t),e.defaultValue=t}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=m?e.checked:!!l,e.defaultChecked=!!l,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.name=d)}function is(e,t,a){t==="number"&&Ei(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function bl(e,t,a,l){if(e=e.options,t){t={};for(var n=0;n<a.length;n++)t["$"+a[n]]=!0;for(a=0;a<e.length;a++)n=t.hasOwnProperty("$"+e[a].value),e[a].selected!==n&&(e[a].selected=n),n&&l&&(e[a].defaultSelected=!0)}else{for(a=""+Tt(a),t=null,n=0;n<e.length;n++){if(e[n].value===a){e[n].selected=!0,l&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function eo(e,t,a){if(t!=null&&(t=""+Tt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Tt(a):""}function to(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(o(92));if(Ce(l)){if(1<l.length)throw Error(o(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=Tt(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function jl(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var xg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ao(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||xg.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function lo(e,t,a){if(t!=null&&typeof t!="object")throw Error(o(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var n in t)l=t[n],t.hasOwnProperty(n)&&a[n]!==l&&ao(e,n,l)}else for(var r in t)t.hasOwnProperty(r)&&ao(e,r,t[r])}function rs(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var yg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),vg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function wi(e){return vg.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var ss=null;function cs(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Nl=null,Sl=null;function no(e){var t=pl(e);if(t&&(e=t.stateNode)){var a=e[ft]||null;e:switch(e=t.stateNode,t.type){case"input":if(ns(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Rt(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var n=l[ft]||null;if(!n)throw Error(o(90));ns(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&Pu(l)}break e;case"textarea":eo(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&bl(e,!!a.multiple,t,!1)}}}var us=!1;function io(e,t,a){if(us)return e(t,a);us=!0;try{var l=e(t);return l}finally{if(us=!1,(Nl!==null||Sl!==null)&&(or(),Nl&&(t=Nl,e=Sl,Sl=Nl=null,no(t),e)))for(t=0;t<e.length;t++)no(e[t])}}function hn(e,t){var a=e.stateNode;if(a===null)return null;var l=a[ft]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(o(231,t,typeof a));return a}var Pt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),os=!1;if(Pt)try{var gn={};Object.defineProperty(gn,"passive",{get:function(){os=!0}}),window.addEventListener("test",gn,gn),window.removeEventListener("test",gn,gn)}catch{os=!1}var Na=null,ds=null,Ai=null;function ro(){if(Ai)return Ai;var e,t=ds,a=t.length,l,n="value"in Na?Na.value:Na.textContent,r=n.length;for(e=0;e<a&&t[e]===n[e];e++);var d=a-e;for(l=1;l<=d&&t[a-l]===n[r-l];l++);return Ai=n.slice(e,1<l?1-l:void 0)}function Ci(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ti(){return!0}function so(){return!1}function mt(e){function t(a,l,n,r,d){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=r,this.target=d,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(a=e[m],this[m]=a?a(r):r[m]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Ti:so,this.isPropagationStopped=so,this}return b(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Ti)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Ti)},persist:function(){},isPersistent:Ti}),t}var Fa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ri=mt(Fa),pn=b({},Fa,{view:0,detail:0}),bg=mt(pn),fs,ms,xn,zi=b({},pn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==xn&&(xn&&e.type==="mousemove"?(fs=e.screenX-xn.screenX,ms=e.screenY-xn.screenY):ms=fs=0,xn=e),fs)},movementY:function(e){return"movementY"in e?e.movementY:ms}}),co=mt(zi),jg=b({},zi,{dataTransfer:0}),Ng=mt(jg),Sg=b({},pn,{relatedTarget:0}),hs=mt(Sg),Eg=b({},Fa,{animationName:0,elapsedTime:0,pseudoElement:0}),wg=mt(Eg),Ag=b({},Fa,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Cg=mt(Ag),Tg=b({},Fa,{data:0}),uo=mt(Tg),Rg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},zg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Og={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Mg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Og[e])?!!t[e]:!1}function gs(){return Mg}var _g=b({},pn,{key:function(e){if(e.key){var t=Rg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ci(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?zg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gs,charCode:function(e){return e.type==="keypress"?Ci(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ci(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Dg=mt(_g),Ug=b({},zi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),oo=mt(Ug),Lg=b({},pn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gs}),Hg=mt(Lg),kg=b({},Fa,{propertyName:0,elapsedTime:0,pseudoElement:0}),qg=mt(kg),Bg=b({},zi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Gg=mt(Bg),Yg=b({},Fa,{newState:0,oldState:0}),Vg=mt(Yg),Xg=[9,13,27,32],ps=Pt&&"CompositionEvent"in window,yn=null;Pt&&"documentMode"in document&&(yn=document.documentMode);var Qg=Pt&&"TextEvent"in window&&!yn,fo=Pt&&(!ps||yn&&8<yn&&11>=yn),mo=" ",ho=!1;function go(e,t){switch(e){case"keyup":return Xg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function po(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var El=!1;function Zg(e,t){switch(e){case"compositionend":return po(t);case"keypress":return t.which!==32?null:(ho=!0,mo);case"textInput":return e=t.data,e===mo&&ho?null:e;default:return null}}function Kg(e,t){if(El)return e==="compositionend"||!ps&&go(e,t)?(e=ro(),Ai=ds=Na=null,El=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return fo&&t.locale!=="ko"?null:t.data;default:return null}}var Jg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function xo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Jg[e.type]:t==="textarea"}function yo(e,t,a,l){Nl?Sl?Sl.push(l):Sl=[l]:Nl=l,t=pr(t,"onChange"),0<t.length&&(a=new Ri("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var vn=null,bn=null;function $g(e){If(e,0)}function Oi(e){var t=mn(e);if(Pu(t))return e}function vo(e,t){if(e==="change")return t}var bo=!1;if(Pt){var xs;if(Pt){var ys="oninput"in document;if(!ys){var jo=document.createElement("div");jo.setAttribute("oninput","return;"),ys=typeof jo.oninput=="function"}xs=ys}else xs=!1;bo=xs&&(!document.documentMode||9<document.documentMode)}function No(){vn&&(vn.detachEvent("onpropertychange",So),bn=vn=null)}function So(e){if(e.propertyName==="value"&&Oi(bn)){var t=[];yo(t,bn,e,cs(e)),io($g,t)}}function Fg(e,t,a){e==="focusin"?(No(),vn=t,bn=a,vn.attachEvent("onpropertychange",So)):e==="focusout"&&No()}function Wg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Oi(bn)}function Pg(e,t){if(e==="click")return Oi(t)}function Ig(e,t){if(e==="input"||e==="change")return Oi(t)}function e0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var jt=typeof Object.is=="function"?Object.is:e0;function jn(e,t){if(jt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!va.call(t,n)||!jt(e[n],t[n]))return!1}return!0}function Eo(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wo(e,t){var a=Eo(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Eo(a)}}function Ao(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ao(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Co(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Ei(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Ei(e.document)}return t}function vs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var t0=Pt&&"documentMode"in document&&11>=document.documentMode,wl=null,bs=null,Nn=null,js=!1;function To(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;js||wl==null||wl!==Ei(l)||(l=wl,"selectionStart"in l&&vs(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Nn&&jn(Nn,l)||(Nn=l,l=pr(bs,"onSelect"),0<l.length&&(t=new Ri("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=wl)))}function Wa(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var Al={animationend:Wa("Animation","AnimationEnd"),animationiteration:Wa("Animation","AnimationIteration"),animationstart:Wa("Animation","AnimationStart"),transitionrun:Wa("Transition","TransitionRun"),transitionstart:Wa("Transition","TransitionStart"),transitioncancel:Wa("Transition","TransitionCancel"),transitionend:Wa("Transition","TransitionEnd")},Ns={},Ro={};Pt&&(Ro=document.createElement("div").style,"AnimationEvent"in window||(delete Al.animationend.animation,delete Al.animationiteration.animation,delete Al.animationstart.animation),"TransitionEvent"in window||delete Al.transitionend.transition);function Pa(e){if(Ns[e])return Ns[e];if(!Al[e])return e;var t=Al[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in Ro)return Ns[e]=t[a];return e}var zo=Pa("animationend"),Oo=Pa("animationiteration"),Mo=Pa("animationstart"),a0=Pa("transitionrun"),l0=Pa("transitionstart"),n0=Pa("transitioncancel"),_o=Pa("transitionend"),Do=new Map,Ss="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ss.push("scrollEnd");function kt(e,t){Do.set(e,t),$a(t,[e])}var Uo=new WeakMap;function zt(e,t){if(typeof e=="object"&&e!==null){var a=Uo.get(e);return a!==void 0?a:(t={value:e,source:t,stack:Fu(t)},Uo.set(e,t),t)}return{value:e,source:t,stack:Fu(t)}}var Ot=[],Cl=0,Es=0;function Mi(){for(var e=Cl,t=Es=Cl=0;t<e;){var a=Ot[t];Ot[t++]=null;var l=Ot[t];Ot[t++]=null;var n=Ot[t];Ot[t++]=null;var r=Ot[t];if(Ot[t++]=null,l!==null&&n!==null){var d=l.pending;d===null?n.next=n:(n.next=d.next,d.next=n),l.pending=n}r!==0&&Lo(a,n,r)}}function _i(e,t,a,l){Ot[Cl++]=e,Ot[Cl++]=t,Ot[Cl++]=a,Ot[Cl++]=l,Es|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function ws(e,t,a,l){return _i(e,t,a,l),Di(e)}function Tl(e,t){return _i(e,null,null,t),Di(e)}function Lo(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var n=!1,r=e.return;r!==null;)r.childLanes|=a,l=r.alternate,l!==null&&(l.childLanes|=a),r.tag===22&&(e=r.stateNode,e===null||e._visibility&1||(n=!0)),e=r,r=r.return;return e.tag===3?(r=e.stateNode,n&&t!==null&&(n=31-bt(a),e=r.hiddenUpdates,l=e[n],l===null?e[n]=[t]:l.push(t),t.lane=a|536870912),r):null}function Di(e){if(50<Kn)throw Kn=0,Oc=null,Error(o(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Rl={};function i0(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Nt(e,t,a,l){return new i0(e,t,a,l)}function As(e){return e=e.prototype,!(!e||!e.isReactComponent)}function It(e,t){var a=e.alternate;return a===null?(a=Nt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Ho(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ui(e,t,a,l,n,r){var d=0;if(l=e,typeof e=="function")As(e)&&(d=1);else if(typeof e=="string")d=sp(e,a,Z.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case te:return e=Nt(31,a,t,n),e.elementType=te,e.lanes=r,e;case M:return Ia(a.children,n,r,t);case D:d=8,n|=24;break;case H:return e=Nt(12,a,t,n|2),e.elementType=H,e.lanes=r,e;case F:return e=Nt(13,a,t,n),e.elementType=F,e.lanes=r,e;case ne:return e=Nt(19,a,t,n),e.elementType=ne,e.lanes=r,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case z:case I:d=10;break e;case P:d=9;break e;case ce:d=11;break e;case pe:d=14;break e;case V:d=16,l=null;break e}d=29,a=Error(o(130,e===null?"null":typeof e,"")),l=null}return t=Nt(d,a,t,n),t.elementType=e,t.type=l,t.lanes=r,t}function Ia(e,t,a,l){return e=Nt(7,e,l,t),e.lanes=a,e}function Cs(e,t,a){return e=Nt(6,e,null,t),e.lanes=a,e}function Ts(e,t,a){return t=Nt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var zl=[],Ol=0,Li=null,Hi=0,Mt=[],_t=0,el=null,ea=1,ta="";function tl(e,t){zl[Ol++]=Hi,zl[Ol++]=Li,Li=e,Hi=t}function ko(e,t,a){Mt[_t++]=ea,Mt[_t++]=ta,Mt[_t++]=el,el=e;var l=ea;e=ta;var n=32-bt(l)-1;l&=~(1<<n),a+=1;var r=32-bt(t)+n;if(30<r){var d=n-n%5;r=(l&(1<<d)-1).toString(32),l>>=d,n-=d,ea=1<<32-bt(t)+n|a<<n|l,ta=r+e}else ea=1<<r|a<<n|l,ta=e}function Rs(e){e.return!==null&&(tl(e,1),ko(e,1,0))}function zs(e){for(;e===Li;)Li=zl[--Ol],zl[Ol]=null,Hi=zl[--Ol],zl[Ol]=null;for(;e===el;)el=Mt[--_t],Mt[_t]=null,ta=Mt[--_t],Mt[_t]=null,ea=Mt[--_t],Mt[_t]=null}var ot=null,Ge=null,Ne=!1,al=null,Vt=!1,Os=Error(o(519));function ll(e){var t=Error(o(418,""));throw wn(zt(t,e)),Os}function qo(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[nt]=e,t[ft]=l,a){case"dialog":he("cancel",t),he("close",t);break;case"iframe":case"object":case"embed":he("load",t);break;case"video":case"audio":for(a=0;a<$n.length;a++)he($n[a],t);break;case"source":he("error",t);break;case"img":case"image":case"link":he("error",t),he("load",t);break;case"details":he("toggle",t);break;case"input":he("invalid",t),Iu(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Si(t);break;case"select":he("invalid",t);break;case"textarea":he("invalid",t),to(t,l.value,l.defaultValue,l.children),Si(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||lm(t.textContent,a)?(l.popover!=null&&(he("beforetoggle",t),he("toggle",t)),l.onScroll!=null&&he("scroll",t),l.onScrollEnd!=null&&he("scrollend",t),l.onClick!=null&&(t.onclick=xr),t=!0):t=!1,t||ll(e)}function Bo(e){for(ot=e.return;ot;)switch(ot.tag){case 5:case 13:Vt=!1;return;case 27:case 3:Vt=!0;return;default:ot=ot.return}}function Sn(e){if(e!==ot)return!1;if(!Ne)return Bo(e),Ne=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Kc(e.type,e.memoizedProps)),a=!a),a&&Ge&&ll(e),Bo(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Ge=Bt(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Ge=null}}else t===27?(t=Ge,ka(e.type)?(e=Wc,Wc=null,Ge=e):Ge=t):Ge=ot?Bt(e.stateNode.nextSibling):null;return!0}function En(){Ge=ot=null,Ne=!1}function Go(){var e=al;return e!==null&&(pt===null?pt=e:pt.push.apply(pt,e),al=null),e}function wn(e){al===null?al=[e]:al.push(e)}var Ms=q(null),nl=null,aa=null;function Sa(e,t,a){Y(Ms,t._currentValue),t._currentValue=a}function la(e){e._currentValue=Ms.current,Q(Ms)}function _s(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Ds(e,t,a,l){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var r=n.dependencies;if(r!==null){var d=n.child;r=r.firstContext;e:for(;r!==null;){var m=r;r=n;for(var y=0;y<t.length;y++)if(m.context===t[y]){r.lanes|=a,m=r.alternate,m!==null&&(m.lanes|=a),_s(r.return,a,e),l||(d=null);break e}r=m.next}}else if(n.tag===18){if(d=n.return,d===null)throw Error(o(341));d.lanes|=a,r=d.alternate,r!==null&&(r.lanes|=a),_s(d,a,e),d=null}else d=n.child;if(d!==null)d.return=n;else for(d=n;d!==null;){if(d===e){d=null;break}if(n=d.sibling,n!==null){n.return=d.return,d=n;break}d=d.return}n=d}}function An(e,t,a,l){e=null;for(var n=t,r=!1;n!==null;){if(!r){if((n.flags&524288)!==0)r=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var d=n.alternate;if(d===null)throw Error(o(387));if(d=d.memoizedProps,d!==null){var m=n.type;jt(n.pendingProps.value,d.value)||(e!==null?e.push(m):e=[m])}}else if(n===be.current){if(d=n.alternate,d===null)throw Error(o(387));d.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(ti):e=[ti])}n=n.return}e!==null&&Ds(t,e,a,l),t.flags|=262144}function ki(e){for(e=e.firstContext;e!==null;){if(!jt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function il(e){nl=e,aa=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function it(e){return Yo(nl,e)}function qi(e,t){return nl===null&&il(e),Yo(e,t)}function Yo(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},aa===null){if(e===null)throw Error(o(308));aa=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else aa=aa.next=t;return a}var r0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},s0=i.unstable_scheduleCallback,c0=i.unstable_NormalPriority,Ke={$$typeof:I,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Us(){return{controller:new r0,data:new Map,refCount:0}}function Cn(e){e.refCount--,e.refCount===0&&s0(c0,function(){e.controller.abort()})}var Tn=null,Ls=0,Ml=0,_l=null;function u0(e,t){if(Tn===null){var a=Tn=[];Ls=0,Ml=kc(),_l={status:"pending",value:void 0,then:function(l){a.push(l)}}}return Ls++,t.then(Vo,Vo),t}function Vo(){if(--Ls===0&&Tn!==null){_l!==null&&(_l.status="fulfilled");var e=Tn;Tn=null,Ml=0,_l=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function o0(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var n=0;n<a.length;n++)(0,a[n])(t)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var Xo=_.S;_.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&u0(e,t),Xo!==null&&Xo(e,t)};var rl=q(null);function Hs(){var e=rl.current;return e!==null?e:De.pooledCache}function Bi(e,t){t===null?Y(rl,rl.current):Y(rl,t.pool)}function Qo(){var e=Hs();return e===null?null:{parent:Ke._currentValue,pool:e}}var Rn=Error(o(460)),Zo=Error(o(474)),Gi=Error(o(542)),ks={then:function(){}};function Ko(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Yi(){}function Jo(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(Yi,Yi),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Fo(e),e;default:if(typeof t.status=="string")t.then(Yi,Yi);else{if(e=De,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=l}},function(l){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Fo(e),e}throw zn=t,Rn}}var zn=null;function $o(){if(zn===null)throw Error(o(459));var e=zn;return zn=null,e}function Fo(e){if(e===Rn||e===Gi)throw Error(o(483))}var Ea=!1;function qs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Bs(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function wa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Aa(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(we&2)!==0){var n=l.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),l.pending=t,t=Di(e),Lo(e,null,a),t}return _i(e,l,t,a),Di(e)}function On(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Yu(e,a)}}function Gs(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,r=null;if(a=a.firstBaseUpdate,a!==null){do{var d={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};r===null?n=r=d:r=r.next=d,a=a.next}while(a!==null);r===null?n=r=t:r=r.next=t}else n=r=t;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:r,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Ys=!1;function Mn(){if(Ys){var e=_l;if(e!==null)throw e}}function _n(e,t,a,l){Ys=!1;var n=e.updateQueue;Ea=!1;var r=n.firstBaseUpdate,d=n.lastBaseUpdate,m=n.shared.pending;if(m!==null){n.shared.pending=null;var y=m,T=y.next;y.next=null,d===null?r=T:d.next=T,d=y;var U=e.alternate;U!==null&&(U=U.updateQueue,m=U.lastBaseUpdate,m!==d&&(m===null?U.firstBaseUpdate=T:m.next=T,U.lastBaseUpdate=y))}if(r!==null){var k=n.baseState;d=0,U=T=y=null,m=r;do{var R=m.lane&-536870913,O=R!==m.lane;if(O?(ve&R)===R:(l&R)===R){R!==0&&R===Ml&&(Ys=!0),U!==null&&(U=U.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});e:{var ie=e,ae=m;R=t;var ze=a;switch(ae.tag){case 1:if(ie=ae.payload,typeof ie=="function"){k=ie.call(ze,k,R);break e}k=ie;break e;case 3:ie.flags=ie.flags&-65537|128;case 0:if(ie=ae.payload,R=typeof ie=="function"?ie.call(ze,k,R):ie,R==null)break e;k=b({},k,R);break e;case 2:Ea=!0}}R=m.callback,R!==null&&(e.flags|=64,O&&(e.flags|=8192),O=n.callbacks,O===null?n.callbacks=[R]:O.push(R))}else O={lane:R,tag:m.tag,payload:m.payload,callback:m.callback,next:null},U===null?(T=U=O,y=k):U=U.next=O,d|=R;if(m=m.next,m===null){if(m=n.shared.pending,m===null)break;O=m,m=O.next,O.next=null,n.lastBaseUpdate=O,n.shared.pending=null}}while(!0);U===null&&(y=k),n.baseState=y,n.firstBaseUpdate=T,n.lastBaseUpdate=U,r===null&&(n.shared.lanes=0),Da|=d,e.lanes=d,e.memoizedState=k}}function Wo(e,t){if(typeof e!="function")throw Error(o(191,e));e.call(t)}function Po(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Wo(a[e],t)}var Dl=q(null),Vi=q(0);function Io(e,t){e=oa,Y(Vi,e),Y(Dl,t),oa=e|t.baseLanes}function Vs(){Y(Vi,oa),Y(Dl,Dl.current)}function Xs(){oa=Vi.current,Q(Dl),Q(Vi)}var Ca=0,ue=null,Te=null,Qe=null,Xi=!1,Ul=!1,sl=!1,Qi=0,Dn=0,Ll=null,d0=0;function Ve(){throw Error(o(321))}function Qs(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!jt(e[a],t[a]))return!1;return!0}function Zs(e,t,a,l,n,r){return Ca=r,ue=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,_.H=e===null||e.memoizedState===null?Ld:Hd,sl=!1,r=a(l,n),sl=!1,Ul&&(r=td(t,a,l,n)),ed(e),r}function ed(e){_.H=Wi;var t=Te!==null&&Te.next!==null;if(Ca=0,Qe=Te=ue=null,Xi=!1,Dn=0,Ll=null,t)throw Error(o(300));e===null||We||(e=e.dependencies,e!==null&&ki(e)&&(We=!0))}function td(e,t,a,l){ue=e;var n=0;do{if(Ul&&(Ll=null),Dn=0,Ul=!1,25<=n)throw Error(o(301));if(n+=1,Qe=Te=null,e.updateQueue!=null){var r=e.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}_.H=y0,r=t(a,l)}while(Ul);return r}function f0(){var e=_.H,t=e.useState()[0];return t=typeof t.then=="function"?Un(t):t,e=e.useState()[0],(Te!==null?Te.memoizedState:null)!==e&&(ue.flags|=1024),t}function Ks(){var e=Qi!==0;return Qi=0,e}function Js(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function $s(e){if(Xi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Xi=!1}Ca=0,Qe=Te=ue=null,Ul=!1,Dn=Qi=0,Ll=null}function ht(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Qe===null?ue.memoizedState=Qe=e:Qe=Qe.next=e,Qe}function Ze(){if(Te===null){var e=ue.alternate;e=e!==null?e.memoizedState:null}else e=Te.next;var t=Qe===null?ue.memoizedState:Qe.next;if(t!==null)Qe=t,Te=e;else{if(e===null)throw ue.alternate===null?Error(o(467)):Error(o(310));Te=e,e={memoizedState:Te.memoizedState,baseState:Te.baseState,baseQueue:Te.baseQueue,queue:Te.queue,next:null},Qe===null?ue.memoizedState=Qe=e:Qe=Qe.next=e}return Qe}function Fs(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Un(e){var t=Dn;return Dn+=1,Ll===null&&(Ll=[]),e=Jo(Ll,e,t),t=ue,(Qe===null?t.memoizedState:Qe.next)===null&&(t=t.alternate,_.H=t===null||t.memoizedState===null?Ld:Hd),e}function Zi(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Un(e);if(e.$$typeof===I)return it(e)}throw Error(o(438,String(e)))}function Ws(e){var t=null,a=ue.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=ue.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Fs(),ue.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=xe;return t.index++,a}function na(e,t){return typeof t=="function"?t(e):t}function Ki(e){var t=Ze();return Ps(t,Te,e)}function Ps(e,t,a){var l=e.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=a;var n=e.baseQueue,r=l.pending;if(r!==null){if(n!==null){var d=n.next;n.next=r.next,r.next=d}t.baseQueue=n=r,l.pending=null}if(r=e.baseState,n===null)e.memoizedState=r;else{t=n.next;var m=d=null,y=null,T=t,U=!1;do{var k=T.lane&-536870913;if(k!==T.lane?(ve&k)===k:(Ca&k)===k){var R=T.revertLane;if(R===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null}),k===Ml&&(U=!0);else if((Ca&R)===R){T=T.next,R===Ml&&(U=!0);continue}else k={lane:0,revertLane:T.revertLane,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null},y===null?(m=y=k,d=r):y=y.next=k,ue.lanes|=R,Da|=R;k=T.action,sl&&a(r,k),r=T.hasEagerState?T.eagerState:a(r,k)}else R={lane:k,revertLane:T.revertLane,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null},y===null?(m=y=R,d=r):y=y.next=R,ue.lanes|=k,Da|=k;T=T.next}while(T!==null&&T!==t);if(y===null?d=r:y.next=m,!jt(r,e.memoizedState)&&(We=!0,U&&(a=_l,a!==null)))throw a;e.memoizedState=r,e.baseState=d,e.baseQueue=y,l.lastRenderedState=r}return n===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Is(e){var t=Ze(),a=t.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=e;var l=a.dispatch,n=a.pending,r=t.memoizedState;if(n!==null){a.pending=null;var d=n=n.next;do r=e(r,d.action),d=d.next;while(d!==n);jt(r,t.memoizedState)||(We=!0),t.memoizedState=r,t.baseQueue===null&&(t.baseState=r),a.lastRenderedState=r}return[r,l]}function ad(e,t,a){var l=ue,n=Ze(),r=Ne;if(r){if(a===void 0)throw Error(o(407));a=a()}else a=t();var d=!jt((Te||n).memoizedState,a);d&&(n.memoizedState=a,We=!0),n=n.queue;var m=id.bind(null,l,n,e);if(Ln(2048,8,m,[e]),n.getSnapshot!==t||d||Qe!==null&&Qe.memoizedState.tag&1){if(l.flags|=2048,Hl(9,Ji(),nd.bind(null,l,n,a,t),null),De===null)throw Error(o(349));r||(Ca&124)!==0||ld(l,t,a)}return a}function ld(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=ue.updateQueue,t===null?(t=Fs(),ue.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function nd(e,t,a,l){t.value=a,t.getSnapshot=l,rd(t)&&sd(e)}function id(e,t,a){return a(function(){rd(t)&&sd(e)})}function rd(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!jt(e,a)}catch{return!0}}function sd(e){var t=Tl(e,2);t!==null&&Ct(t,e,2)}function ec(e){var t=ht();if(typeof e=="function"){var a=e;if(e=a(),sl){ba(!0);try{a()}finally{ba(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:na,lastRenderedState:e},t}function cd(e,t,a,l){return e.baseState=a,Ps(e,Te,typeof l=="function"?l:na)}function m0(e,t,a,l,n){if(Fi(e))throw Error(o(485));if(e=t.action,e!==null){var r={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){r.listeners.push(d)}};_.T!==null?a(!0):r.isTransition=!1,l(r),a=t.pending,a===null?(r.next=t.pending=r,ud(t,r)):(r.next=a.next,t.pending=a.next=r)}}function ud(e,t){var a=t.action,l=t.payload,n=e.state;if(t.isTransition){var r=_.T,d={};_.T=d;try{var m=a(n,l),y=_.S;y!==null&&y(d,m),od(e,t,m)}catch(T){tc(e,t,T)}finally{_.T=r}}else try{r=a(n,l),od(e,t,r)}catch(T){tc(e,t,T)}}function od(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){dd(e,t,l)},function(l){return tc(e,t,l)}):dd(e,t,a)}function dd(e,t,a){t.status="fulfilled",t.value=a,fd(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,ud(e,a)))}function tc(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,fd(t),t=t.next;while(t!==l)}e.action=null}function fd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function md(e,t){return t}function hd(e,t){if(Ne){var a=De.formState;if(a!==null){e:{var l=ue;if(Ne){if(Ge){t:{for(var n=Ge,r=Vt;n.nodeType!==8;){if(!r){n=null;break t}if(n=Bt(n.nextSibling),n===null){n=null;break t}}r=n.data,n=r==="F!"||r==="F"?n:null}if(n){Ge=Bt(n.nextSibling),l=n.data==="F!";break e}}ll(l)}l=!1}l&&(t=a[0])}}return a=ht(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:md,lastRenderedState:t},a.queue=l,a=_d.bind(null,ue,l),l.dispatch=a,l=ec(!1),r=rc.bind(null,ue,!1,l.queue),l=ht(),n={state:t,dispatch:null,action:e,pending:null},l.queue=n,a=m0.bind(null,ue,n,r,a),n.dispatch=a,l.memoizedState=e,[t,a,!1]}function gd(e){var t=Ze();return pd(t,Te,e)}function pd(e,t,a){if(t=Ps(e,t,md)[0],e=Ki(na)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Un(t)}catch(d){throw d===Rn?Gi:d}else l=t;t=Ze();var n=t.queue,r=n.dispatch;return a!==t.memoizedState&&(ue.flags|=2048,Hl(9,Ji(),h0.bind(null,n,a),null)),[l,r,e]}function h0(e,t){e.action=t}function xd(e){var t=Ze(),a=Te;if(a!==null)return pd(t,a,e);Ze(),t=t.memoizedState,a=Ze();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function Hl(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=ue.updateQueue,t===null&&(t=Fs(),ue.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function Ji(){return{destroy:void 0,resource:void 0}}function yd(){return Ze().memoizedState}function $i(e,t,a,l){var n=ht();l=l===void 0?null:l,ue.flags|=e,n.memoizedState=Hl(1|t,Ji(),a,l)}function Ln(e,t,a,l){var n=Ze();l=l===void 0?null:l;var r=n.memoizedState.inst;Te!==null&&l!==null&&Qs(l,Te.memoizedState.deps)?n.memoizedState=Hl(t,r,a,l):(ue.flags|=e,n.memoizedState=Hl(1|t,r,a,l))}function vd(e,t){$i(8390656,8,e,t)}function bd(e,t){Ln(2048,8,e,t)}function jd(e,t){return Ln(4,2,e,t)}function Nd(e,t){return Ln(4,4,e,t)}function Sd(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ed(e,t,a){a=a!=null?a.concat([e]):null,Ln(4,4,Sd.bind(null,t,e),a)}function ac(){}function wd(e,t){var a=Ze();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Qs(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function Ad(e,t){var a=Ze();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Qs(t,l[1]))return l[0];if(l=e(),sl){ba(!0);try{e()}finally{ba(!1)}}return a.memoizedState=[l,t],l}function lc(e,t,a){return a===void 0||(Ca&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=zf(),ue.lanes|=e,Da|=e,a)}function Cd(e,t,a,l){return jt(a,t)?a:Dl.current!==null?(e=lc(e,a,l),jt(e,t)||(We=!0),e):(Ca&42)===0?(We=!0,e.memoizedState=a):(e=zf(),ue.lanes|=e,Da|=e,t)}function Td(e,t,a,l,n){var r=X.p;X.p=r!==0&&8>r?r:8;var d=_.T,m={};_.T=m,rc(e,!1,t,a);try{var y=n(),T=_.S;if(T!==null&&T(m,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var U=o0(y,l);Hn(e,t,U,At(e))}else Hn(e,t,l,At(e))}catch(k){Hn(e,t,{then:function(){},status:"rejected",reason:k},At())}finally{X.p=r,_.T=d}}function g0(){}function nc(e,t,a,l){if(e.tag!==5)throw Error(o(476));var n=Rd(e).queue;Td(e,n,t,B,a===null?g0:function(){return zd(e),a(l)})}function Rd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:B,baseState:B,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:na,lastRenderedState:B},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:na,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function zd(e){var t=Rd(e).next.queue;Hn(e,t,{},At())}function ic(){return it(ti)}function Od(){return Ze().memoizedState}function Md(){return Ze().memoizedState}function p0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=At();e=wa(a);var l=Aa(t,e,a);l!==null&&(Ct(l,t,a),On(l,t,a)),t={cache:Us()},e.payload=t;return}t=t.return}}function x0(e,t,a){var l=At();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Fi(e)?Dd(t,a):(a=ws(e,t,a,l),a!==null&&(Ct(a,e,l),Ud(a,t,l)))}function _d(e,t,a){var l=At();Hn(e,t,a,l)}function Hn(e,t,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Fi(e))Dd(t,n);else{var r=e.alternate;if(e.lanes===0&&(r===null||r.lanes===0)&&(r=t.lastRenderedReducer,r!==null))try{var d=t.lastRenderedState,m=r(d,a);if(n.hasEagerState=!0,n.eagerState=m,jt(m,d))return _i(e,t,n,0),De===null&&Mi(),!1}catch{}finally{}if(a=ws(e,t,n,l),a!==null)return Ct(a,e,l),Ud(a,t,l),!0}return!1}function rc(e,t,a,l){if(l={lane:2,revertLane:kc(),action:l,hasEagerState:!1,eagerState:null,next:null},Fi(e)){if(t)throw Error(o(479))}else t=ws(e,a,l,2),t!==null&&Ct(t,e,2)}function Fi(e){var t=e.alternate;return e===ue||t!==null&&t===ue}function Dd(e,t){Ul=Xi=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Ud(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Yu(e,a)}}var Wi={readContext:it,use:Zi,useCallback:Ve,useContext:Ve,useEffect:Ve,useImperativeHandle:Ve,useLayoutEffect:Ve,useInsertionEffect:Ve,useMemo:Ve,useReducer:Ve,useRef:Ve,useState:Ve,useDebugValue:Ve,useDeferredValue:Ve,useTransition:Ve,useSyncExternalStore:Ve,useId:Ve,useHostTransitionStatus:Ve,useFormState:Ve,useActionState:Ve,useOptimistic:Ve,useMemoCache:Ve,useCacheRefresh:Ve},Ld={readContext:it,use:Zi,useCallback:function(e,t){return ht().memoizedState=[e,t===void 0?null:t],e},useContext:it,useEffect:vd,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,$i(4194308,4,Sd.bind(null,t,e),a)},useLayoutEffect:function(e,t){return $i(4194308,4,e,t)},useInsertionEffect:function(e,t){$i(4,2,e,t)},useMemo:function(e,t){var a=ht();t=t===void 0?null:t;var l=e();if(sl){ba(!0);try{e()}finally{ba(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=ht();if(a!==void 0){var n=a(t);if(sl){ba(!0);try{a(t)}finally{ba(!1)}}}else n=t;return l.memoizedState=l.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=x0.bind(null,ue,e),[l.memoizedState,e]},useRef:function(e){var t=ht();return e={current:e},t.memoizedState=e},useState:function(e){e=ec(e);var t=e.queue,a=_d.bind(null,ue,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:ac,useDeferredValue:function(e,t){var a=ht();return lc(a,e,t)},useTransition:function(){var e=ec(!1);return e=Td.bind(null,ue,e.queue,!0,!1),ht().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=ue,n=ht();if(Ne){if(a===void 0)throw Error(o(407));a=a()}else{if(a=t(),De===null)throw Error(o(349));(ve&124)!==0||ld(l,t,a)}n.memoizedState=a;var r={value:a,getSnapshot:t};return n.queue=r,vd(id.bind(null,l,r,e),[e]),l.flags|=2048,Hl(9,Ji(),nd.bind(null,l,r,a,t),null),a},useId:function(){var e=ht(),t=De.identifierPrefix;if(Ne){var a=ta,l=ea;a=(l&~(1<<32-bt(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=Qi++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=d0++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:ic,useFormState:hd,useActionState:hd,useOptimistic:function(e){var t=ht();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=rc.bind(null,ue,!0,a),a.dispatch=t,[e,t]},useMemoCache:Ws,useCacheRefresh:function(){return ht().memoizedState=p0.bind(null,ue)}},Hd={readContext:it,use:Zi,useCallback:wd,useContext:it,useEffect:bd,useImperativeHandle:Ed,useInsertionEffect:jd,useLayoutEffect:Nd,useMemo:Ad,useReducer:Ki,useRef:yd,useState:function(){return Ki(na)},useDebugValue:ac,useDeferredValue:function(e,t){var a=Ze();return Cd(a,Te.memoizedState,e,t)},useTransition:function(){var e=Ki(na)[0],t=Ze().memoizedState;return[typeof e=="boolean"?e:Un(e),t]},useSyncExternalStore:ad,useId:Od,useHostTransitionStatus:ic,useFormState:gd,useActionState:gd,useOptimistic:function(e,t){var a=Ze();return cd(a,Te,e,t)},useMemoCache:Ws,useCacheRefresh:Md},y0={readContext:it,use:Zi,useCallback:wd,useContext:it,useEffect:bd,useImperativeHandle:Ed,useInsertionEffect:jd,useLayoutEffect:Nd,useMemo:Ad,useReducer:Is,useRef:yd,useState:function(){return Is(na)},useDebugValue:ac,useDeferredValue:function(e,t){var a=Ze();return Te===null?lc(a,e,t):Cd(a,Te.memoizedState,e,t)},useTransition:function(){var e=Is(na)[0],t=Ze().memoizedState;return[typeof e=="boolean"?e:Un(e),t]},useSyncExternalStore:ad,useId:Od,useHostTransitionStatus:ic,useFormState:xd,useActionState:xd,useOptimistic:function(e,t){var a=Ze();return Te!==null?cd(a,Te,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Ws,useCacheRefresh:Md},kl=null,kn=0;function Pi(e){var t=kn;return kn+=1,kl===null&&(kl=[]),Jo(kl,e,t)}function qn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Ii(e,t){throw t.$$typeof===E?Error(o(525)):(e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function kd(e){var t=e._init;return t(e._payload)}function qd(e){function t(A,S){if(e){var C=A.deletions;C===null?(A.deletions=[S],A.flags|=16):C.push(S)}}function a(A,S){if(!e)return null;for(;S!==null;)t(A,S),S=S.sibling;return null}function l(A){for(var S=new Map;A!==null;)A.key!==null?S.set(A.key,A):S.set(A.index,A),A=A.sibling;return S}function n(A,S){return A=It(A,S),A.index=0,A.sibling=null,A}function r(A,S,C){return A.index=C,e?(C=A.alternate,C!==null?(C=C.index,C<S?(A.flags|=67108866,S):C):(A.flags|=67108866,S)):(A.flags|=1048576,S)}function d(A){return e&&A.alternate===null&&(A.flags|=67108866),A}function m(A,S,C,L){return S===null||S.tag!==6?(S=Cs(C,A.mode,L),S.return=A,S):(S=n(S,C),S.return=A,S)}function y(A,S,C,L){var K=C.type;return K===M?U(A,S,C.props.children,L,C.key):S!==null&&(S.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===V&&kd(K)===S.type)?(S=n(S,C.props),qn(S,C),S.return=A,S):(S=Ui(C.type,C.key,C.props,null,A.mode,L),qn(S,C),S.return=A,S)}function T(A,S,C,L){return S===null||S.tag!==4||S.stateNode.containerInfo!==C.containerInfo||S.stateNode.implementation!==C.implementation?(S=Ts(C,A.mode,L),S.return=A,S):(S=n(S,C.children||[]),S.return=A,S)}function U(A,S,C,L,K){return S===null||S.tag!==7?(S=Ia(C,A.mode,L,K),S.return=A,S):(S=n(S,C),S.return=A,S)}function k(A,S,C){if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return S=Cs(""+S,A.mode,C),S.return=A,S;if(typeof S=="object"&&S!==null){switch(S.$$typeof){case j:return C=Ui(S.type,S.key,S.props,null,A.mode,C),qn(C,S),C.return=A,C;case G:return S=Ts(S,A.mode,C),S.return=A,S;case V:var L=S._init;return S=L(S._payload),k(A,S,C)}if(Ce(S)||Se(S))return S=Ia(S,A.mode,C,null),S.return=A,S;if(typeof S.then=="function")return k(A,Pi(S),C);if(S.$$typeof===I)return k(A,qi(A,S),C);Ii(A,S)}return null}function R(A,S,C,L){var K=S!==null?S.key:null;if(typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint")return K!==null?null:m(A,S,""+C,L);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case j:return C.key===K?y(A,S,C,L):null;case G:return C.key===K?T(A,S,C,L):null;case V:return K=C._init,C=K(C._payload),R(A,S,C,L)}if(Ce(C)||Se(C))return K!==null?null:U(A,S,C,L,null);if(typeof C.then=="function")return R(A,S,Pi(C),L);if(C.$$typeof===I)return R(A,S,qi(A,C),L);Ii(A,C)}return null}function O(A,S,C,L,K){if(typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint")return A=A.get(C)||null,m(S,A,""+L,K);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case j:return A=A.get(L.key===null?C:L.key)||null,y(S,A,L,K);case G:return A=A.get(L.key===null?C:L.key)||null,T(S,A,L,K);case V:var de=L._init;return L=de(L._payload),O(A,S,C,L,K)}if(Ce(L)||Se(L))return A=A.get(C)||null,U(S,A,L,K,null);if(typeof L.then=="function")return O(A,S,C,Pi(L),K);if(L.$$typeof===I)return O(A,S,C,qi(S,L),K);Ii(S,L)}return null}function ie(A,S,C,L){for(var K=null,de=null,ee=S,le=S=0,Ie=null;ee!==null&&le<C.length;le++){ee.index>le?(Ie=ee,ee=null):Ie=ee.sibling;var je=R(A,ee,C[le],L);if(je===null){ee===null&&(ee=Ie);break}e&&ee&&je.alternate===null&&t(A,ee),S=r(je,S,le),de===null?K=je:de.sibling=je,de=je,ee=Ie}if(le===C.length)return a(A,ee),Ne&&tl(A,le),K;if(ee===null){for(;le<C.length;le++)ee=k(A,C[le],L),ee!==null&&(S=r(ee,S,le),de===null?K=ee:de.sibling=ee,de=ee);return Ne&&tl(A,le),K}for(ee=l(ee);le<C.length;le++)Ie=O(ee,A,le,C[le],L),Ie!==null&&(e&&Ie.alternate!==null&&ee.delete(Ie.key===null?le:Ie.key),S=r(Ie,S,le),de===null?K=Ie:de.sibling=Ie,de=Ie);return e&&ee.forEach(function(Va){return t(A,Va)}),Ne&&tl(A,le),K}function ae(A,S,C,L){if(C==null)throw Error(o(151));for(var K=null,de=null,ee=S,le=S=0,Ie=null,je=C.next();ee!==null&&!je.done;le++,je=C.next()){ee.index>le?(Ie=ee,ee=null):Ie=ee.sibling;var Va=R(A,ee,je.value,L);if(Va===null){ee===null&&(ee=Ie);break}e&&ee&&Va.alternate===null&&t(A,ee),S=r(Va,S,le),de===null?K=Va:de.sibling=Va,de=Va,ee=Ie}if(je.done)return a(A,ee),Ne&&tl(A,le),K;if(ee===null){for(;!je.done;le++,je=C.next())je=k(A,je.value,L),je!==null&&(S=r(je,S,le),de===null?K=je:de.sibling=je,de=je);return Ne&&tl(A,le),K}for(ee=l(ee);!je.done;le++,je=C.next())je=O(ee,A,le,je.value,L),je!==null&&(e&&je.alternate!==null&&ee.delete(je.key===null?le:je.key),S=r(je,S,le),de===null?K=je:de.sibling=je,de=je);return e&&ee.forEach(function(vp){return t(A,vp)}),Ne&&tl(A,le),K}function ze(A,S,C,L){if(typeof C=="object"&&C!==null&&C.type===M&&C.key===null&&(C=C.props.children),typeof C=="object"&&C!==null){switch(C.$$typeof){case j:e:{for(var K=C.key;S!==null;){if(S.key===K){if(K=C.type,K===M){if(S.tag===7){a(A,S.sibling),L=n(S,C.props.children),L.return=A,A=L;break e}}else if(S.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===V&&kd(K)===S.type){a(A,S.sibling),L=n(S,C.props),qn(L,C),L.return=A,A=L;break e}a(A,S);break}else t(A,S);S=S.sibling}C.type===M?(L=Ia(C.props.children,A.mode,L,C.key),L.return=A,A=L):(L=Ui(C.type,C.key,C.props,null,A.mode,L),qn(L,C),L.return=A,A=L)}return d(A);case G:e:{for(K=C.key;S!==null;){if(S.key===K)if(S.tag===4&&S.stateNode.containerInfo===C.containerInfo&&S.stateNode.implementation===C.implementation){a(A,S.sibling),L=n(S,C.children||[]),L.return=A,A=L;break e}else{a(A,S);break}else t(A,S);S=S.sibling}L=Ts(C,A.mode,L),L.return=A,A=L}return d(A);case V:return K=C._init,C=K(C._payload),ze(A,S,C,L)}if(Ce(C))return ie(A,S,C,L);if(Se(C)){if(K=Se(C),typeof K!="function")throw Error(o(150));return C=K.call(C),ae(A,S,C,L)}if(typeof C.then=="function")return ze(A,S,Pi(C),L);if(C.$$typeof===I)return ze(A,S,qi(A,C),L);Ii(A,C)}return typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint"?(C=""+C,S!==null&&S.tag===6?(a(A,S.sibling),L=n(S,C),L.return=A,A=L):(a(A,S),L=Cs(C,A.mode,L),L.return=A,A=L),d(A)):a(A,S)}return function(A,S,C,L){try{kn=0;var K=ze(A,S,C,L);return kl=null,K}catch(ee){if(ee===Rn||ee===Gi)throw ee;var de=Nt(29,ee,null,A.mode);return de.lanes=L,de.return=A,de}finally{}}}var ql=qd(!0),Bd=qd(!1),Dt=q(null),Xt=null;function Ta(e){var t=e.alternate;Y(Je,Je.current&1),Y(Dt,e),Xt===null&&(t===null||Dl.current!==null||t.memoizedState!==null)&&(Xt=e)}function Gd(e){if(e.tag===22){if(Y(Je,Je.current),Y(Dt,e),Xt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Xt=e)}}else Ra()}function Ra(){Y(Je,Je.current),Y(Dt,Dt.current)}function ia(e){Q(Dt),Xt===e&&(Xt=null),Q(Je)}var Je=q(0);function er(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Fc(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function sc(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:b({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var cc={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=At(),n=wa(l);n.payload=t,a!=null&&(n.callback=a),t=Aa(e,n,l),t!==null&&(Ct(t,e,l),On(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=At(),n=wa(l);n.tag=1,n.payload=t,a!=null&&(n.callback=a),t=Aa(e,n,l),t!==null&&(Ct(t,e,l),On(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=At(),l=wa(a);l.tag=2,t!=null&&(l.callback=t),t=Aa(e,l,a),t!==null&&(Ct(t,e,a),On(t,e,a))}};function Yd(e,t,a,l,n,r,d){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,r,d):t.prototype&&t.prototype.isPureReactComponent?!jn(a,l)||!jn(n,r):!0}function Vd(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&cc.enqueueReplaceState(t,t.state,null)}function cl(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=b({},a));for(var n in e)a[n]===void 0&&(a[n]=e[n])}return a}var tr=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Xd(e){tr(e)}function Qd(e){console.error(e)}function Zd(e){tr(e)}function ar(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Kd(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function uc(e,t,a){return a=wa(a),a.tag=3,a.payload={element:null},a.callback=function(){ar(e,t)},a}function Jd(e){return e=wa(e),e.tag=3,e}function $d(e,t,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var r=l.value;e.payload=function(){return n(r)},e.callback=function(){Kd(t,a,l)}}var d=a.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(e.callback=function(){Kd(t,a,l),typeof n!="function"&&(Ua===null?Ua=new Set([this]):Ua.add(this));var m=l.stack;this.componentDidCatch(l.value,{componentStack:m!==null?m:""})})}function v0(e,t,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&An(t,a,n,!0),a=Dt.current,a!==null){switch(a.tag){case 13:return Xt===null?_c():a.alternate===null&&Ye===0&&(Ye=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===ks?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),Uc(e,l,n)),!1;case 22:return a.flags|=65536,l===ks?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),Uc(e,l,n)),!1}throw Error(o(435,a.tag))}return Uc(e,l,n),_c(),!1}if(Ne)return t=Dt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,l!==Os&&(e=Error(o(422),{cause:l}),wn(zt(e,a)))):(l!==Os&&(t=Error(o(423),{cause:l}),wn(zt(t,a))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,l=zt(l,a),n=uc(e.stateNode,l,n),Gs(e,n),Ye!==4&&(Ye=2)),!1;var r=Error(o(520),{cause:l});if(r=zt(r,a),Zn===null?Zn=[r]:Zn.push(r),Ye!==4&&(Ye=2),t===null)return!0;l=zt(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=n&-n,a.lanes|=e,e=uc(a.stateNode,l,e),Gs(a,e),!1;case 1:if(t=a.type,r=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(Ua===null||!Ua.has(r))))return a.flags|=65536,n&=-n,a.lanes|=n,n=Jd(n),$d(n,e,a,l),Gs(a,n),!1}a=a.return}while(a!==null);return!1}var Fd=Error(o(461)),We=!1;function et(e,t,a,l){t.child=e===null?Bd(t,null,a,l):ql(t,e.child,a,l)}function Wd(e,t,a,l,n){a=a.render;var r=t.ref;if("ref"in l){var d={};for(var m in l)m!=="ref"&&(d[m]=l[m])}else d=l;return il(t),l=Zs(e,t,a,d,r,n),m=Ks(),e!==null&&!We?(Js(e,t,n),ra(e,t,n)):(Ne&&m&&Rs(t),t.flags|=1,et(e,t,l,n),t.child)}function Pd(e,t,a,l,n){if(e===null){var r=a.type;return typeof r=="function"&&!As(r)&&r.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=r,Id(e,t,r,l,n)):(e=Ui(a.type,null,l,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(r=e.child,!xc(e,n)){var d=r.memoizedProps;if(a=a.compare,a=a!==null?a:jn,a(d,l)&&e.ref===t.ref)return ra(e,t,n)}return t.flags|=1,e=It(r,l),e.ref=t.ref,e.return=t,t.child=e}function Id(e,t,a,l,n){if(e!==null){var r=e.memoizedProps;if(jn(r,l)&&e.ref===t.ref)if(We=!1,t.pendingProps=l=r,xc(e,n))(e.flags&131072)!==0&&(We=!0);else return t.lanes=e.lanes,ra(e,t,n)}return oc(e,t,a,l,n)}function ef(e,t,a){var l=t.pendingProps,n=l.children,r=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=r!==null?r.baseLanes|a:a,e!==null){for(n=t.child=e.child,r=0;n!==null;)r=r|n.lanes|n.childLanes,n=n.sibling;t.childLanes=r&~l}else t.childLanes=0,t.child=null;return tf(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Bi(t,r!==null?r.cachePool:null),r!==null?Io(t,r):Vs(),Gd(t);else return t.lanes=t.childLanes=536870912,tf(e,t,r!==null?r.baseLanes|a:a,a)}else r!==null?(Bi(t,r.cachePool),Io(t,r),Ra(),t.memoizedState=null):(e!==null&&Bi(t,null),Vs(),Ra());return et(e,t,n,a),t.child}function tf(e,t,a,l){var n=Hs();return n=n===null?null:{parent:Ke._currentValue,pool:n},t.memoizedState={baseLanes:a,cachePool:n},e!==null&&Bi(t,null),Vs(),Gd(t),e!==null&&An(e,t,l,!0),null}function lr(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(o(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function oc(e,t,a,l,n){return il(t),a=Zs(e,t,a,l,void 0,n),l=Ks(),e!==null&&!We?(Js(e,t,n),ra(e,t,n)):(Ne&&l&&Rs(t),t.flags|=1,et(e,t,a,n),t.child)}function af(e,t,a,l,n,r){return il(t),t.updateQueue=null,a=td(t,l,a,n),ed(e),l=Ks(),e!==null&&!We?(Js(e,t,r),ra(e,t,r)):(Ne&&l&&Rs(t),t.flags|=1,et(e,t,a,r),t.child)}function lf(e,t,a,l,n){if(il(t),t.stateNode===null){var r=Rl,d=a.contextType;typeof d=="object"&&d!==null&&(r=it(d)),r=new a(l,r),t.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=cc,t.stateNode=r,r._reactInternals=t,r=t.stateNode,r.props=l,r.state=t.memoizedState,r.refs={},qs(t),d=a.contextType,r.context=typeof d=="object"&&d!==null?it(d):Rl,r.state=t.memoizedState,d=a.getDerivedStateFromProps,typeof d=="function"&&(sc(t,a,d,l),r.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(d=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),d!==r.state&&cc.enqueueReplaceState(r,r.state,null),_n(t,l,r,n),Mn(),r.state=t.memoizedState),typeof r.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){r=t.stateNode;var m=t.memoizedProps,y=cl(a,m);r.props=y;var T=r.context,U=a.contextType;d=Rl,typeof U=="object"&&U!==null&&(d=it(U));var k=a.getDerivedStateFromProps;U=typeof k=="function"||typeof r.getSnapshotBeforeUpdate=="function",m=t.pendingProps!==m,U||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(m||T!==d)&&Vd(t,r,l,d),Ea=!1;var R=t.memoizedState;r.state=R,_n(t,l,r,n),Mn(),T=t.memoizedState,m||R!==T||Ea?(typeof k=="function"&&(sc(t,a,k,l),T=t.memoizedState),(y=Ea||Yd(t,a,y,l,R,T,d))?(U||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(t.flags|=4194308)):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=T),r.props=l,r.state=T,r.context=d,l=y):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{r=t.stateNode,Bs(e,t),d=t.memoizedProps,U=cl(a,d),r.props=U,k=t.pendingProps,R=r.context,T=a.contextType,y=Rl,typeof T=="object"&&T!==null&&(y=it(T)),m=a.getDerivedStateFromProps,(T=typeof m=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(d!==k||R!==y)&&Vd(t,r,l,y),Ea=!1,R=t.memoizedState,r.state=R,_n(t,l,r,n),Mn();var O=t.memoizedState;d!==k||R!==O||Ea||e!==null&&e.dependencies!==null&&ki(e.dependencies)?(typeof m=="function"&&(sc(t,a,m,l),O=t.memoizedState),(U=Ea||Yd(t,a,U,l,R,O,y)||e!==null&&e.dependencies!==null&&ki(e.dependencies))?(T||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(l,O,y),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(l,O,y)),typeof r.componentDidUpdate=="function"&&(t.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof r.componentDidUpdate!="function"||d===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=O),r.props=l,r.state=O,r.context=y,l=U):(typeof r.componentDidUpdate!="function"||d===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),l=!1)}return r=l,lr(e,t),l=(t.flags&128)!==0,r||l?(r=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:r.render(),t.flags|=1,e!==null&&l?(t.child=ql(t,e.child,null,n),t.child=ql(t,null,a,n)):et(e,t,a,n),t.memoizedState=r.state,e=t.child):e=ra(e,t,n),e}function nf(e,t,a,l){return En(),t.flags|=256,et(e,t,a,l),t.child}var dc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function fc(e){return{baseLanes:e,cachePool:Qo()}}function mc(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Ut),e}function rf(e,t,a){var l=t.pendingProps,n=!1,r=(t.flags&128)!==0,d;if((d=r)||(d=e!==null&&e.memoizedState===null?!1:(Je.current&2)!==0),d&&(n=!0,t.flags&=-129),d=(t.flags&32)!==0,t.flags&=-33,e===null){if(Ne){if(n?Ta(t):Ra(),Ne){var m=Ge,y;if(y=m){e:{for(y=m,m=Vt;y.nodeType!==8;){if(!m){m=null;break e}if(y=Bt(y.nextSibling),y===null){m=null;break e}}m=y}m!==null?(t.memoizedState={dehydrated:m,treeContext:el!==null?{id:ea,overflow:ta}:null,retryLane:536870912,hydrationErrors:null},y=Nt(18,null,null,0),y.stateNode=m,y.return=t,t.child=y,ot=t,Ge=null,y=!0):y=!1}y||ll(t)}if(m=t.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return Fc(m)?t.lanes=32:t.lanes=536870912,null;ia(t)}return m=l.children,l=l.fallback,n?(Ra(),n=t.mode,m=nr({mode:"hidden",children:m},n),l=Ia(l,n,a,null),m.return=t,l.return=t,m.sibling=l,t.child=m,n=t.child,n.memoizedState=fc(a),n.childLanes=mc(e,d,a),t.memoizedState=dc,l):(Ta(t),hc(t,m))}if(y=e.memoizedState,y!==null&&(m=y.dehydrated,m!==null)){if(r)t.flags&256?(Ta(t),t.flags&=-257,t=gc(e,t,a)):t.memoizedState!==null?(Ra(),t.child=e.child,t.flags|=128,t=null):(Ra(),n=l.fallback,m=t.mode,l=nr({mode:"visible",children:l.children},m),n=Ia(n,m,a,null),n.flags|=2,l.return=t,n.return=t,l.sibling=n,t.child=l,ql(t,e.child,null,a),l=t.child,l.memoizedState=fc(a),l.childLanes=mc(e,d,a),t.memoizedState=dc,t=n);else if(Ta(t),Fc(m)){if(d=m.nextSibling&&m.nextSibling.dataset,d)var T=d.dgst;d=T,l=Error(o(419)),l.stack="",l.digest=d,wn({value:l,source:null,stack:null}),t=gc(e,t,a)}else if(We||An(e,t,a,!1),d=(a&e.childLanes)!==0,We||d){if(d=De,d!==null&&(l=a&-a,l=(l&42)!==0?1:Wr(l),l=(l&(d.suspendedLanes|a))!==0?0:l,l!==0&&l!==y.retryLane))throw y.retryLane=l,Tl(e,l),Ct(d,e,l),Fd;m.data==="$?"||_c(),t=gc(e,t,a)}else m.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=y.treeContext,Ge=Bt(m.nextSibling),ot=t,Ne=!0,al=null,Vt=!1,e!==null&&(Mt[_t++]=ea,Mt[_t++]=ta,Mt[_t++]=el,ea=e.id,ta=e.overflow,el=t),t=hc(t,l.children),t.flags|=4096);return t}return n?(Ra(),n=l.fallback,m=t.mode,y=e.child,T=y.sibling,l=It(y,{mode:"hidden",children:l.children}),l.subtreeFlags=y.subtreeFlags&65011712,T!==null?n=It(T,n):(n=Ia(n,m,a,null),n.flags|=2),n.return=t,l.return=t,l.sibling=n,t.child=l,l=n,n=t.child,m=e.child.memoizedState,m===null?m=fc(a):(y=m.cachePool,y!==null?(T=Ke._currentValue,y=y.parent!==T?{parent:T,pool:T}:y):y=Qo(),m={baseLanes:m.baseLanes|a,cachePool:y}),n.memoizedState=m,n.childLanes=mc(e,d,a),t.memoizedState=dc,l):(Ta(t),a=e.child,e=a.sibling,a=It(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(d=t.deletions,d===null?(t.deletions=[e],t.flags|=16):d.push(e)),t.child=a,t.memoizedState=null,a)}function hc(e,t){return t=nr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function nr(e,t){return e=Nt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function gc(e,t,a){return ql(t,e.child,null,a),e=hc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function sf(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),_s(e.return,t,a)}function pc(e,t,a,l,n){var r=e.memoizedState;r===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(r.isBackwards=t,r.rendering=null,r.renderingStartTime=0,r.last=l,r.tail=a,r.tailMode=n)}function cf(e,t,a){var l=t.pendingProps,n=l.revealOrder,r=l.tail;if(et(e,t,l.children,a),l=Je.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&sf(e,a,t);else if(e.tag===19)sf(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(Y(Je,l),n){case"forwards":for(a=t.child,n=null;a!==null;)e=a.alternate,e!==null&&er(e)===null&&(n=a),a=a.sibling;a=n,a===null?(n=t.child,t.child=null):(n=a.sibling,a.sibling=null),pc(t,!1,n,a,r);break;case"backwards":for(a=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&er(e)===null){t.child=n;break}e=n.sibling,n.sibling=a,a=n,n=e}pc(t,!0,a,null,r);break;case"together":pc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ra(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Da|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(An(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,a=It(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=It(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function xc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&ki(e)))}function b0(e,t,a){switch(t.tag){case 3:Oe(t,t.stateNode.containerInfo),Sa(t,Ke,e.memoizedState.cache),En();break;case 27:case 5:xa(t);break;case 4:Oe(t,t.stateNode.containerInfo);break;case 10:Sa(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Ta(t),t.flags|=128,null):(a&t.child.childLanes)!==0?rf(e,t,a):(Ta(t),e=ra(e,t,a),e!==null?e.sibling:null);Ta(t);break;case 19:var n=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(An(e,t,a,!1),l=(a&t.childLanes)!==0),n){if(l)return cf(e,t,a);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),Y(Je,Je.current),l)break;return null;case 22:case 23:return t.lanes=0,ef(e,t,a);case 24:Sa(t,Ke,e.memoizedState.cache)}return ra(e,t,a)}function uf(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)We=!0;else{if(!xc(e,a)&&(t.flags&128)===0)return We=!1,b0(e,t,a);We=(e.flags&131072)!==0}else We=!1,Ne&&(t.flags&1048576)!==0&&ko(t,Hi,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,n=l._init;if(l=n(l._payload),t.type=l,typeof l=="function")As(l)?(e=cl(l,e),t.tag=1,t=lf(null,t,l,e,a)):(t.tag=0,t=oc(null,t,l,e,a));else{if(l!=null){if(n=l.$$typeof,n===ce){t.tag=11,t=Wd(null,t,l,e,a);break e}else if(n===pe){t.tag=14,t=Pd(null,t,l,e,a);break e}}throw t=lt(l)||l,Error(o(306,t,""))}}return t;case 0:return oc(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,n=cl(l,t.pendingProps),lf(e,t,l,n,a);case 3:e:{if(Oe(t,t.stateNode.containerInfo),e===null)throw Error(o(387));l=t.pendingProps;var r=t.memoizedState;n=r.element,Bs(e,t),_n(t,l,null,a);var d=t.memoizedState;if(l=d.cache,Sa(t,Ke,l),l!==r.cache&&Ds(t,[Ke],a,!0),Mn(),l=d.element,r.isDehydrated)if(r={element:l,isDehydrated:!1,cache:d.cache},t.updateQueue.baseState=r,t.memoizedState=r,t.flags&256){t=nf(e,t,l,a);break e}else if(l!==n){n=zt(Error(o(424)),t),wn(n),t=nf(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ge=Bt(e.firstChild),ot=t,Ne=!0,al=null,Vt=!0,a=Bd(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(En(),l===n){t=ra(e,t,a);break e}et(e,t,l,a)}t=t.child}return t;case 26:return lr(e,t),e===null?(a=mm(t.type,null,t.pendingProps,null))?t.memoizedState=a:Ne||(a=t.type,e=t.pendingProps,l=yr(re.current).createElement(a),l[nt]=t,l[ft]=e,at(l,a,e),Fe(l),t.stateNode=l):t.memoizedState=mm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return xa(t),e===null&&Ne&&(l=t.stateNode=om(t.type,t.pendingProps,re.current),ot=t,Vt=!0,n=Ge,ka(t.type)?(Wc=n,Ge=Bt(l.firstChild)):Ge=n),et(e,t,t.pendingProps.children,a),lr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Ne&&((n=l=Ge)&&(l=J0(l,t.type,t.pendingProps,Vt),l!==null?(t.stateNode=l,ot=t,Ge=Bt(l.firstChild),Vt=!1,n=!0):n=!1),n||ll(t)),xa(t),n=t.type,r=t.pendingProps,d=e!==null?e.memoizedProps:null,l=r.children,Kc(n,r)?l=null:d!==null&&Kc(n,d)&&(t.flags|=32),t.memoizedState!==null&&(n=Zs(e,t,f0,null,null,a),ti._currentValue=n),lr(e,t),et(e,t,l,a),t.child;case 6:return e===null&&Ne&&((e=a=Ge)&&(a=$0(a,t.pendingProps,Vt),a!==null?(t.stateNode=a,ot=t,Ge=null,e=!0):e=!1),e||ll(t)),null;case 13:return rf(e,t,a);case 4:return Oe(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=ql(t,null,l,a):et(e,t,l,a),t.child;case 11:return Wd(e,t,t.type,t.pendingProps,a);case 7:return et(e,t,t.pendingProps,a),t.child;case 8:return et(e,t,t.pendingProps.children,a),t.child;case 12:return et(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,Sa(t,t.type,l.value),et(e,t,l.children,a),t.child;case 9:return n=t.type._context,l=t.pendingProps.children,il(t),n=it(n),l=l(n),t.flags|=1,et(e,t,l,a),t.child;case 14:return Pd(e,t,t.type,t.pendingProps,a);case 15:return Id(e,t,t.type,t.pendingProps,a);case 19:return cf(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=nr(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=It(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return ef(e,t,a);case 24:return il(t),l=it(Ke),e===null?(n=Hs(),n===null&&(n=De,r=Us(),n.pooledCache=r,r.refCount++,r!==null&&(n.pooledCacheLanes|=a),n=r),t.memoizedState={parent:l,cache:n},qs(t),Sa(t,Ke,n)):((e.lanes&a)!==0&&(Bs(e,t),_n(t,null,null,a),Mn()),n=e.memoizedState,r=t.memoizedState,n.parent!==l?(n={parent:l,cache:l},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),Sa(t,Ke,l)):(l=r.cache,Sa(t,Ke,l),l!==n.cache&&Ds(t,[Ke],a,!0))),et(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function sa(e){e.flags|=4}function of(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!ym(t)){if(t=Dt.current,t!==null&&((ve&4194048)===ve?Xt!==null:(ve&62914560)!==ve&&(ve&536870912)===0||t!==Xt))throw zn=ks,Zo;e.flags|=8192}}function ir(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Bu():536870912,e.lanes|=t,Vl|=t)}function Bn(e,t){if(!Ne)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function He(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function j0(e,t,a){var l=t.pendingProps;switch(zs(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return He(t),null;case 1:return He(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),la(Ke),yt(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(Sn(t)?sa(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Go())),He(t),null;case 26:return a=t.memoizedState,e===null?(sa(t),a!==null?(He(t),of(t,a)):(He(t),t.flags&=-16777217)):a?a!==e.memoizedState?(sa(t),He(t),of(t,a)):(He(t),t.flags&=-16777217):(e.memoizedProps!==l&&sa(t),He(t),t.flags&=-16777217),null;case 27:ya(t),a=re.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&sa(t);else{if(!l){if(t.stateNode===null)throw Error(o(166));return He(t),null}e=Z.current,Sn(t)?qo(t):(e=om(n,l,a),t.stateNode=e,sa(t))}return He(t),null;case 5:if(ya(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&sa(t);else{if(!l){if(t.stateNode===null)throw Error(o(166));return He(t),null}if(e=Z.current,Sn(t))qo(t);else{switch(n=yr(re.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}e[nt]=t,e[ft]=l;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(at(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&sa(t)}}return He(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&sa(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(o(166));if(e=re.current,Sn(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,n=ot,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}e[nt]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||lm(e.nodeValue,a)),e||ll(t)}else e=yr(e).createTextNode(l),e[nt]=t,t.stateNode=e}return He(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=Sn(t),l!==null&&l.dehydrated!==null){if(e===null){if(!n)throw Error(o(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(o(317));n[nt]=t}else En(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;He(t),n=!1}else n=Go(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(ia(t),t):(ia(t),null)}if(ia(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var r=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(r=l.memoizedState.cachePool.pool),r!==n&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),ir(t,t.updateQueue),He(t),null;case 4:return yt(),e===null&&Yc(t.stateNode.containerInfo),He(t),null;case 10:return la(t.type),He(t),null;case 19:if(Q(Je),n=t.memoizedState,n===null)return He(t),null;if(l=(t.flags&128)!==0,r=n.rendering,r===null)if(l)Bn(n,!1);else{if(Ye!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(r=er(e),r!==null){for(t.flags|=128,Bn(n,!1),e=r.updateQueue,t.updateQueue=e,ir(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Ho(a,e),a=a.sibling;return Y(Je,Je.current&1|2),t.child}e=e.sibling}n.tail!==null&&Yt()>cr&&(t.flags|=128,l=!0,Bn(n,!1),t.lanes=4194304)}else{if(!l)if(e=er(r),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,ir(t,e),Bn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!r.alternate&&!Ne)return He(t),null}else 2*Yt()-n.renderingStartTime>cr&&a!==536870912&&(t.flags|=128,l=!0,Bn(n,!1),t.lanes=4194304);n.isBackwards?(r.sibling=t.child,t.child=r):(e=n.last,e!==null?e.sibling=r:t.child=r,n.last=r)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=Yt(),t.sibling=null,e=Je.current,Y(Je,l?e&1|2:e&1),t):(He(t),null);case 22:case 23:return ia(t),Xs(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(He(t),t.subtreeFlags&6&&(t.flags|=8192)):He(t),a=t.updateQueue,a!==null&&ir(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&Q(rl),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),la(Ke),He(t),null;case 25:return null;case 30:return null}throw Error(o(156,t.tag))}function N0(e,t){switch(zs(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return la(Ke),yt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return ya(t),null;case 13:if(ia(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));En()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Q(Je),null;case 4:return yt(),null;case 10:return la(t.type),null;case 22:case 23:return ia(t),Xs(),e!==null&&Q(rl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return la(Ke),null;case 25:return null;default:return null}}function df(e,t){switch(zs(t),t.tag){case 3:la(Ke),yt();break;case 26:case 27:case 5:ya(t);break;case 4:yt();break;case 13:ia(t);break;case 19:Q(Je);break;case 10:la(t.type);break;case 22:case 23:ia(t),Xs(),e!==null&&Q(rl);break;case 24:la(Ke)}}function Gn(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&e)===e){l=void 0;var r=a.create,d=a.inst;l=r(),d.destroy=l}a=a.next}while(a!==n)}}catch(m){Me(t,t.return,m)}}function za(e,t,a){try{var l=t.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var r=n.next;l=r;do{if((l.tag&e)===e){var d=l.inst,m=d.destroy;if(m!==void 0){d.destroy=void 0,n=t;var y=a,T=m;try{T()}catch(U){Me(n,y,U)}}}l=l.next}while(l!==r)}}catch(U){Me(t,t.return,U)}}function ff(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Po(t,a)}catch(l){Me(e,e.return,l)}}}function mf(e,t,a){a.props=cl(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){Me(e,t,l)}}function Yn(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(n){Me(e,t,n)}}function Qt(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){Me(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){Me(e,t,n)}else a.current=null}function hf(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){Me(e,e.return,n)}}function yc(e,t,a){try{var l=e.stateNode;V0(l,e.type,a,t),l[ft]=t}catch(n){Me(e,e.return,n)}}function gf(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ka(e.type)||e.tag===4}function vc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||gf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ka(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function bc(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=xr));else if(l!==4&&(l===27&&ka(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(bc(e,t,a),e=e.sibling;e!==null;)bc(e,t,a),e=e.sibling}function rr(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&ka(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(rr(e,t,a),e=e.sibling;e!==null;)rr(e,t,a),e=e.sibling}function pf(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);at(t,l,a),t[nt]=e,t[ft]=a}catch(r){Me(e,e.return,r)}}var ca=!1,Xe=!1,jc=!1,xf=typeof WeakSet=="function"?WeakSet:Set,Pe=null;function S0(e,t){if(e=e.containerInfo,Qc=Er,e=Co(e),vs(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,r=l.focusNode;l=l.focusOffset;try{a.nodeType,r.nodeType}catch{a=null;break e}var d=0,m=-1,y=-1,T=0,U=0,k=e,R=null;t:for(;;){for(var O;k!==a||n!==0&&k.nodeType!==3||(m=d+n),k!==r||l!==0&&k.nodeType!==3||(y=d+l),k.nodeType===3&&(d+=k.nodeValue.length),(O=k.firstChild)!==null;)R=k,k=O;for(;;){if(k===e)break t;if(R===a&&++T===n&&(m=d),R===r&&++U===l&&(y=d),(O=k.nextSibling)!==null)break;k=R,R=k.parentNode}k=O}a=m===-1||y===-1?null:{start:m,end:y}}else a=null}a=a||{start:0,end:0}}else a=null;for(Zc={focusedElem:e,selectionRange:a},Er=!1,Pe=t;Pe!==null;)if(t=Pe,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Pe=e;else for(;Pe!==null;){switch(t=Pe,r=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&r!==null){e=void 0,a=t,n=r.memoizedProps,r=r.memoizedState,l=a.stateNode;try{var ie=cl(a.type,n,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(ie,r),l.__reactInternalSnapshotBeforeUpdate=e}catch(ae){Me(a,a.return,ae)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)$c(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":$c(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(o(163))}if(e=t.sibling,e!==null){e.return=t.return,Pe=e;break}Pe=t.return}}function yf(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:Oa(e,a),l&4&&Gn(5,a);break;case 1:if(Oa(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(d){Me(a,a.return,d)}else{var n=cl(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(d){Me(a,a.return,d)}}l&64&&ff(a),l&512&&Yn(a,a.return);break;case 3:if(Oa(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Po(e,t)}catch(d){Me(a,a.return,d)}}break;case 27:t===null&&l&4&&pf(a);case 26:case 5:Oa(e,a),t===null&&l&4&&hf(a),l&512&&Yn(a,a.return);break;case 12:Oa(e,a);break;case 13:Oa(e,a),l&4&&jf(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=M0.bind(null,a),F0(e,a))));break;case 22:if(l=a.memoizedState!==null||ca,!l){t=t!==null&&t.memoizedState!==null||Xe,n=ca;var r=Xe;ca=l,(Xe=t)&&!r?Ma(e,a,(a.subtreeFlags&8772)!==0):Oa(e,a),ca=n,Xe=r}break;case 30:break;default:Oa(e,a)}}function vf(e){var t=e.alternate;t!==null&&(e.alternate=null,vf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&es(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Le=null,gt=!1;function ua(e,t,a){for(a=a.child;a!==null;)bf(e,t,a),a=a.sibling}function bf(e,t,a){if(vt&&typeof vt.onCommitFiberUnmount=="function")try{vt.onCommitFiberUnmount(un,a)}catch{}switch(a.tag){case 26:Xe||Qt(a,t),ua(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Xe||Qt(a,t);var l=Le,n=gt;ka(a.type)&&(Le=a.stateNode,gt=!1),ua(e,t,a),Wn(a.stateNode),Le=l,gt=n;break;case 5:Xe||Qt(a,t);case 6:if(l=Le,n=gt,Le=null,ua(e,t,a),Le=l,gt=n,Le!==null)if(gt)try{(Le.nodeType===9?Le.body:Le.nodeName==="HTML"?Le.ownerDocument.body:Le).removeChild(a.stateNode)}catch(r){Me(a,t,r)}else try{Le.removeChild(a.stateNode)}catch(r){Me(a,t,r)}break;case 18:Le!==null&&(gt?(e=Le,cm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),ii(e)):cm(Le,a.stateNode));break;case 4:l=Le,n=gt,Le=a.stateNode.containerInfo,gt=!0,ua(e,t,a),Le=l,gt=n;break;case 0:case 11:case 14:case 15:Xe||za(2,a,t),Xe||za(4,a,t),ua(e,t,a);break;case 1:Xe||(Qt(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&mf(a,t,l)),ua(e,t,a);break;case 21:ua(e,t,a);break;case 22:Xe=(l=Xe)||a.memoizedState!==null,ua(e,t,a),Xe=l;break;default:ua(e,t,a)}}function jf(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{ii(e)}catch(a){Me(t,t.return,a)}}function E0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new xf),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new xf),t;default:throw Error(o(435,e.tag))}}function Nc(e,t){var a=E0(e);t.forEach(function(l){var n=_0.bind(null,e,l);a.has(l)||(a.add(l),l.then(n,n))})}function St(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],r=e,d=t,m=d;e:for(;m!==null;){switch(m.tag){case 27:if(ka(m.type)){Le=m.stateNode,gt=!1;break e}break;case 5:Le=m.stateNode,gt=!1;break e;case 3:case 4:Le=m.stateNode.containerInfo,gt=!0;break e}m=m.return}if(Le===null)throw Error(o(160));bf(r,d,n),Le=null,gt=!1,r=n.alternate,r!==null&&(r.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Nf(t,e),t=t.sibling}var qt=null;function Nf(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:St(t,e),Et(e),l&4&&(za(3,e,e.return),Gn(3,e),za(5,e,e.return));break;case 1:St(t,e),Et(e),l&512&&(Xe||a===null||Qt(a,a.return)),l&64&&ca&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=qt;if(St(t,e),Et(e),l&512&&(Xe||a===null||Qt(a,a.return)),l&4){var r=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,n=n.ownerDocument||n;t:switch(l){case"title":r=n.getElementsByTagName("title")[0],(!r||r[fn]||r[nt]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=n.createElement(l),n.head.insertBefore(r,n.querySelector("head > title"))),at(r,l,a),r[nt]=e,Fe(r),l=r;break e;case"link":var d=pm("link","href",n).get(l+(a.href||""));if(d){for(var m=0;m<d.length;m++)if(r=d[m],r.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&r.getAttribute("rel")===(a.rel==null?null:a.rel)&&r.getAttribute("title")===(a.title==null?null:a.title)&&r.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){d.splice(m,1);break t}}r=n.createElement(l),at(r,l,a),n.head.appendChild(r);break;case"meta":if(d=pm("meta","content",n).get(l+(a.content||""))){for(m=0;m<d.length;m++)if(r=d[m],r.getAttribute("content")===(a.content==null?null:""+a.content)&&r.getAttribute("name")===(a.name==null?null:a.name)&&r.getAttribute("property")===(a.property==null?null:a.property)&&r.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&r.getAttribute("charset")===(a.charSet==null?null:a.charSet)){d.splice(m,1);break t}}r=n.createElement(l),at(r,l,a),n.head.appendChild(r);break;default:throw Error(o(468,l))}r[nt]=e,Fe(r),l=r}e.stateNode=l}else xm(n,e.type,e.stateNode);else e.stateNode=gm(n,l,e.memoizedProps);else r!==l?(r===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):r.count--,l===null?xm(n,e.type,e.stateNode):gm(n,l,e.memoizedProps)):l===null&&e.stateNode!==null&&yc(e,e.memoizedProps,a.memoizedProps)}break;case 27:St(t,e),Et(e),l&512&&(Xe||a===null||Qt(a,a.return)),a!==null&&l&4&&yc(e,e.memoizedProps,a.memoizedProps);break;case 5:if(St(t,e),Et(e),l&512&&(Xe||a===null||Qt(a,a.return)),e.flags&32){n=e.stateNode;try{jl(n,"")}catch(O){Me(e,e.return,O)}}l&4&&e.stateNode!=null&&(n=e.memoizedProps,yc(e,n,a!==null?a.memoizedProps:n)),l&1024&&(jc=!0);break;case 6:if(St(t,e),Et(e),l&4){if(e.stateNode===null)throw Error(o(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(O){Me(e,e.return,O)}}break;case 3:if(jr=null,n=qt,qt=vr(t.containerInfo),St(t,e),qt=n,Et(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{ii(t.containerInfo)}catch(O){Me(e,e.return,O)}jc&&(jc=!1,Sf(e));break;case 4:l=qt,qt=vr(e.stateNode.containerInfo),St(t,e),Et(e),qt=l;break;case 12:St(t,e),Et(e);break;case 13:St(t,e),Et(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Tc=Yt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Nc(e,l)));break;case 22:n=e.memoizedState!==null;var y=a!==null&&a.memoizedState!==null,T=ca,U=Xe;if(ca=T||n,Xe=U||y,St(t,e),Xe=U,ca=T,Et(e),l&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(a===null||y||ca||Xe||ul(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){y=a=t;try{if(r=y.stateNode,n)d=r.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{m=y.stateNode;var k=y.memoizedProps.style,R=k!=null&&k.hasOwnProperty("display")?k.display:null;m.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(O){Me(y,y.return,O)}}}else if(t.tag===6){if(a===null){y=t;try{y.stateNode.nodeValue=n?"":y.memoizedProps}catch(O){Me(y,y.return,O)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,Nc(e,a))));break;case 19:St(t,e),Et(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Nc(e,l)));break;case 30:break;case 21:break;default:St(t,e),Et(e)}}function Et(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(gf(l)){a=l;break}l=l.return}if(a==null)throw Error(o(160));switch(a.tag){case 27:var n=a.stateNode,r=vc(e);rr(e,r,n);break;case 5:var d=a.stateNode;a.flags&32&&(jl(d,""),a.flags&=-33);var m=vc(e);rr(e,m,d);break;case 3:case 4:var y=a.stateNode.containerInfo,T=vc(e);bc(e,T,y);break;default:throw Error(o(161))}}catch(U){Me(e,e.return,U)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Sf(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Sf(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Oa(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)yf(e,t.alternate,t),t=t.sibling}function ul(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:za(4,t,t.return),ul(t);break;case 1:Qt(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&mf(t,t.return,a),ul(t);break;case 27:Wn(t.stateNode);case 26:case 5:Qt(t,t.return),ul(t);break;case 22:t.memoizedState===null&&ul(t);break;case 30:ul(t);break;default:ul(t)}e=e.sibling}}function Ma(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,n=e,r=t,d=r.flags;switch(r.tag){case 0:case 11:case 15:Ma(n,r,a),Gn(4,r);break;case 1:if(Ma(n,r,a),l=r,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(T){Me(l,l.return,T)}if(l=r,n=l.updateQueue,n!==null){var m=l.stateNode;try{var y=n.shared.hiddenCallbacks;if(y!==null)for(n.shared.hiddenCallbacks=null,n=0;n<y.length;n++)Wo(y[n],m)}catch(T){Me(l,l.return,T)}}a&&d&64&&ff(r),Yn(r,r.return);break;case 27:pf(r);case 26:case 5:Ma(n,r,a),a&&l===null&&d&4&&hf(r),Yn(r,r.return);break;case 12:Ma(n,r,a);break;case 13:Ma(n,r,a),a&&d&4&&jf(n,r);break;case 22:r.memoizedState===null&&Ma(n,r,a),Yn(r,r.return);break;case 30:break;default:Ma(n,r,a)}t=t.sibling}}function Sc(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Cn(a))}function Ec(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Cn(e))}function Zt(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ef(e,t,a,l),t=t.sibling}function Ef(e,t,a,l){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Zt(e,t,a,l),n&2048&&Gn(9,t);break;case 1:Zt(e,t,a,l);break;case 3:Zt(e,t,a,l),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Cn(e)));break;case 12:if(n&2048){Zt(e,t,a,l),e=t.stateNode;try{var r=t.memoizedProps,d=r.id,m=r.onPostCommit;typeof m=="function"&&m(d,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(y){Me(t,t.return,y)}}else Zt(e,t,a,l);break;case 13:Zt(e,t,a,l);break;case 23:break;case 22:r=t.stateNode,d=t.alternate,t.memoizedState!==null?r._visibility&2?Zt(e,t,a,l):Vn(e,t):r._visibility&2?Zt(e,t,a,l):(r._visibility|=2,Bl(e,t,a,l,(t.subtreeFlags&10256)!==0)),n&2048&&Sc(d,t);break;case 24:Zt(e,t,a,l),n&2048&&Ec(t.alternate,t);break;default:Zt(e,t,a,l)}}function Bl(e,t,a,l,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var r=e,d=t,m=a,y=l,T=d.flags;switch(d.tag){case 0:case 11:case 15:Bl(r,d,m,y,n),Gn(8,d);break;case 23:break;case 22:var U=d.stateNode;d.memoizedState!==null?U._visibility&2?Bl(r,d,m,y,n):Vn(r,d):(U._visibility|=2,Bl(r,d,m,y,n)),n&&T&2048&&Sc(d.alternate,d);break;case 24:Bl(r,d,m,y,n),n&&T&2048&&Ec(d.alternate,d);break;default:Bl(r,d,m,y,n)}t=t.sibling}}function Vn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,n=l.flags;switch(l.tag){case 22:Vn(a,l),n&2048&&Sc(l.alternate,l);break;case 24:Vn(a,l),n&2048&&Ec(l.alternate,l);break;default:Vn(a,l)}t=t.sibling}}var Xn=8192;function Gl(e){if(e.subtreeFlags&Xn)for(e=e.child;e!==null;)wf(e),e=e.sibling}function wf(e){switch(e.tag){case 26:Gl(e),e.flags&Xn&&e.memoizedState!==null&&up(qt,e.memoizedState,e.memoizedProps);break;case 5:Gl(e);break;case 3:case 4:var t=qt;qt=vr(e.stateNode.containerInfo),Gl(e),qt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Xn,Xn=16777216,Gl(e),Xn=t):Gl(e));break;default:Gl(e)}}function Af(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Qn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Pe=l,Tf(l,e)}Af(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Cf(e),e=e.sibling}function Cf(e){switch(e.tag){case 0:case 11:case 15:Qn(e),e.flags&2048&&za(9,e,e.return);break;case 3:Qn(e);break;case 12:Qn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,sr(e)):Qn(e);break;default:Qn(e)}}function sr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Pe=l,Tf(l,e)}Af(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:za(8,t,t.return),sr(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,sr(t));break;default:sr(t)}e=e.sibling}}function Tf(e,t){for(;Pe!==null;){var a=Pe;switch(a.tag){case 0:case 11:case 15:za(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Cn(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Pe=l;else e:for(a=e;Pe!==null;){l=Pe;var n=l.sibling,r=l.return;if(vf(l),l===a){Pe=null;break e}if(n!==null){n.return=r,Pe=n;break e}Pe=r}}}var w0={getCacheForType:function(e){var t=it(Ke),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},A0=typeof WeakMap=="function"?WeakMap:Map,we=0,De=null,me=null,ve=0,Ae=0,wt=null,_a=!1,Yl=!1,wc=!1,oa=0,Ye=0,Da=0,ol=0,Ac=0,Ut=0,Vl=0,Zn=null,pt=null,Cc=!1,Tc=0,cr=1/0,ur=null,Ua=null,tt=0,La=null,Xl=null,Ql=0,Rc=0,zc=null,Rf=null,Kn=0,Oc=null;function At(){if((we&2)!==0&&ve!==0)return ve&-ve;if(_.T!==null){var e=Ml;return e!==0?e:kc()}return Vu()}function zf(){Ut===0&&(Ut=(ve&536870912)===0||Ne?qu():536870912);var e=Dt.current;return e!==null&&(e.flags|=32),Ut}function Ct(e,t,a){(e===De&&(Ae===2||Ae===9)||e.cancelPendingCommit!==null)&&(Zl(e,0),Ha(e,ve,Ut,!1)),dn(e,a),((we&2)===0||e!==De)&&(e===De&&((we&2)===0&&(ol|=a),Ye===4&&Ha(e,ve,Ut,!1)),Kt(e))}function Of(e,t,a){if((we&6)!==0)throw Error(o(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||on(e,t),n=l?R0(e,t):Dc(e,t,!0),r=l;do{if(n===0){Yl&&!l&&Ha(e,t,0,!1);break}else{if(a=e.current.alternate,r&&!C0(a)){n=Dc(e,t,!1),r=!1;continue}if(n===2){if(r=t,e.errorRecoveryDisabledLanes&r)var d=0;else d=e.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){t=d;e:{var m=e;n=Zn;var y=m.current.memoizedState.isDehydrated;if(y&&(Zl(m,d).flags|=256),d=Dc(m,d,!1),d!==2){if(wc&&!y){m.errorRecoveryDisabledLanes|=r,ol|=r,n=4;break e}r=pt,pt=n,r!==null&&(pt===null?pt=r:pt.push.apply(pt,r))}n=d}if(r=!1,n!==2)continue}}if(n===1){Zl(e,0),Ha(e,t,0,!0);break}e:{switch(l=e,r=n,r){case 0:case 1:throw Error(o(345));case 4:if((t&4194048)!==t)break;case 6:Ha(l,t,Ut,!_a);break e;case 2:pt=null;break;case 3:case 5:break;default:throw Error(o(329))}if((t&62914560)===t&&(n=Tc+300-Yt(),10<n)){if(Ha(l,t,Ut,!_a),bi(l,0,!0)!==0)break e;l.timeoutHandle=rm(Mf.bind(null,l,a,pt,ur,Cc,t,Ut,ol,Vl,_a,r,2,-0,0),n);break e}Mf(l,a,pt,ur,Cc,t,Ut,ol,Vl,_a,r,0,-0,0)}}break}while(!0);Kt(e)}function Mf(e,t,a,l,n,r,d,m,y,T,U,k,R,O){if(e.timeoutHandle=-1,k=t.subtreeFlags,(k&8192||(k&16785408)===16785408)&&(ei={stylesheets:null,count:0,unsuspend:cp},wf(t),k=op(),k!==null)){e.cancelPendingCommit=k(qf.bind(null,e,t,r,a,l,n,d,m,y,U,1,R,O)),Ha(e,r,d,!T);return}qf(e,t,r,a,l,n,d,m,y)}function C0(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],r=n.getSnapshot;n=n.value;try{if(!jt(r(),n))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ha(e,t,a,l){t&=~Ac,t&=~ol,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var n=t;0<n;){var r=31-bt(n),d=1<<r;l[r]=-1,n&=~d}a!==0&&Gu(e,a,t)}function or(){return(we&6)===0?(Jn(0),!1):!0}function Mc(){if(me!==null){if(Ae===0)var e=me.return;else e=me,aa=nl=null,$s(e),kl=null,kn=0,e=me;for(;e!==null;)df(e.alternate,e),e=e.return;me=null}}function Zl(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,Q0(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Mc(),De=e,me=a=It(e.current,null),ve=t,Ae=0,wt=null,_a=!1,Yl=on(e,t),wc=!1,Vl=Ut=Ac=ol=Da=Ye=0,pt=Zn=null,Cc=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var n=31-bt(l),r=1<<n;t|=e[n],l&=~r}return oa=t,Mi(),a}function _f(e,t){ue=null,_.H=Wi,t===Rn||t===Gi?(t=$o(),Ae=3):t===Zo?(t=$o(),Ae=4):Ae=t===Fd?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,wt=t,me===null&&(Ye=1,ar(e,zt(t,e.current)))}function Df(){var e=_.H;return _.H=Wi,e===null?Wi:e}function Uf(){var e=_.A;return _.A=w0,e}function _c(){Ye=4,_a||(ve&4194048)!==ve&&Dt.current!==null||(Yl=!0),(Da&134217727)===0&&(ol&134217727)===0||De===null||Ha(De,ve,Ut,!1)}function Dc(e,t,a){var l=we;we|=2;var n=Df(),r=Uf();(De!==e||ve!==t)&&(ur=null,Zl(e,t)),t=!1;var d=Ye;e:do try{if(Ae!==0&&me!==null){var m=me,y=wt;switch(Ae){case 8:Mc(),d=6;break e;case 3:case 2:case 9:case 6:Dt.current===null&&(t=!0);var T=Ae;if(Ae=0,wt=null,Kl(e,m,y,T),a&&Yl){d=0;break e}break;default:T=Ae,Ae=0,wt=null,Kl(e,m,y,T)}}T0(),d=Ye;break}catch(U){_f(e,U)}while(!0);return t&&e.shellSuspendCounter++,aa=nl=null,we=l,_.H=n,_.A=r,me===null&&(De=null,ve=0,Mi()),d}function T0(){for(;me!==null;)Lf(me)}function R0(e,t){var a=we;we|=2;var l=Df(),n=Uf();De!==e||ve!==t?(ur=null,cr=Yt()+500,Zl(e,t)):Yl=on(e,t);e:do try{if(Ae!==0&&me!==null){t=me;var r=wt;t:switch(Ae){case 1:Ae=0,wt=null,Kl(e,t,r,1);break;case 2:case 9:if(Ko(r)){Ae=0,wt=null,Hf(t);break}t=function(){Ae!==2&&Ae!==9||De!==e||(Ae=7),Kt(e)},r.then(t,t);break e;case 3:Ae=7;break e;case 4:Ae=5;break e;case 7:Ko(r)?(Ae=0,wt=null,Hf(t)):(Ae=0,wt=null,Kl(e,t,r,7));break;case 5:var d=null;switch(me.tag){case 26:d=me.memoizedState;case 5:case 27:var m=me;if(!d||ym(d)){Ae=0,wt=null;var y=m.sibling;if(y!==null)me=y;else{var T=m.return;T!==null?(me=T,dr(T)):me=null}break t}}Ae=0,wt=null,Kl(e,t,r,5);break;case 6:Ae=0,wt=null,Kl(e,t,r,6);break;case 8:Mc(),Ye=6;break e;default:throw Error(o(462))}}z0();break}catch(U){_f(e,U)}while(!0);return aa=nl=null,_.H=l,_.A=n,we=a,me!==null?0:(De=null,ve=0,Mi(),Ye)}function z0(){for(;me!==null&&!Ph();)Lf(me)}function Lf(e){var t=uf(e.alternate,e,oa);e.memoizedProps=e.pendingProps,t===null?dr(e):me=t}function Hf(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=af(a,t,t.pendingProps,t.type,void 0,ve);break;case 11:t=af(a,t,t.pendingProps,t.type.render,t.ref,ve);break;case 5:$s(t);default:df(a,t),t=me=Ho(t,oa),t=uf(a,t,oa)}e.memoizedProps=e.pendingProps,t===null?dr(e):me=t}function Kl(e,t,a,l){aa=nl=null,$s(t),kl=null,kn=0;var n=t.return;try{if(v0(e,n,t,a,ve)){Ye=1,ar(e,zt(a,e.current)),me=null;return}}catch(r){if(n!==null)throw me=n,r;Ye=1,ar(e,zt(a,e.current)),me=null;return}t.flags&32768?(Ne||l===1?e=!0:Yl||(ve&536870912)!==0?e=!1:(_a=e=!0,(l===2||l===9||l===3||l===6)&&(l=Dt.current,l!==null&&l.tag===13&&(l.flags|=16384))),kf(t,e)):dr(t)}function dr(e){var t=e;do{if((t.flags&32768)!==0){kf(t,_a);return}e=t.return;var a=j0(t.alternate,t,oa);if(a!==null){me=a;return}if(t=t.sibling,t!==null){me=t;return}me=t=e}while(t!==null);Ye===0&&(Ye=5)}function kf(e,t){do{var a=N0(e.alternate,e);if(a!==null){a.flags&=32767,me=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){me=e;return}me=e=a}while(e!==null);Ye=6,me=null}function qf(e,t,a,l,n,r,d,m,y){e.cancelPendingCommit=null;do fr();while(tt!==0);if((we&6)!==0)throw Error(o(327));if(t!==null){if(t===e.current)throw Error(o(177));if(r=t.lanes|t.childLanes,r|=Es,cg(e,a,r,d,m,y),e===De&&(me=De=null,ve=0),Xl=t,La=e,Ql=a,Rc=r,zc=n,Rf=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,D0(xi,function(){return Xf(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=_.T,_.T=null,n=X.p,X.p=2,d=we,we|=4;try{S0(e,t,a)}finally{we=d,X.p=n,_.T=l}}tt=1,Bf(),Gf(),Yf()}}function Bf(){if(tt===1){tt=0;var e=La,t=Xl,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=_.T,_.T=null;var l=X.p;X.p=2;var n=we;we|=4;try{Nf(t,e);var r=Zc,d=Co(e.containerInfo),m=r.focusedElem,y=r.selectionRange;if(d!==m&&m&&m.ownerDocument&&Ao(m.ownerDocument.documentElement,m)){if(y!==null&&vs(m)){var T=y.start,U=y.end;if(U===void 0&&(U=T),"selectionStart"in m)m.selectionStart=T,m.selectionEnd=Math.min(U,m.value.length);else{var k=m.ownerDocument||document,R=k&&k.defaultView||window;if(R.getSelection){var O=R.getSelection(),ie=m.textContent.length,ae=Math.min(y.start,ie),ze=y.end===void 0?ae:Math.min(y.end,ie);!O.extend&&ae>ze&&(d=ze,ze=ae,ae=d);var A=wo(m,ae),S=wo(m,ze);if(A&&S&&(O.rangeCount!==1||O.anchorNode!==A.node||O.anchorOffset!==A.offset||O.focusNode!==S.node||O.focusOffset!==S.offset)){var C=k.createRange();C.setStart(A.node,A.offset),O.removeAllRanges(),ae>ze?(O.addRange(C),O.extend(S.node,S.offset)):(C.setEnd(S.node,S.offset),O.addRange(C))}}}}for(k=[],O=m;O=O.parentNode;)O.nodeType===1&&k.push({element:O,left:O.scrollLeft,top:O.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<k.length;m++){var L=k[m];L.element.scrollLeft=L.left,L.element.scrollTop=L.top}}Er=!!Qc,Zc=Qc=null}finally{we=n,X.p=l,_.T=a}}e.current=t,tt=2}}function Gf(){if(tt===2){tt=0;var e=La,t=Xl,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=_.T,_.T=null;var l=X.p;X.p=2;var n=we;we|=4;try{yf(e,t.alternate,t)}finally{we=n,X.p=l,_.T=a}}tt=3}}function Yf(){if(tt===4||tt===3){tt=0,Ih();var e=La,t=Xl,a=Ql,l=Rf;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?tt=5:(tt=0,Xl=La=null,Vf(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(Ua=null),Pr(a),t=t.stateNode,vt&&typeof vt.onCommitFiberRoot=="function")try{vt.onCommitFiberRoot(un,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=_.T,n=X.p,X.p=2,_.T=null;try{for(var r=e.onRecoverableError,d=0;d<l.length;d++){var m=l[d];r(m.value,{componentStack:m.stack})}}finally{_.T=t,X.p=n}}(Ql&3)!==0&&fr(),Kt(e),n=e.pendingLanes,(a&4194090)!==0&&(n&42)!==0?e===Oc?Kn++:(Kn=0,Oc=e):Kn=0,Jn(0)}}function Vf(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Cn(t)))}function fr(e){return Bf(),Gf(),Yf(),Xf()}function Xf(){if(tt!==5)return!1;var e=La,t=Rc;Rc=0;var a=Pr(Ql),l=_.T,n=X.p;try{X.p=32>a?32:a,_.T=null,a=zc,zc=null;var r=La,d=Ql;if(tt=0,Xl=La=null,Ql=0,(we&6)!==0)throw Error(o(331));var m=we;if(we|=4,Cf(r.current),Ef(r,r.current,d,a),we=m,Jn(0,!1),vt&&typeof vt.onPostCommitFiberRoot=="function")try{vt.onPostCommitFiberRoot(un,r)}catch{}return!0}finally{X.p=n,_.T=l,Vf(e,t)}}function Qf(e,t,a){t=zt(a,t),t=uc(e.stateNode,t,2),e=Aa(e,t,2),e!==null&&(dn(e,2),Kt(e))}function Me(e,t,a){if(e.tag===3)Qf(e,e,a);else for(;t!==null;){if(t.tag===3){Qf(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Ua===null||!Ua.has(l))){e=zt(a,e),a=Jd(2),l=Aa(t,a,2),l!==null&&($d(a,l,t,e),dn(l,2),Kt(l));break}}t=t.return}}function Uc(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new A0;var n=new Set;l.set(t,n)}else n=l.get(t),n===void 0&&(n=new Set,l.set(t,n));n.has(a)||(wc=!0,n.add(a),e=O0.bind(null,e,t,a),t.then(e,e))}function O0(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,De===e&&(ve&a)===a&&(Ye===4||Ye===3&&(ve&62914560)===ve&&300>Yt()-Tc?(we&2)===0&&Zl(e,0):Ac|=a,Vl===ve&&(Vl=0)),Kt(e)}function Zf(e,t){t===0&&(t=Bu()),e=Tl(e,t),e!==null&&(dn(e,t),Kt(e))}function M0(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Zf(e,a)}function _0(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,n=e.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(o(314))}l!==null&&l.delete(t),Zf(e,a)}function D0(e,t){return Jr(e,t)}var mr=null,Jl=null,Lc=!1,hr=!1,Hc=!1,dl=0;function Kt(e){e!==Jl&&e.next===null&&(Jl===null?mr=Jl=e:Jl=Jl.next=e),hr=!0,Lc||(Lc=!0,L0())}function Jn(e,t){if(!Hc&&hr){Hc=!0;do for(var a=!1,l=mr;l!==null;){if(e!==0){var n=l.pendingLanes;if(n===0)var r=0;else{var d=l.suspendedLanes,m=l.pingedLanes;r=(1<<31-bt(42|e)+1)-1,r&=n&~(d&~m),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(a=!0,Ff(l,r))}else r=ve,r=bi(l,l===De?r:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(r&3)===0||on(l,r)||(a=!0,Ff(l,r));l=l.next}while(a);Hc=!1}}function U0(){Kf()}function Kf(){hr=Lc=!1;var e=0;dl!==0&&(X0()&&(e=dl),dl=0);for(var t=Yt(),a=null,l=mr;l!==null;){var n=l.next,r=Jf(l,t);r===0?(l.next=null,a===null?mr=n:a.next=n,n===null&&(Jl=a)):(a=l,(e!==0||(r&3)!==0)&&(hr=!0)),l=n}Jn(e)}function Jf(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,n=e.expirationTimes,r=e.pendingLanes&-62914561;0<r;){var d=31-bt(r),m=1<<d,y=n[d];y===-1?((m&a)===0||(m&l)!==0)&&(n[d]=sg(m,t)):y<=t&&(e.expiredLanes|=m),r&=~m}if(t=De,a=ve,a=bi(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(Ae===2||Ae===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&$r(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||on(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&$r(l),Pr(a)){case 2:case 8:a=Hu;break;case 32:a=xi;break;case 268435456:a=ku;break;default:a=xi}return l=$f.bind(null,e),a=Jr(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&$r(l),e.callbackPriority=2,e.callbackNode=null,2}function $f(e,t){if(tt!==0&&tt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(fr()&&e.callbackNode!==a)return null;var l=ve;return l=bi(e,e===De?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Of(e,l,t),Jf(e,Yt()),e.callbackNode!=null&&e.callbackNode===a?$f.bind(null,e):null)}function Ff(e,t){if(fr())return null;Of(e,t,!0)}function L0(){Z0(function(){(we&6)!==0?Jr(Lu,U0):Kf()})}function kc(){return dl===0&&(dl=qu()),dl}function Wf(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:wi(""+e)}function Pf(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function H0(e,t,a,l,n){if(t==="submit"&&a&&a.stateNode===n){var r=Wf((n[ft]||null).action),d=l.submitter;d&&(t=(t=d[ft]||null)?Wf(t.formAction):d.getAttribute("formAction"),t!==null&&(r=t,d=null));var m=new Ri("action","action",null,l,n);e.push({event:m,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(dl!==0){var y=d?Pf(n,d):new FormData(n);nc(a,{pending:!0,data:y,method:n.method,action:r},null,y)}}else typeof r=="function"&&(m.preventDefault(),y=d?Pf(n,d):new FormData(n),nc(a,{pending:!0,data:y,method:n.method,action:r},r,y))},currentTarget:n}]})}}for(var qc=0;qc<Ss.length;qc++){var Bc=Ss[qc],k0=Bc.toLowerCase(),q0=Bc[0].toUpperCase()+Bc.slice(1);kt(k0,"on"+q0)}kt(zo,"onAnimationEnd"),kt(Oo,"onAnimationIteration"),kt(Mo,"onAnimationStart"),kt("dblclick","onDoubleClick"),kt("focusin","onFocus"),kt("focusout","onBlur"),kt(a0,"onTransitionRun"),kt(l0,"onTransitionStart"),kt(n0,"onTransitionCancel"),kt(_o,"onTransitionEnd"),yl("onMouseEnter",["mouseout","mouseover"]),yl("onMouseLeave",["mouseout","mouseover"]),yl("onPointerEnter",["pointerout","pointerover"]),yl("onPointerLeave",["pointerout","pointerover"]),$a("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),$a("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),$a("onBeforeInput",["compositionend","keypress","textInput","paste"]),$a("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),$a("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),$a("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var $n="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),B0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat($n));function If(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],n=l.event;l=l.listeners;e:{var r=void 0;if(t)for(var d=l.length-1;0<=d;d--){var m=l[d],y=m.instance,T=m.currentTarget;if(m=m.listener,y!==r&&n.isPropagationStopped())break e;r=m,n.currentTarget=T;try{r(n)}catch(U){tr(U)}n.currentTarget=null,r=y}else for(d=0;d<l.length;d++){if(m=l[d],y=m.instance,T=m.currentTarget,m=m.listener,y!==r&&n.isPropagationStopped())break e;r=m,n.currentTarget=T;try{r(n)}catch(U){tr(U)}n.currentTarget=null,r=y}}}}function he(e,t){var a=t[Ir];a===void 0&&(a=t[Ir]=new Set);var l=e+"__bubble";a.has(l)||(em(t,e,2,!1),a.add(l))}function Gc(e,t,a){var l=0;t&&(l|=4),em(a,e,l,t)}var gr="_reactListening"+Math.random().toString(36).slice(2);function Yc(e){if(!e[gr]){e[gr]=!0,Qu.forEach(function(a){a!=="selectionchange"&&(B0.has(a)||Gc(a,!1,e),Gc(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[gr]||(t[gr]=!0,Gc("selectionchange",!1,t))}}function em(e,t,a,l){switch(Em(t)){case 2:var n=mp;break;case 8:n=hp;break;default:n=au}a=n.bind(null,t,a,e),n=void 0,!os||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),l?n!==void 0?e.addEventListener(t,a,{capture:!0,passive:n}):e.addEventListener(t,a,!0):n!==void 0?e.addEventListener(t,a,{passive:n}):e.addEventListener(t,a,!1)}function Vc(e,t,a,l,n){var r=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var d=l.tag;if(d===3||d===4){var m=l.stateNode.containerInfo;if(m===n)break;if(d===4)for(d=l.return;d!==null;){var y=d.tag;if((y===3||y===4)&&d.stateNode.containerInfo===n)return;d=d.return}for(;m!==null;){if(d=gl(m),d===null)return;if(y=d.tag,y===5||y===6||y===26||y===27){l=r=d;continue e}m=m.parentNode}}l=l.return}io(function(){var T=r,U=cs(a),k=[];e:{var R=Do.get(e);if(R!==void 0){var O=Ri,ie=e;switch(e){case"keypress":if(Ci(a)===0)break e;case"keydown":case"keyup":O=Dg;break;case"focusin":ie="focus",O=hs;break;case"focusout":ie="blur",O=hs;break;case"beforeblur":case"afterblur":O=hs;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":O=co;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":O=Ng;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":O=Hg;break;case zo:case Oo:case Mo:O=wg;break;case _o:O=qg;break;case"scroll":case"scrollend":O=bg;break;case"wheel":O=Gg;break;case"copy":case"cut":case"paste":O=Cg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":O=oo;break;case"toggle":case"beforetoggle":O=Vg}var ae=(t&4)!==0,ze=!ae&&(e==="scroll"||e==="scrollend"),A=ae?R!==null?R+"Capture":null:R;ae=[];for(var S=T,C;S!==null;){var L=S;if(C=L.stateNode,L=L.tag,L!==5&&L!==26&&L!==27||C===null||A===null||(L=hn(S,A),L!=null&&ae.push(Fn(S,L,C))),ze)break;S=S.return}0<ae.length&&(R=new O(R,ie,null,a,U),k.push({event:R,listeners:ae}))}}if((t&7)===0){e:{if(R=e==="mouseover"||e==="pointerover",O=e==="mouseout"||e==="pointerout",R&&a!==ss&&(ie=a.relatedTarget||a.fromElement)&&(gl(ie)||ie[hl]))break e;if((O||R)&&(R=U.window===U?U:(R=U.ownerDocument)?R.defaultView||R.parentWindow:window,O?(ie=a.relatedTarget||a.toElement,O=T,ie=ie?gl(ie):null,ie!==null&&(ze=h(ie),ae=ie.tag,ie!==ze||ae!==5&&ae!==27&&ae!==6)&&(ie=null)):(O=null,ie=T),O!==ie)){if(ae=co,L="onMouseLeave",A="onMouseEnter",S="mouse",(e==="pointerout"||e==="pointerover")&&(ae=oo,L="onPointerLeave",A="onPointerEnter",S="pointer"),ze=O==null?R:mn(O),C=ie==null?R:mn(ie),R=new ae(L,S+"leave",O,a,U),R.target=ze,R.relatedTarget=C,L=null,gl(U)===T&&(ae=new ae(A,S+"enter",ie,a,U),ae.target=C,ae.relatedTarget=ze,L=ae),ze=L,O&&ie)t:{for(ae=O,A=ie,S=0,C=ae;C;C=$l(C))S++;for(C=0,L=A;L;L=$l(L))C++;for(;0<S-C;)ae=$l(ae),S--;for(;0<C-S;)A=$l(A),C--;for(;S--;){if(ae===A||A!==null&&ae===A.alternate)break t;ae=$l(ae),A=$l(A)}ae=null}else ae=null;O!==null&&tm(k,R,O,ae,!1),ie!==null&&ze!==null&&tm(k,ze,ie,ae,!0)}}e:{if(R=T?mn(T):window,O=R.nodeName&&R.nodeName.toLowerCase(),O==="select"||O==="input"&&R.type==="file")var K=vo;else if(xo(R))if(bo)K=Ig;else{K=Wg;var de=Fg}else O=R.nodeName,!O||O.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?T&&rs(T.elementType)&&(K=vo):K=Pg;if(K&&(K=K(e,T))){yo(k,K,a,U);break e}de&&de(e,R,T),e==="focusout"&&T&&R.type==="number"&&T.memoizedProps.value!=null&&is(R,"number",R.value)}switch(de=T?mn(T):window,e){case"focusin":(xo(de)||de.contentEditable==="true")&&(wl=de,bs=T,Nn=null);break;case"focusout":Nn=bs=wl=null;break;case"mousedown":js=!0;break;case"contextmenu":case"mouseup":case"dragend":js=!1,To(k,a,U);break;case"selectionchange":if(t0)break;case"keydown":case"keyup":To(k,a,U)}var ee;if(ps)e:{switch(e){case"compositionstart":var le="onCompositionStart";break e;case"compositionend":le="onCompositionEnd";break e;case"compositionupdate":le="onCompositionUpdate";break e}le=void 0}else El?go(e,a)&&(le="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(le="onCompositionStart");le&&(fo&&a.locale!=="ko"&&(El||le!=="onCompositionStart"?le==="onCompositionEnd"&&El&&(ee=ro()):(Na=U,ds="value"in Na?Na.value:Na.textContent,El=!0)),de=pr(T,le),0<de.length&&(le=new uo(le,e,null,a,U),k.push({event:le,listeners:de}),ee?le.data=ee:(ee=po(a),ee!==null&&(le.data=ee)))),(ee=Qg?Zg(e,a):Kg(e,a))&&(le=pr(T,"onBeforeInput"),0<le.length&&(de=new uo("onBeforeInput","beforeinput",null,a,U),k.push({event:de,listeners:le}),de.data=ee)),H0(k,e,T,a,U)}If(k,t)})}function Fn(e,t,a){return{instance:e,listener:t,currentTarget:a}}function pr(e,t){for(var a=t+"Capture",l=[];e!==null;){var n=e,r=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||r===null||(n=hn(e,a),n!=null&&l.unshift(Fn(e,n,r)),n=hn(e,t),n!=null&&l.push(Fn(e,n,r))),e.tag===3)return l;e=e.return}return[]}function $l(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function tm(e,t,a,l,n){for(var r=t._reactName,d=[];a!==null&&a!==l;){var m=a,y=m.alternate,T=m.stateNode;if(m=m.tag,y!==null&&y===l)break;m!==5&&m!==26&&m!==27||T===null||(y=T,n?(T=hn(a,r),T!=null&&d.unshift(Fn(a,T,y))):n||(T=hn(a,r),T!=null&&d.push(Fn(a,T,y)))),a=a.return}d.length!==0&&e.push({event:t,listeners:d})}var G0=/\r\n?/g,Y0=/\u0000|\uFFFD/g;function am(e){return(typeof e=="string"?e:""+e).replace(G0,`
`).replace(Y0,"")}function lm(e,t){return t=am(t),am(e)===t}function xr(){}function Re(e,t,a,l,n,r){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||jl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&jl(e,""+l);break;case"className":Ni(e,"class",l);break;case"tabIndex":Ni(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Ni(e,a,l);break;case"style":lo(e,l,r);break;case"data":if(t!=="object"){Ni(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=wi(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(a==="formAction"?(t!=="input"&&Re(e,t,"name",n.name,n,null),Re(e,t,"formEncType",n.formEncType,n,null),Re(e,t,"formMethod",n.formMethod,n,null),Re(e,t,"formTarget",n.formTarget,n,null)):(Re(e,t,"encType",n.encType,n,null),Re(e,t,"method",n.method,n,null),Re(e,t,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=wi(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=xr);break;case"onScroll":l!=null&&he("scroll",e);break;case"onScrollEnd":l!=null&&he("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(o(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=wi(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":he("beforetoggle",e),he("toggle",e),ji(e,"popover",l);break;case"xlinkActuate":Wt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Wt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Wt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Wt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Wt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Wt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Wt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Wt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Wt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":ji(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=yg.get(a)||a,ji(e,a,l))}}function Xc(e,t,a,l,n,r){switch(a){case"style":lo(e,l,r);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(o(60));e.innerHTML=a}}break;case"children":typeof l=="string"?jl(e,l):(typeof l=="number"||typeof l=="bigint")&&jl(e,""+l);break;case"onScroll":l!=null&&he("scroll",e);break;case"onScrollEnd":l!=null&&he("scrollend",e);break;case"onClick":l!=null&&(e.onclick=xr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Zu.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),t=a.slice(2,n?a.length-7:void 0),r=e[ft]||null,r=r!=null?r[a]:null,typeof r=="function"&&e.removeEventListener(t,r,n),typeof l=="function")){typeof r!="function"&&r!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,n);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):ji(e,a,l)}}}function at(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":he("error",e),he("load",e);var l=!1,n=!1,r;for(r in a)if(a.hasOwnProperty(r)){var d=a[r];if(d!=null)switch(r){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Re(e,t,r,d,a,null)}}n&&Re(e,t,"srcSet",a.srcSet,a,null),l&&Re(e,t,"src",a.src,a,null);return;case"input":he("invalid",e);var m=r=d=n=null,y=null,T=null;for(l in a)if(a.hasOwnProperty(l)){var U=a[l];if(U!=null)switch(l){case"name":n=U;break;case"type":d=U;break;case"checked":y=U;break;case"defaultChecked":T=U;break;case"value":r=U;break;case"defaultValue":m=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(o(137,t));break;default:Re(e,t,l,U,a,null)}}Iu(e,r,m,y,T,d,n,!1),Si(e);return;case"select":he("invalid",e),l=d=r=null;for(n in a)if(a.hasOwnProperty(n)&&(m=a[n],m!=null))switch(n){case"value":r=m;break;case"defaultValue":d=m;break;case"multiple":l=m;default:Re(e,t,n,m,a,null)}t=r,a=d,e.multiple=!!l,t!=null?bl(e,!!l,t,!1):a!=null&&bl(e,!!l,a,!0);return;case"textarea":he("invalid",e),r=n=l=null;for(d in a)if(a.hasOwnProperty(d)&&(m=a[d],m!=null))switch(d){case"value":l=m;break;case"defaultValue":n=m;break;case"children":r=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(o(91));break;default:Re(e,t,d,m,a,null)}to(e,l,n,r),Si(e);return;case"option":for(y in a)if(a.hasOwnProperty(y)&&(l=a[y],l!=null))switch(y){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Re(e,t,y,l,a,null)}return;case"dialog":he("beforetoggle",e),he("toggle",e),he("cancel",e),he("close",e);break;case"iframe":case"object":he("load",e);break;case"video":case"audio":for(l=0;l<$n.length;l++)he($n[l],e);break;case"image":he("error",e),he("load",e);break;case"details":he("toggle",e);break;case"embed":case"source":case"link":he("error",e),he("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(T in a)if(a.hasOwnProperty(T)&&(l=a[T],l!=null))switch(T){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Re(e,t,T,l,a,null)}return;default:if(rs(t)){for(U in a)a.hasOwnProperty(U)&&(l=a[U],l!==void 0&&Xc(e,t,U,l,a,void 0));return}}for(m in a)a.hasOwnProperty(m)&&(l=a[m],l!=null&&Re(e,t,m,l,a,null))}function V0(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,r=null,d=null,m=null,y=null,T=null,U=null;for(O in a){var k=a[O];if(a.hasOwnProperty(O)&&k!=null)switch(O){case"checked":break;case"value":break;case"defaultValue":y=k;default:l.hasOwnProperty(O)||Re(e,t,O,null,l,k)}}for(var R in l){var O=l[R];if(k=a[R],l.hasOwnProperty(R)&&(O!=null||k!=null))switch(R){case"type":r=O;break;case"name":n=O;break;case"checked":T=O;break;case"defaultChecked":U=O;break;case"value":d=O;break;case"defaultValue":m=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(o(137,t));break;default:O!==k&&Re(e,t,R,O,l,k)}}ns(e,d,m,y,T,U,r,n);return;case"select":O=d=m=R=null;for(r in a)if(y=a[r],a.hasOwnProperty(r)&&y!=null)switch(r){case"value":break;case"multiple":O=y;default:l.hasOwnProperty(r)||Re(e,t,r,null,l,y)}for(n in l)if(r=l[n],y=a[n],l.hasOwnProperty(n)&&(r!=null||y!=null))switch(n){case"value":R=r;break;case"defaultValue":m=r;break;case"multiple":d=r;default:r!==y&&Re(e,t,n,r,l,y)}t=m,a=d,l=O,R!=null?bl(e,!!a,R,!1):!!l!=!!a&&(t!=null?bl(e,!!a,t,!0):bl(e,!!a,a?[]:"",!1));return;case"textarea":O=R=null;for(m in a)if(n=a[m],a.hasOwnProperty(m)&&n!=null&&!l.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:Re(e,t,m,null,l,n)}for(d in l)if(n=l[d],r=a[d],l.hasOwnProperty(d)&&(n!=null||r!=null))switch(d){case"value":R=n;break;case"defaultValue":O=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(o(91));break;default:n!==r&&Re(e,t,d,n,l,r)}eo(e,R,O);return;case"option":for(var ie in a)if(R=a[ie],a.hasOwnProperty(ie)&&R!=null&&!l.hasOwnProperty(ie))switch(ie){case"selected":e.selected=!1;break;default:Re(e,t,ie,null,l,R)}for(y in l)if(R=l[y],O=a[y],l.hasOwnProperty(y)&&R!==O&&(R!=null||O!=null))switch(y){case"selected":e.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:Re(e,t,y,R,l,O)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ae in a)R=a[ae],a.hasOwnProperty(ae)&&R!=null&&!l.hasOwnProperty(ae)&&Re(e,t,ae,null,l,R);for(T in l)if(R=l[T],O=a[T],l.hasOwnProperty(T)&&R!==O&&(R!=null||O!=null))switch(T){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(o(137,t));break;default:Re(e,t,T,R,l,O)}return;default:if(rs(t)){for(var ze in a)R=a[ze],a.hasOwnProperty(ze)&&R!==void 0&&!l.hasOwnProperty(ze)&&Xc(e,t,ze,void 0,l,R);for(U in l)R=l[U],O=a[U],!l.hasOwnProperty(U)||R===O||R===void 0&&O===void 0||Xc(e,t,U,R,l,O);return}}for(var A in a)R=a[A],a.hasOwnProperty(A)&&R!=null&&!l.hasOwnProperty(A)&&Re(e,t,A,null,l,R);for(k in l)R=l[k],O=a[k],!l.hasOwnProperty(k)||R===O||R==null&&O==null||Re(e,t,k,R,l,O)}var Qc=null,Zc=null;function yr(e){return e.nodeType===9?e:e.ownerDocument}function nm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function im(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Kc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Jc=null;function X0(){var e=window.event;return e&&e.type==="popstate"?e===Jc?!1:(Jc=e,!0):(Jc=null,!1)}var rm=typeof setTimeout=="function"?setTimeout:void 0,Q0=typeof clearTimeout=="function"?clearTimeout:void 0,sm=typeof Promise=="function"?Promise:void 0,Z0=typeof queueMicrotask=="function"?queueMicrotask:typeof sm<"u"?function(e){return sm.resolve(null).then(e).catch(K0)}:rm;function K0(e){setTimeout(function(){throw e})}function ka(e){return e==="head"}function cm(e,t){var a=t,l=0,n=0;do{var r=a.nextSibling;if(e.removeChild(a),r&&r.nodeType===8)if(a=r.data,a==="/$"){if(0<l&&8>l){a=l;var d=e.ownerDocument;if(a&1&&Wn(d.documentElement),a&2&&Wn(d.body),a&4)for(a=d.head,Wn(a),d=a.firstChild;d;){var m=d.nextSibling,y=d.nodeName;d[fn]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&d.rel.toLowerCase()==="stylesheet"||a.removeChild(d),d=m}}if(n===0){e.removeChild(r),ii(t);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=r}while(a);ii(t)}function $c(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":$c(a),es(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function J0(e,t,a,l){for(;e.nodeType===1;){var n=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[fn])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(r=e.getAttribute("rel"),r==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(r!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(r=e.getAttribute("src"),(r!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&r&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var r=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===r)return e}else return e;if(e=Bt(e.nextSibling),e===null)break}return null}function $0(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Bt(e.nextSibling),e===null))return null;return e}function Fc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function F0(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Bt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Wc=null;function um(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function om(e,t,a){switch(t=yr(a),e){case"html":if(e=t.documentElement,!e)throw Error(o(452));return e;case"head":if(e=t.head,!e)throw Error(o(453));return e;case"body":if(e=t.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}function Wn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);es(e)}var Lt=new Map,dm=new Set;function vr(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var da=X.d;X.d={f:W0,r:P0,D:I0,C:ep,L:tp,m:ap,X:np,S:lp,M:ip};function W0(){var e=da.f(),t=or();return e||t}function P0(e){var t=pl(e);t!==null&&t.tag===5&&t.type==="form"?zd(t):da.r(e)}var Fl=typeof document>"u"?null:document;function fm(e,t,a){var l=Fl;if(l&&typeof t=="string"&&t){var n=Rt(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),dm.has(n)||(dm.add(n),e={rel:e,crossOrigin:a,href:t},l.querySelector(n)===null&&(t=l.createElement("link"),at(t,"link",e),Fe(t),l.head.appendChild(t)))}}function I0(e){da.D(e),fm("dns-prefetch",e,null)}function ep(e,t){da.C(e,t),fm("preconnect",e,t)}function tp(e,t,a){da.L(e,t,a);var l=Fl;if(l&&e&&t){var n='link[rel="preload"][as="'+Rt(t)+'"]';t==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+Rt(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+Rt(a.imageSizes)+'"]')):n+='[href="'+Rt(e)+'"]';var r=n;switch(t){case"style":r=Wl(e);break;case"script":r=Pl(e)}Lt.has(r)||(e=b({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Lt.set(r,e),l.querySelector(n)!==null||t==="style"&&l.querySelector(Pn(r))||t==="script"&&l.querySelector(In(r))||(t=l.createElement("link"),at(t,"link",e),Fe(t),l.head.appendChild(t)))}}function ap(e,t){da.m(e,t);var a=Fl;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+Rt(l)+'"][href="'+Rt(e)+'"]',r=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=Pl(e)}if(!Lt.has(r)&&(e=b({rel:"modulepreload",href:e},t),Lt.set(r,e),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(In(r)))return}l=a.createElement("link"),at(l,"link",e),Fe(l),a.head.appendChild(l)}}}function lp(e,t,a){da.S(e,t,a);var l=Fl;if(l&&e){var n=xl(l).hoistableStyles,r=Wl(e);t=t||"default";var d=n.get(r);if(!d){var m={loading:0,preload:null};if(d=l.querySelector(Pn(r)))m.loading=5;else{e=b({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Lt.get(r))&&Pc(e,a);var y=d=l.createElement("link");Fe(y),at(y,"link",e),y._p=new Promise(function(T,U){y.onload=T,y.onerror=U}),y.addEventListener("load",function(){m.loading|=1}),y.addEventListener("error",function(){m.loading|=2}),m.loading|=4,br(d,t,l)}d={type:"stylesheet",instance:d,count:1,state:m},n.set(r,d)}}}function np(e,t){da.X(e,t);var a=Fl;if(a&&e){var l=xl(a).hoistableScripts,n=Pl(e),r=l.get(n);r||(r=a.querySelector(In(n)),r||(e=b({src:e,async:!0},t),(t=Lt.get(n))&&Ic(e,t),r=a.createElement("script"),Fe(r),at(r,"link",e),a.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(n,r))}}function ip(e,t){da.M(e,t);var a=Fl;if(a&&e){var l=xl(a).hoistableScripts,n=Pl(e),r=l.get(n);r||(r=a.querySelector(In(n)),r||(e=b({src:e,async:!0,type:"module"},t),(t=Lt.get(n))&&Ic(e,t),r=a.createElement("script"),Fe(r),at(r,"link",e),a.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(n,r))}}function mm(e,t,a,l){var n=(n=re.current)?vr(n):null;if(!n)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Wl(a.href),a=xl(n).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Wl(a.href);var r=xl(n).hoistableStyles,d=r.get(e);if(d||(n=n.ownerDocument||n,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(e,d),(r=n.querySelector(Pn(e)))&&!r._p&&(d.instance=r,d.state.loading=5),Lt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Lt.set(e,a),r||rp(n,e,a,d.state))),t&&l===null)throw Error(o(528,""));return d}if(t&&l!==null)throw Error(o(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Pl(a),a=xl(n).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function Wl(e){return'href="'+Rt(e)+'"'}function Pn(e){return'link[rel="stylesheet"]['+e+"]"}function hm(e){return b({},e,{"data-precedence":e.precedence,precedence:null})}function rp(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),at(t,"link",a),Fe(t),e.head.appendChild(t))}function Pl(e){return'[src="'+Rt(e)+'"]'}function In(e){return"script[async]"+e}function gm(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Rt(a.href)+'"]');if(l)return t.instance=l,Fe(l),l;var n=b({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Fe(l),at(l,"style",n),br(l,a.precedence,e),t.instance=l;case"stylesheet":n=Wl(a.href);var r=e.querySelector(Pn(n));if(r)return t.state.loading|=4,t.instance=r,Fe(r),r;l=hm(a),(n=Lt.get(n))&&Pc(l,n),r=(e.ownerDocument||e).createElement("link"),Fe(r);var d=r;return d._p=new Promise(function(m,y){d.onload=m,d.onerror=y}),at(r,"link",l),t.state.loading|=4,br(r,a.precedence,e),t.instance=r;case"script":return r=Pl(a.src),(n=e.querySelector(In(r)))?(t.instance=n,Fe(n),n):(l=a,(n=Lt.get(r))&&(l=b({},a),Ic(l,n)),e=e.ownerDocument||e,n=e.createElement("script"),Fe(n),at(n,"link",l),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(o(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,br(l,a.precedence,e));return t.instance}function br(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,r=n,d=0;d<l.length;d++){var m=l[d];if(m.dataset.precedence===t)r=m;else if(r!==n)break}r?r.parentNode.insertBefore(e,r.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Pc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Ic(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var jr=null;function pm(e,t,a){if(jr===null){var l=new Map,n=jr=new Map;n.set(a,l)}else n=jr,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),n=0;n<a.length;n++){var r=a[n];if(!(r[fn]||r[nt]||e==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var d=r.getAttribute(t)||"";d=e+d;var m=l.get(d);m?m.push(r):l.set(d,[r])}}return l}function xm(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function sp(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function ym(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var ei=null;function cp(){}function up(e,t,a){if(ei===null)throw Error(o(475));var l=ei;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=Wl(a.href),r=e.querySelector(Pn(n));if(r){e=r._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Nr.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=r,Fe(r);return}r=e.ownerDocument||e,a=hm(a),(n=Lt.get(n))&&Pc(a,n),r=r.createElement("link"),Fe(r);var d=r;d._p=new Promise(function(m,y){d.onload=m,d.onerror=y}),at(r,"link",a),t.instance=r}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Nr.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function op(){if(ei===null)throw Error(o(475));var e=ei;return e.stylesheets&&e.count===0&&eu(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&eu(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Nr(){if(this.count--,this.count===0){if(this.stylesheets)eu(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Sr=null;function eu(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Sr=new Map,t.forEach(dp,e),Sr=null,Nr.call(e))}function dp(e,t){if(!(t.state.loading&4)){var a=Sr.get(e);if(a)var l=a.get(null);else{a=new Map,Sr.set(e,a);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<n.length;r++){var d=n[r];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(a.set(d.dataset.precedence,d),l=d)}l&&a.set(null,l)}n=t.instance,d=n.getAttribute("data-precedence"),r=a.get(d)||l,r===l&&a.set(null,n),a.set(d,n),this.count++,l=Nr.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),r?r.parentNode.insertBefore(n,r.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var ti={$$typeof:I,Provider:null,Consumer:null,_currentValue:B,_currentValue2:B,_threadCount:0};function fp(e,t,a,l,n,r,d,m){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Fr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Fr(0),this.hiddenUpdates=Fr(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=r,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function vm(e,t,a,l,n,r,d,m,y,T,U,k){return e=new fp(e,t,a,d,m,y,T,k),t=1,r===!0&&(t|=24),r=Nt(3,null,null,t),e.current=r,r.stateNode=e,t=Us(),t.refCount++,e.pooledCache=t,t.refCount++,r.memoizedState={element:l,isDehydrated:a,cache:t},qs(r),e}function bm(e){return e?(e=Rl,e):Rl}function jm(e,t,a,l,n,r){n=bm(n),l.context===null?l.context=n:l.pendingContext=n,l=wa(t),l.payload={element:a},r=r===void 0?null:r,r!==null&&(l.callback=r),a=Aa(e,l,t),a!==null&&(Ct(a,e,t),On(a,e,t))}function Nm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function tu(e,t){Nm(e,t),(e=e.alternate)&&Nm(e,t)}function Sm(e){if(e.tag===13){var t=Tl(e,67108864);t!==null&&Ct(t,e,67108864),tu(e,67108864)}}var Er=!0;function mp(e,t,a,l){var n=_.T;_.T=null;var r=X.p;try{X.p=2,au(e,t,a,l)}finally{X.p=r,_.T=n}}function hp(e,t,a,l){var n=_.T;_.T=null;var r=X.p;try{X.p=8,au(e,t,a,l)}finally{X.p=r,_.T=n}}function au(e,t,a,l){if(Er){var n=lu(l);if(n===null)Vc(e,t,l,wr,a),wm(e,l);else if(pp(n,e,t,a,l))l.stopPropagation();else if(wm(e,l),t&4&&-1<gp.indexOf(e)){for(;n!==null;){var r=pl(n);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var d=Ja(r.pendingLanes);if(d!==0){var m=r;for(m.pendingLanes|=2,m.entangledLanes|=2;d;){var y=1<<31-bt(d);m.entanglements[1]|=y,d&=~y}Kt(r),(we&6)===0&&(cr=Yt()+500,Jn(0))}}break;case 13:m=Tl(r,2),m!==null&&Ct(m,r,2),or(),tu(r,2)}if(r=lu(l),r===null&&Vc(e,t,l,wr,a),r===n)break;n=r}n!==null&&l.stopPropagation()}else Vc(e,t,l,null,a)}}function lu(e){return e=cs(e),nu(e)}var wr=null;function nu(e){if(wr=null,e=gl(e),e!==null){var t=h(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=x(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return wr=e,null}function Em(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(eg()){case Lu:return 2;case Hu:return 8;case xi:case tg:return 32;case ku:return 268435456;default:return 32}default:return 32}}var iu=!1,qa=null,Ba=null,Ga=null,ai=new Map,li=new Map,Ya=[],gp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function wm(e,t){switch(e){case"focusin":case"focusout":qa=null;break;case"dragenter":case"dragleave":Ba=null;break;case"mouseover":case"mouseout":Ga=null;break;case"pointerover":case"pointerout":ai.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":li.delete(t.pointerId)}}function ni(e,t,a,l,n,r){return e===null||e.nativeEvent!==r?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:r,targetContainers:[n]},t!==null&&(t=pl(t),t!==null&&Sm(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function pp(e,t,a,l,n){switch(t){case"focusin":return qa=ni(qa,e,t,a,l,n),!0;case"dragenter":return Ba=ni(Ba,e,t,a,l,n),!0;case"mouseover":return Ga=ni(Ga,e,t,a,l,n),!0;case"pointerover":var r=n.pointerId;return ai.set(r,ni(ai.get(r)||null,e,t,a,l,n)),!0;case"gotpointercapture":return r=n.pointerId,li.set(r,ni(li.get(r)||null,e,t,a,l,n)),!0}return!1}function Am(e){var t=gl(e.target);if(t!==null){var a=h(t);if(a!==null){if(t=a.tag,t===13){if(t=x(a),t!==null){e.blockedOn=t,ug(e.priority,function(){if(a.tag===13){var l=At();l=Wr(l);var n=Tl(a,l);n!==null&&Ct(n,a,l),tu(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ar(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=lu(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);ss=l,a.target.dispatchEvent(l),ss=null}else return t=pl(a),t!==null&&Sm(t),e.blockedOn=a,!1;t.shift()}return!0}function Cm(e,t,a){Ar(e)&&a.delete(t)}function xp(){iu=!1,qa!==null&&Ar(qa)&&(qa=null),Ba!==null&&Ar(Ba)&&(Ba=null),Ga!==null&&Ar(Ga)&&(Ga=null),ai.forEach(Cm),li.forEach(Cm)}function Cr(e,t){e.blockedOn===t&&(e.blockedOn=null,iu||(iu=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,xp)))}var Tr=null;function Tm(e){Tr!==e&&(Tr=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Tr===e&&(Tr=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],n=e[t+2];if(typeof l!="function"){if(nu(l||a)===null)continue;break}var r=pl(a);r!==null&&(e.splice(t,3),t-=3,nc(r,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function ii(e){function t(y){return Cr(y,e)}qa!==null&&Cr(qa,e),Ba!==null&&Cr(Ba,e),Ga!==null&&Cr(Ga,e),ai.forEach(t),li.forEach(t);for(var a=0;a<Ya.length;a++){var l=Ya[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Ya.length&&(a=Ya[0],a.blockedOn===null);)Am(a),a.blockedOn===null&&Ya.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],r=a[l+1],d=n[ft]||null;if(typeof r=="function")d||Tm(a);else if(d){var m=null;if(r&&r.hasAttribute("formAction")){if(n=r,d=r[ft]||null)m=d.formAction;else if(nu(n)!==null)continue}else m=d.action;typeof m=="function"?a[l+1]=m:(a.splice(l,3),l-=3),Tm(a)}}}function ru(e){this._internalRoot=e}Rr.prototype.render=ru.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));var a=t.current,l=At();jm(a,l,e,t,null,null)},Rr.prototype.unmount=ru.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;jm(e.current,2,null,e,null,null),or(),t[hl]=null}};function Rr(e){this._internalRoot=e}Rr.prototype.unstable_scheduleHydration=function(e){if(e){var t=Vu();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Ya.length&&t!==0&&t<Ya[a].priority;a++);Ya.splice(a,0,e),a===0&&Am(e)}};var Rm=c.version;if(Rm!=="19.1.0")throw Error(o(527,Rm,"19.1.0"));X.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=p(t),e=e!==null?g(e):null,e=e===null?null:e.stateNode,e};var yp={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:_,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var zr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!zr.isDisabled&&zr.supportsFiber)try{un=zr.inject(yp),vt=zr}catch{}}return si.createRoot=function(e,t){if(!f(e))throw Error(o(299));var a=!1,l="",n=Xd,r=Qd,d=Zd,m=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(r=t.onCaughtError),t.onRecoverableError!==void 0&&(d=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(m=t.unstable_transitionCallbacks)),t=vm(e,1,!1,null,null,a,l,n,r,d,m,null),e[hl]=t.current,Yc(e),new ru(t)},si.hydrateRoot=function(e,t,a){if(!f(e))throw Error(o(299));var l=!1,n="",r=Xd,d=Qd,m=Zd,y=null,T=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(r=a.onUncaughtError),a.onCaughtError!==void 0&&(d=a.onCaughtError),a.onRecoverableError!==void 0&&(m=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(y=a.unstable_transitionCallbacks),a.formState!==void 0&&(T=a.formState)),t=vm(e,1,!0,t,a??null,l,n,r,d,m,y,T),t.context=bm(null),a=t.current,l=At(),l=Wr(l),n=wa(l),n.callback=null,Aa(a,n,l),a=l,t.current.lanes=a,dn(t,a),Kt(t),e[hl]=t.current,Yc(e),new Rr(t)},si.version="19.1.0",si}var qm;function Rp(){if(qm)return uu.exports;qm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(c){console.error(c)}}return i(),uu.exports=Tp(),uu.exports}var zp=Rp(),ci={},Bm;function Op(){if(Bm)return ci;Bm=1,Object.defineProperty(ci,"__esModule",{value:!0}),ci.parse=x,ci.serialize=g;const i=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,c=/^[\u0021-\u003A\u003C-\u007E]*$/,u=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,f=Object.prototype.toString,h=(()=>{const j=function(){};return j.prototype=Object.create(null),j})();function x(j,G){const M=new h,D=j.length;if(D<2)return M;const H=G?.decode||b;let z=0;do{const P=j.indexOf("=",z);if(P===-1)break;const I=j.indexOf(";",z),ce=I===-1?D:I;if(P>ce){z=j.lastIndexOf(";",P-1)+1;continue}const F=v(j,z,P),ne=p(j,P,F),pe=j.slice(F,ne);if(M[pe]===void 0){let V=v(j,P+1,ce),te=p(j,ce,V);const xe=H(j.slice(V,te));M[pe]=xe}z=ce+1}while(z<D);return M}function v(j,G,M){do{const D=j.charCodeAt(G);if(D!==32&&D!==9)return G}while(++G<M);return M}function p(j,G,M){for(;G>M;){const D=j.charCodeAt(--G);if(D!==32&&D!==9)return G+1}return M}function g(j,G,M){const D=M?.encode||encodeURIComponent;if(!i.test(j))throw new TypeError(`argument name is invalid: ${j}`);const H=D(G);if(!c.test(H))throw new TypeError(`argument val is invalid: ${G}`);let z=j+"="+H;if(!M)return z;if(M.maxAge!==void 0){if(!Number.isInteger(M.maxAge))throw new TypeError(`option maxAge is invalid: ${M.maxAge}`);z+="; Max-Age="+M.maxAge}if(M.domain){if(!u.test(M.domain))throw new TypeError(`option domain is invalid: ${M.domain}`);z+="; Domain="+M.domain}if(M.path){if(!o.test(M.path))throw new TypeError(`option path is invalid: ${M.path}`);z+="; Path="+M.path}if(M.expires){if(!E(M.expires)||!Number.isFinite(M.expires.valueOf()))throw new TypeError(`option expires is invalid: ${M.expires}`);z+="; Expires="+M.expires.toUTCString()}if(M.httpOnly&&(z+="; HttpOnly"),M.secure&&(z+="; Secure"),M.partitioned&&(z+="; Partitioned"),M.priority)switch(typeof M.priority=="string"?M.priority.toLowerCase():void 0){case"low":z+="; Priority=Low";break;case"medium":z+="; Priority=Medium";break;case"high":z+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${M.priority}`)}if(M.sameSite)switch(typeof M.sameSite=="string"?M.sameSite.toLowerCase():M.sameSite){case!0:case"strict":z+="; SameSite=Strict";break;case"lax":z+="; SameSite=Lax";break;case"none":z+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${M.sameSite}`)}return z}function b(j){if(j.indexOf("%")===-1)return j;try{return decodeURIComponent(j)}catch{return j}}function E(j){return f.call(j)==="[object Date]"}return ci}Op();var Gm="popstate";function Mp(i={}){function c(o,f){let{pathname:h,search:x,hash:v}=o.location;return vu("",{pathname:h,search:x,hash:v},f.state&&f.state.usr||null,f.state&&f.state.key||"default")}function u(o,f){return typeof f=="string"?f:fi(f)}return Dp(c,u,null,i)}function qe(i,c){if(i===!1||i===null||typeof i>"u")throw new Error(c)}function Gt(i,c){if(!i){typeof console<"u"&&console.warn(c);try{throw new Error(c)}catch{}}}function _p(){return Math.random().toString(36).substring(2,10)}function Ym(i,c){return{usr:i.state,key:i.key,idx:c}}function vu(i,c,u=null,o){return{pathname:typeof i=="string"?i:i.pathname,search:"",hash:"",...typeof c=="string"?ln(c):c,state:u,key:c&&c.key||o||_p()}}function fi({pathname:i="/",search:c="",hash:u=""}){return c&&c!=="?"&&(i+=c.charAt(0)==="?"?c:"?"+c),u&&u!=="#"&&(i+=u.charAt(0)==="#"?u:"#"+u),i}function ln(i){let c={};if(i){let u=i.indexOf("#");u>=0&&(c.hash=i.substring(u),i=i.substring(0,u));let o=i.indexOf("?");o>=0&&(c.search=i.substring(o),i=i.substring(0,o)),i&&(c.pathname=i)}return c}function Dp(i,c,u,o={}){let{window:f=document.defaultView,v5Compat:h=!1}=o,x=f.history,v="POP",p=null,g=b();g==null&&(g=0,x.replaceState({...x.state,idx:g},""));function b(){return(x.state||{idx:null}).idx}function E(){v="POP";let H=b(),z=H==null?null:H-g;g=H,p&&p({action:v,location:D.location,delta:z})}function j(H,z){v="PUSH";let P=vu(D.location,H,z);g=b()+1;let I=Ym(P,g),ce=D.createHref(P);try{x.pushState(I,"",ce)}catch(F){if(F instanceof DOMException&&F.name==="DataCloneError")throw F;f.location.assign(ce)}h&&p&&p({action:v,location:D.location,delta:1})}function G(H,z){v="REPLACE";let P=vu(D.location,H,z);g=b();let I=Ym(P,g),ce=D.createHref(P);x.replaceState(I,"",ce),h&&p&&p({action:v,location:D.location,delta:0})}function M(H){return Up(H)}let D={get action(){return v},get location(){return i(f,x)},listen(H){if(p)throw new Error("A history only accepts one active listener");return f.addEventListener(Gm,E),p=H,()=>{f.removeEventListener(Gm,E),p=null}},createHref(H){return c(f,H)},createURL:M,encodeLocation(H){let z=M(H);return{pathname:z.pathname,search:z.search,hash:z.hash}},push:j,replace:G,go(H){return x.go(H)}};return D}function Up(i,c=!1){let u="http://localhost";typeof window<"u"&&(u=window.location.origin!=="null"?window.location.origin:window.location.href),qe(u,"No window.location.(origin|href) available to create URL");let o=typeof i=="string"?i:fi(i);return o=o.replace(/ $/,"%20"),!c&&o.startsWith("//")&&(o=u+o),new URL(o,u)}function dh(i,c,u="/"){return Lp(i,c,u,!1)}function Lp(i,c,u,o){let f=typeof c=="string"?ln(c):c,h=ha(f.pathname||"/",u);if(h==null)return null;let x=fh(i);Hp(x);let v=null;for(let p=0;v==null&&p<x.length;++p){let g=Jp(h);v=Zp(x[p],g,o)}return v}function fh(i,c=[],u=[],o=""){let f=(h,x,v)=>{let p={relativePath:v===void 0?h.path||"":v,caseSensitive:h.caseSensitive===!0,childrenIndex:x,route:h};p.relativePath.startsWith("/")&&(qe(p.relativePath.startsWith(o),`Absolute route path "${p.relativePath}" nested under path "${o}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(o.length));let g=ma([o,p.relativePath]),b=u.concat(p);h.children&&h.children.length>0&&(qe(h.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${g}".`),fh(h.children,c,b,g)),!(h.path==null&&!h.index)&&c.push({path:g,score:Xp(g,h.index),routesMeta:b})};return i.forEach((h,x)=>{if(h.path===""||!h.path?.includes("?"))f(h,x);else for(let v of mh(h.path))f(h,x,v)}),c}function mh(i){let c=i.split("/");if(c.length===0)return[];let[u,...o]=c,f=u.endsWith("?"),h=u.replace(/\?$/,"");if(o.length===0)return f?[h,""]:[h];let x=mh(o.join("/")),v=[];return v.push(...x.map(p=>p===""?h:[h,p].join("/"))),f&&v.push(...x),v.map(p=>i.startsWith("/")&&p===""?"/":p)}function Hp(i){i.sort((c,u)=>c.score!==u.score?u.score-c.score:Qp(c.routesMeta.map(o=>o.childrenIndex),u.routesMeta.map(o=>o.childrenIndex)))}var kp=/^:[\w-]+$/,qp=3,Bp=2,Gp=1,Yp=10,Vp=-2,Vm=i=>i==="*";function Xp(i,c){let u=i.split("/"),o=u.length;return u.some(Vm)&&(o+=Vp),c&&(o+=Bp),u.filter(f=>!Vm(f)).reduce((f,h)=>f+(kp.test(h)?qp:h===""?Gp:Yp),o)}function Qp(i,c){return i.length===c.length&&i.slice(0,-1).every((o,f)=>o===c[f])?i[i.length-1]-c[c.length-1]:0}function Zp(i,c,u=!1){let{routesMeta:o}=i,f={},h="/",x=[];for(let v=0;v<o.length;++v){let p=o[v],g=v===o.length-1,b=h==="/"?c:c.slice(h.length)||"/",E=Gr({path:p.relativePath,caseSensitive:p.caseSensitive,end:g},b),j=p.route;if(!E&&g&&u&&!o[o.length-1].route.index&&(E=Gr({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},b)),!E)return null;Object.assign(f,E.params),x.push({params:f,pathname:ma([h,E.pathname]),pathnameBase:Pp(ma([h,E.pathnameBase])),route:j}),E.pathnameBase!=="/"&&(h=ma([h,E.pathnameBase]))}return x}function Gr(i,c){typeof i=="string"&&(i={path:i,caseSensitive:!1,end:!0});let[u,o]=Kp(i.path,i.caseSensitive,i.end),f=c.match(u);if(!f)return null;let h=f[0],x=h.replace(/(.)\/+$/,"$1"),v=f.slice(1);return{params:o.reduce((g,{paramName:b,isOptional:E},j)=>{if(b==="*"){let M=v[j]||"";x=h.slice(0,h.length-M.length).replace(/(.)\/+$/,"$1")}const G=v[j];return E&&!G?g[b]=void 0:g[b]=(G||"").replace(/%2F/g,"/"),g},{}),pathname:h,pathnameBase:x,pattern:i}}function Kp(i,c=!1,u=!0){Gt(i==="*"||!i.endsWith("*")||i.endsWith("/*"),`Route path "${i}" will be treated as if it were "${i.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${i.replace(/\*$/,"/*")}".`);let o=[],f="^"+i.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(x,v,p)=>(o.push({paramName:v,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return i.endsWith("*")?(o.push({paramName:"*"}),f+=i==="*"||i==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):u?f+="\\/*$":i!==""&&i!=="/"&&(f+="(?:(?=\\/|$))"),[new RegExp(f,c?void 0:"i"),o]}function Jp(i){try{return i.split("/").map(c=>decodeURIComponent(c).replace(/\//g,"%2F")).join("/")}catch(c){return Gt(!1,`The URL path "${i}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${c}).`),i}}function ha(i,c){if(c==="/")return i;if(!i.toLowerCase().startsWith(c.toLowerCase()))return null;let u=c.endsWith("/")?c.length-1:c.length,o=i.charAt(u);return o&&o!=="/"?null:i.slice(u)||"/"}function $p(i,c="/"){let{pathname:u,search:o="",hash:f=""}=typeof i=="string"?ln(i):i;return{pathname:u?u.startsWith("/")?u:Fp(u,c):c,search:Ip(o),hash:ex(f)}}function Fp(i,c){let u=c.replace(/\/+$/,"").split("/");return i.split("/").forEach(f=>{f===".."?u.length>1&&u.pop():f!=="."&&u.push(f)}),u.length>1?u.join("/"):"/"}function mu(i,c,u,o){return`Cannot include a '${i}' character in a manually specified \`to.${c}\` field [${JSON.stringify(o)}].  Please separate it out to the \`to.${u}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Wp(i){return i.filter((c,u)=>u===0||c.route.path&&c.route.path.length>0)}function hh(i){let c=Wp(i);return c.map((u,o)=>o===c.length-1?u.pathname:u.pathnameBase)}function gh(i,c,u,o=!1){let f;typeof i=="string"?f=ln(i):(f={...i},qe(!f.pathname||!f.pathname.includes("?"),mu("?","pathname","search",f)),qe(!f.pathname||!f.pathname.includes("#"),mu("#","pathname","hash",f)),qe(!f.search||!f.search.includes("#"),mu("#","search","hash",f)));let h=i===""||f.pathname==="",x=h?"/":f.pathname,v;if(x==null)v=u;else{let E=c.length-1;if(!o&&x.startsWith("..")){let j=x.split("/");for(;j[0]==="..";)j.shift(),E-=1;f.pathname=j.join("/")}v=E>=0?c[E]:"/"}let p=$p(f,v),g=x&&x!=="/"&&x.endsWith("/"),b=(h||x===".")&&u.endsWith("/");return!p.pathname.endsWith("/")&&(g||b)&&(p.pathname+="/"),p}var ma=i=>i.join("/").replace(/\/\/+/g,"/"),Pp=i=>i.replace(/\/+$/,"").replace(/^\/*/,"/"),Ip=i=>!i||i==="?"?"":i.startsWith("?")?i:"?"+i,ex=i=>!i||i==="#"?"":i.startsWith("#")?i:"#"+i;function tx(i){return i!=null&&typeof i.status=="number"&&typeof i.statusText=="string"&&typeof i.internal=="boolean"&&"data"in i}var ph=["POST","PUT","PATCH","DELETE"];new Set(ph);var ax=["GET",...ph];new Set(ax);var nn=w.createContext(null);nn.displayName="DataRouter";var Xr=w.createContext(null);Xr.displayName="DataRouterState";var xh=w.createContext({isTransitioning:!1});xh.displayName="ViewTransition";var lx=w.createContext(new Map);lx.displayName="Fetchers";var nx=w.createContext(null);nx.displayName="Await";var Jt=w.createContext(null);Jt.displayName="Navigation";var mi=w.createContext(null);mi.displayName="Location";var $t=w.createContext({outlet:null,matches:[],isDataRoute:!1});$t.displayName="Route";var Tu=w.createContext(null);Tu.displayName="RouteError";function ix(i,{relative:c}={}){qe(hi(),"useHref() may be used only in the context of a <Router> component.");let{basename:u,navigator:o}=w.useContext(Jt),{hash:f,pathname:h,search:x}=gi(i,{relative:c}),v=h;return u!=="/"&&(v=h==="/"?u:ma([u,h])),o.createHref({pathname:v,search:x,hash:f})}function hi(){return w.useContext(mi)!=null}function pa(){return qe(hi(),"useLocation() may be used only in the context of a <Router> component."),w.useContext(mi).location}var yh="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function vh(i){w.useContext(Jt).static||w.useLayoutEffect(i)}function xt(){let{isDataRoute:i}=w.useContext($t);return i?yx():rx()}function rx(){qe(hi(),"useNavigate() may be used only in the context of a <Router> component.");let i=w.useContext(nn),{basename:c,navigator:u}=w.useContext(Jt),{matches:o}=w.useContext($t),{pathname:f}=pa(),h=JSON.stringify(hh(o)),x=w.useRef(!1);return vh(()=>{x.current=!0}),w.useCallback((p,g={})=>{if(Gt(x.current,yh),!x.current)return;if(typeof p=="number"){u.go(p);return}let b=gh(p,JSON.parse(h),f,g.relative==="path");i==null&&c!=="/"&&(b.pathname=b.pathname==="/"?c:ma([c,b.pathname])),(g.replace?u.replace:u.push)(b,g.state,g)},[c,u,h,f,i])}w.createContext(null);function bh(){let{matches:i}=w.useContext($t),c=i[i.length-1];return c?c.params:{}}function gi(i,{relative:c}={}){let{matches:u}=w.useContext($t),{pathname:o}=pa(),f=JSON.stringify(hh(u));return w.useMemo(()=>gh(i,JSON.parse(f),o,c==="path"),[i,f,o,c])}function sx(i,c){return jh(i,c)}function jh(i,c,u,o){qe(hi(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:f}=w.useContext(Jt),{matches:h}=w.useContext($t),x=h[h.length-1],v=x?x.params:{},p=x?x.pathname:"/",g=x?x.pathnameBase:"/",b=x&&x.route;{let z=b&&b.path||"";Nh(p,!b||z.endsWith("*")||z.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${p}" (under <Route path="${z}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${z}"> to <Route path="${z==="/"?"*":`${z}/*`}">.`)}let E=pa(),j;if(c){let z=typeof c=="string"?ln(c):c;qe(g==="/"||z.pathname?.startsWith(g),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${g}" but pathname "${z.pathname}" was given in the \`location\` prop.`),j=z}else j=E;let G=j.pathname||"/",M=G;if(g!=="/"){let z=g.replace(/^\//,"").split("/");M="/"+G.replace(/^\//,"").split("/").slice(z.length).join("/")}let D=dh(i,{pathname:M});Gt(b||D!=null,`No routes matched location "${j.pathname}${j.search}${j.hash}" `),Gt(D==null||D[D.length-1].route.element!==void 0||D[D.length-1].route.Component!==void 0||D[D.length-1].route.lazy!==void 0,`Matched leaf route at location "${j.pathname}${j.search}${j.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let H=fx(D&&D.map(z=>Object.assign({},z,{params:Object.assign({},v,z.params),pathname:ma([g,f.encodeLocation?f.encodeLocation(z.pathname).pathname:z.pathname]),pathnameBase:z.pathnameBase==="/"?g:ma([g,f.encodeLocation?f.encodeLocation(z.pathnameBase).pathname:z.pathnameBase])})),h,u,o);return c&&H?w.createElement(mi.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...j},navigationType:"POP"}},H):H}function cx(){let i=xx(),c=tx(i)?`${i.status} ${i.statusText}`:i instanceof Error?i.message:JSON.stringify(i),u=i instanceof Error?i.stack:null,o="rgba(200,200,200, 0.5)",f={padding:"0.5rem",backgroundColor:o},h={padding:"2px 4px",backgroundColor:o},x=null;return console.error("Error handled by React Router default ErrorBoundary:",i),x=w.createElement(w.Fragment,null,w.createElement("p",null,"💿 Hey developer 👋"),w.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",w.createElement("code",{style:h},"ErrorBoundary")," or"," ",w.createElement("code",{style:h},"errorElement")," prop on your route.")),w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},c),u?w.createElement("pre",{style:f},u):null,x)}var ux=w.createElement(cx,null),ox=class extends w.Component{constructor(i){super(i),this.state={location:i.location,revalidation:i.revalidation,error:i.error}}static getDerivedStateFromError(i){return{error:i}}static getDerivedStateFromProps(i,c){return c.location!==i.location||c.revalidation!=="idle"&&i.revalidation==="idle"?{error:i.error,location:i.location,revalidation:i.revalidation}:{error:i.error!==void 0?i.error:c.error,location:c.location,revalidation:i.revalidation||c.revalidation}}componentDidCatch(i,c){console.error("React Router caught the following error during render",i,c)}render(){return this.state.error!==void 0?w.createElement($t.Provider,{value:this.props.routeContext},w.createElement(Tu.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function dx({routeContext:i,match:c,children:u}){let o=w.useContext(nn);return o&&o.static&&o.staticContext&&(c.route.errorElement||c.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=c.route.id),w.createElement($t.Provider,{value:i},u)}function fx(i,c=[],u=null,o=null){if(i==null){if(!u)return null;if(u.errors)i=u.matches;else if(c.length===0&&!u.initialized&&u.matches.length>0)i=u.matches;else return null}let f=i,h=u?.errors;if(h!=null){let p=f.findIndex(g=>g.route.id&&h?.[g.route.id]!==void 0);qe(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(h).join(",")}`),f=f.slice(0,Math.min(f.length,p+1))}let x=!1,v=-1;if(u)for(let p=0;p<f.length;p++){let g=f[p];if((g.route.HydrateFallback||g.route.hydrateFallbackElement)&&(v=p),g.route.id){let{loaderData:b,errors:E}=u,j=g.route.loader&&!b.hasOwnProperty(g.route.id)&&(!E||E[g.route.id]===void 0);if(g.route.lazy||j){x=!0,v>=0?f=f.slice(0,v+1):f=[f[0]];break}}}return f.reduceRight((p,g,b)=>{let E,j=!1,G=null,M=null;u&&(E=h&&g.route.id?h[g.route.id]:void 0,G=g.route.errorElement||ux,x&&(v<0&&b===0?(Nh("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),j=!0,M=null):v===b&&(j=!0,M=g.route.hydrateFallbackElement||null)));let D=c.concat(f.slice(0,b+1)),H=()=>{let z;return E?z=G:j?z=M:g.route.Component?z=w.createElement(g.route.Component,null):g.route.element?z=g.route.element:z=p,w.createElement(dx,{match:g,routeContext:{outlet:p,matches:D,isDataRoute:u!=null},children:z})};return u&&(g.route.ErrorBoundary||g.route.errorElement||b===0)?w.createElement(ox,{location:u.location,revalidation:u.revalidation,component:G,error:E,children:H(),routeContext:{outlet:null,matches:D,isDataRoute:!0}}):H()},null)}function Ru(i){return`${i} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function mx(i){let c=w.useContext(nn);return qe(c,Ru(i)),c}function hx(i){let c=w.useContext(Xr);return qe(c,Ru(i)),c}function gx(i){let c=w.useContext($t);return qe(c,Ru(i)),c}function zu(i){let c=gx(i),u=c.matches[c.matches.length-1];return qe(u.route.id,`${i} can only be used on routes that contain a unique "id"`),u.route.id}function px(){return zu("useRouteId")}function xx(){let i=w.useContext(Tu),c=hx("useRouteError"),u=zu("useRouteError");return i!==void 0?i:c.errors?.[u]}function yx(){let{router:i}=mx("useNavigate"),c=zu("useNavigate"),u=w.useRef(!1);return vh(()=>{u.current=!0}),w.useCallback(async(f,h={})=>{Gt(u.current,yh),u.current&&(typeof f=="number"?i.navigate(f):await i.navigate(f,{fromRouteId:c,...h}))},[i,c])}var Xm={};function Nh(i,c,u){!c&&!Xm[i]&&(Xm[i]=!0,Gt(!1,u))}w.memo(vx);function vx({routes:i,future:c,state:u}){return jh(i,void 0,u,c)}function dt(i){qe(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function bx({basename:i="/",children:c=null,location:u,navigationType:o="POP",navigator:f,static:h=!1}){qe(!hi(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let x=i.replace(/^\/*/,"/"),v=w.useMemo(()=>({basename:x,navigator:f,static:h,future:{}}),[x,f,h]);typeof u=="string"&&(u=ln(u));let{pathname:p="/",search:g="",hash:b="",state:E=null,key:j="default"}=u,G=w.useMemo(()=>{let M=ha(p,x);return M==null?null:{location:{pathname:M,search:g,hash:b,state:E,key:j},navigationType:o}},[x,p,g,b,E,j,o]);return Gt(G!=null,`<Router basename="${x}"> is not able to match the URL "${p}${g}${b}" because it does not start with the basename, so the <Router> won't render anything.`),G==null?null:w.createElement(Jt.Provider,{value:v},w.createElement(mi.Provider,{children:c,value:G}))}function jx({children:i,location:c}){return sx(bu(i),c)}function bu(i,c=[]){let u=[];return w.Children.forEach(i,(o,f)=>{if(!w.isValidElement(o))return;let h=[...c,f];if(o.type===w.Fragment){u.push.apply(u,bu(o.props.children,h));return}qe(o.type===dt,`[${typeof o.type=="string"?o.type:o.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),qe(!o.props.index||!o.props.children,"An index route cannot have child routes.");let x={id:o.props.id||h.join("-"),caseSensitive:o.props.caseSensitive,element:o.props.element,Component:o.props.Component,index:o.props.index,path:o.props.path,loader:o.props.loader,action:o.props.action,hydrateFallbackElement:o.props.hydrateFallbackElement,HydrateFallback:o.props.HydrateFallback,errorElement:o.props.errorElement,ErrorBoundary:o.props.ErrorBoundary,hasErrorBoundary:o.props.hasErrorBoundary===!0||o.props.ErrorBoundary!=null||o.props.errorElement!=null,shouldRevalidate:o.props.shouldRevalidate,handle:o.props.handle,lazy:o.props.lazy};o.props.children&&(x.children=bu(o.props.children,h)),u.push(x)}),u}var kr="get",qr="application/x-www-form-urlencoded";function Qr(i){return i!=null&&typeof i.tagName=="string"}function Nx(i){return Qr(i)&&i.tagName.toLowerCase()==="button"}function Sx(i){return Qr(i)&&i.tagName.toLowerCase()==="form"}function Ex(i){return Qr(i)&&i.tagName.toLowerCase()==="input"}function wx(i){return!!(i.metaKey||i.altKey||i.ctrlKey||i.shiftKey)}function Ax(i,c){return i.button===0&&(!c||c==="_self")&&!wx(i)}function ju(i=""){return new URLSearchParams(typeof i=="string"||Array.isArray(i)||i instanceof URLSearchParams?i:Object.keys(i).reduce((c,u)=>{let o=i[u];return c.concat(Array.isArray(o)?o.map(f=>[u,f]):[[u,o]])},[]))}function Cx(i,c){let u=ju(i);return c&&c.forEach((o,f)=>{u.has(f)||c.getAll(f).forEach(h=>{u.append(f,h)})}),u}var Or=null;function Tx(){if(Or===null)try{new FormData(document.createElement("form"),0),Or=!1}catch{Or=!0}return Or}var Rx=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function hu(i){return i!=null&&!Rx.has(i)?(Gt(!1,`"${i}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${qr}"`),null):i}function zx(i,c){let u,o,f,h,x;if(Sx(i)){let v=i.getAttribute("action");o=v?ha(v,c):null,u=i.getAttribute("method")||kr,f=hu(i.getAttribute("enctype"))||qr,h=new FormData(i)}else if(Nx(i)||Ex(i)&&(i.type==="submit"||i.type==="image")){let v=i.form;if(v==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=i.getAttribute("formaction")||v.getAttribute("action");if(o=p?ha(p,c):null,u=i.getAttribute("formmethod")||v.getAttribute("method")||kr,f=hu(i.getAttribute("formenctype"))||hu(v.getAttribute("enctype"))||qr,h=new FormData(v,i),!Tx()){let{name:g,type:b,value:E}=i;if(b==="image"){let j=g?`${g}.`:"";h.append(`${j}x`,"0"),h.append(`${j}y`,"0")}else g&&h.append(g,E)}}else{if(Qr(i))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');u=kr,o=null,f=qr,x=i}return h&&f==="text/plain"&&(x=h,h=void 0),{action:o,method:u.toLowerCase(),encType:f,formData:h,body:x}}function Ou(i,c){if(i===!1||i===null||typeof i>"u")throw new Error(c)}async function Ox(i,c){if(i.id in c)return c[i.id];try{let u=await import(i.module);return c[i.id]=u,u}catch(u){return console.error(`Error loading route module \`${i.module}\`, reloading page...`),console.error(u),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Mx(i){return i==null?!1:i.href==null?i.rel==="preload"&&typeof i.imageSrcSet=="string"&&typeof i.imageSizes=="string":typeof i.rel=="string"&&typeof i.href=="string"}async function _x(i,c,u){let o=await Promise.all(i.map(async f=>{let h=c.routes[f.route.id];if(h){let x=await Ox(h,u);return x.links?x.links():[]}return[]}));return Hx(o.flat(1).filter(Mx).filter(f=>f.rel==="stylesheet"||f.rel==="preload").map(f=>f.rel==="stylesheet"?{...f,rel:"prefetch",as:"style"}:{...f,rel:"prefetch"}))}function Qm(i,c,u,o,f,h){let x=(p,g)=>u[g]?p.route.id!==u[g].route.id:!0,v=(p,g)=>u[g].pathname!==p.pathname||u[g].route.path?.endsWith("*")&&u[g].params["*"]!==p.params["*"];return h==="assets"?c.filter((p,g)=>x(p,g)||v(p,g)):h==="data"?c.filter((p,g)=>{let b=o.routes[p.route.id];if(!b||!b.hasLoader)return!1;if(x(p,g)||v(p,g))return!0;if(p.route.shouldRevalidate){let E=p.route.shouldRevalidate({currentUrl:new URL(f.pathname+f.search+f.hash,window.origin),currentParams:u[0]?.params||{},nextUrl:new URL(i,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof E=="boolean")return E}return!0}):[]}function Dx(i,c,{includeHydrateFallback:u}={}){return Ux(i.map(o=>{let f=c.routes[o.route.id];if(!f)return[];let h=[f.module];return f.clientActionModule&&(h=h.concat(f.clientActionModule)),f.clientLoaderModule&&(h=h.concat(f.clientLoaderModule)),u&&f.hydrateFallbackModule&&(h=h.concat(f.hydrateFallbackModule)),f.imports&&(h=h.concat(f.imports)),h}).flat(1))}function Ux(i){return[...new Set(i)]}function Lx(i){let c={},u=Object.keys(i).sort();for(let o of u)c[o]=i[o];return c}function Hx(i,c){let u=new Set;return new Set(c),i.reduce((o,f)=>{let h=JSON.stringify(Lx(f));return u.has(h)||(u.add(h),o.push({key:h,link:f})),o},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var kx=new Set([100,101,204,205]);function qx(i,c){let u=typeof i=="string"?new URL(i,typeof window>"u"?"server://singlefetch/":window.location.origin):i;return u.pathname==="/"?u.pathname="_root.data":c&&ha(u.pathname,c)==="/"?u.pathname=`${c.replace(/\/$/,"")}/_root.data`:u.pathname=`${u.pathname.replace(/\/$/,"")}.data`,u}function Sh(){let i=w.useContext(nn);return Ou(i,"You must render this element inside a <DataRouterContext.Provider> element"),i}function Bx(){let i=w.useContext(Xr);return Ou(i,"You must render this element inside a <DataRouterStateContext.Provider> element"),i}var Mu=w.createContext(void 0);Mu.displayName="FrameworkContext";function Eh(){let i=w.useContext(Mu);return Ou(i,"You must render this element inside a <HydratedRouter> element"),i}function Gx(i,c){let u=w.useContext(Mu),[o,f]=w.useState(!1),[h,x]=w.useState(!1),{onFocus:v,onBlur:p,onMouseEnter:g,onMouseLeave:b,onTouchStart:E}=c,j=w.useRef(null);w.useEffect(()=>{if(i==="render"&&x(!0),i==="viewport"){let D=z=>{z.forEach(P=>{x(P.isIntersecting)})},H=new IntersectionObserver(D,{threshold:.5});return j.current&&H.observe(j.current),()=>{H.disconnect()}}},[i]),w.useEffect(()=>{if(o){let D=setTimeout(()=>{x(!0)},100);return()=>{clearTimeout(D)}}},[o]);let G=()=>{f(!0)},M=()=>{f(!1),x(!1)};return u?i!=="intent"?[h,j,{}]:[h,j,{onFocus:ui(v,G),onBlur:ui(p,M),onMouseEnter:ui(g,G),onMouseLeave:ui(b,M),onTouchStart:ui(E,G)}]:[!1,j,{}]}function ui(i,c){return u=>{i&&i(u),u.defaultPrevented||c(u)}}function Yx({page:i,...c}){let{router:u}=Sh(),o=w.useMemo(()=>dh(u.routes,i,u.basename),[u.routes,i,u.basename]);return o?w.createElement(Xx,{page:i,matches:o,...c}):null}function Vx(i){let{manifest:c,routeModules:u}=Eh(),[o,f]=w.useState([]);return w.useEffect(()=>{let h=!1;return _x(i,c,u).then(x=>{h||f(x)}),()=>{h=!0}},[i,c,u]),o}function Xx({page:i,matches:c,...u}){let o=pa(),{manifest:f,routeModules:h}=Eh(),{basename:x}=Sh(),{loaderData:v,matches:p}=Bx(),g=w.useMemo(()=>Qm(i,c,p,f,o,"data"),[i,c,p,f,o]),b=w.useMemo(()=>Qm(i,c,p,f,o,"assets"),[i,c,p,f,o]),E=w.useMemo(()=>{if(i===o.pathname+o.search+o.hash)return[];let M=new Set,D=!1;if(c.forEach(z=>{let P=f.routes[z.route.id];!P||!P.hasLoader||(!g.some(I=>I.route.id===z.route.id)&&z.route.id in v&&h[z.route.id]?.shouldRevalidate||P.hasClientLoader?D=!0:M.add(z.route.id))}),M.size===0)return[];let H=qx(i,x);return D&&M.size>0&&H.searchParams.set("_routes",c.filter(z=>M.has(z.route.id)).map(z=>z.route.id).join(",")),[H.pathname+H.search]},[x,v,o,f,g,c,i,h]),j=w.useMemo(()=>Dx(b,f),[b,f]),G=Vx(b);return w.createElement(w.Fragment,null,E.map(M=>w.createElement("link",{key:M,rel:"prefetch",as:"fetch",href:M,...u})),j.map(M=>w.createElement("link",{key:M,rel:"modulepreload",href:M,...u})),G.map(({key:M,link:D})=>w.createElement("link",{key:M,...D})))}function Qx(...i){return c=>{i.forEach(u=>{typeof u=="function"?u(c):u!=null&&(u.current=c)})}}var wh=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{wh&&(window.__reactRouterVersion="7.6.3")}catch{}function Zx({basename:i,children:c,window:u}){let o=w.useRef();o.current==null&&(o.current=Mp({window:u,v5Compat:!0}));let f=o.current,[h,x]=w.useState({action:f.action,location:f.location}),v=w.useCallback(p=>{w.startTransition(()=>x(p))},[x]);return w.useLayoutEffect(()=>f.listen(v),[f,v]),w.createElement(bx,{basename:i,children:c,location:h.location,navigationType:h.action,navigator:f})}var Ah=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,_u=w.forwardRef(function({onClick:c,discover:u="render",prefetch:o="none",relative:f,reloadDocument:h,replace:x,state:v,target:p,to:g,preventScrollReset:b,viewTransition:E,...j},G){let{basename:M}=w.useContext(Jt),D=typeof g=="string"&&Ah.test(g),H,z=!1;if(typeof g=="string"&&D&&(H=g,wh))try{let te=new URL(window.location.href),xe=g.startsWith("//")?new URL(te.protocol+g):new URL(g),Ue=ha(xe.pathname,M);xe.origin===te.origin&&Ue!=null?g=Ue+xe.search+xe.hash:z=!0}catch{Gt(!1,`<Link to="${g}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let P=ix(g,{relative:f}),[I,ce,F]=Gx(o,j),ne=Fx(g,{replace:x,state:v,target:p,preventScrollReset:b,relative:f,viewTransition:E});function pe(te){c&&c(te),te.defaultPrevented||ne(te)}let V=w.createElement("a",{...j,...F,href:H||P,onClick:z||h?c:pe,ref:Qx(G,ce),target:p,"data-discover":!D&&u==="render"?"true":void 0});return I&&!D?w.createElement(w.Fragment,null,V,w.createElement(Yx,{page:P})):V});_u.displayName="Link";var Kx=w.forwardRef(function({"aria-current":c="page",caseSensitive:u=!1,className:o="",end:f=!1,style:h,to:x,viewTransition:v,children:p,...g},b){let E=gi(x,{relative:g.relative}),j=pa(),G=w.useContext(Xr),{navigator:M,basename:D}=w.useContext(Jt),H=G!=null&&ay(E)&&v===!0,z=M.encodeLocation?M.encodeLocation(E).pathname:E.pathname,P=j.pathname,I=G&&G.navigation&&G.navigation.location?G.navigation.location.pathname:null;u||(P=P.toLowerCase(),I=I?I.toLowerCase():null,z=z.toLowerCase()),I&&D&&(I=ha(I,D)||I);const ce=z!=="/"&&z.endsWith("/")?z.length-1:z.length;let F=P===z||!f&&P.startsWith(z)&&P.charAt(ce)==="/",ne=I!=null&&(I===z||!f&&I.startsWith(z)&&I.charAt(z.length)==="/"),pe={isActive:F,isPending:ne,isTransitioning:H},V=F?c:void 0,te;typeof o=="function"?te=o(pe):te=[o,F?"active":null,ne?"pending":null,H?"transitioning":null].filter(Boolean).join(" ");let xe=typeof h=="function"?h(pe):h;return w.createElement(_u,{...g,"aria-current":V,className:te,ref:b,style:xe,to:x,viewTransition:v},typeof p=="function"?p(pe):p)});Kx.displayName="NavLink";var Jx=w.forwardRef(({discover:i="render",fetcherKey:c,navigate:u,reloadDocument:o,replace:f,state:h,method:x=kr,action:v,onSubmit:p,relative:g,preventScrollReset:b,viewTransition:E,...j},G)=>{let M=ey(),D=ty(v,{relative:g}),H=x.toLowerCase()==="get"?"get":"post",z=typeof v=="string"&&Ah.test(v),P=I=>{if(p&&p(I),I.defaultPrevented)return;I.preventDefault();let ce=I.nativeEvent.submitter,F=ce?.getAttribute("formmethod")||x;M(ce||I.currentTarget,{fetcherKey:c,method:F,navigate:u,replace:f,state:h,relative:g,preventScrollReset:b,viewTransition:E})};return w.createElement("form",{ref:G,method:H,action:D,onSubmit:o?p:P,...j,"data-discover":!z&&i==="render"?"true":void 0})});Jx.displayName="Form";function $x(i){return`${i} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Ch(i){let c=w.useContext(nn);return qe(c,$x(i)),c}function Fx(i,{target:c,replace:u,state:o,preventScrollReset:f,relative:h,viewTransition:x}={}){let v=xt(),p=pa(),g=gi(i,{relative:h});return w.useCallback(b=>{if(Ax(b,c)){b.preventDefault();let E=u!==void 0?u:fi(p)===fi(g);v(i,{replace:E,state:o,preventScrollReset:f,relative:h,viewTransition:x})}},[p,v,g,u,o,c,i,f,h,x])}function Wx(i){Gt(typeof URLSearchParams<"u","You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let c=w.useRef(ju(i)),u=w.useRef(!1),o=pa(),f=w.useMemo(()=>Cx(o.search,u.current?null:c.current),[o.search]),h=xt(),x=w.useCallback((v,p)=>{const g=ju(typeof v=="function"?v(f):v);u.current=!0,h("?"+g,p)},[h,f]);return[f,x]}var Px=0,Ix=()=>`__${String(++Px)}__`;function ey(){let{router:i}=Ch("useSubmit"),{basename:c}=w.useContext(Jt),u=px();return w.useCallback(async(o,f={})=>{let{action:h,method:x,encType:v,formData:p,body:g}=zx(o,c);if(f.navigate===!1){let b=f.fetcherKey||Ix();await i.fetch(b,u,f.action||h,{preventScrollReset:f.preventScrollReset,formData:p,body:g,formMethod:f.method||x,formEncType:f.encType||v,flushSync:f.flushSync})}else await i.navigate(f.action||h,{preventScrollReset:f.preventScrollReset,formData:p,body:g,formMethod:f.method||x,formEncType:f.encType||v,replace:f.replace,state:f.state,fromRouteId:u,flushSync:f.flushSync,viewTransition:f.viewTransition})},[i,c,u])}function ty(i,{relative:c}={}){let{basename:u}=w.useContext(Jt),o=w.useContext($t);qe(o,"useFormAction must be used inside a RouteContext");let[f]=o.matches.slice(-1),h={...gi(i||".",{relative:c})},x=pa();if(i==null){h.search=x.search;let v=new URLSearchParams(h.search),p=v.getAll("index");if(p.some(b=>b==="")){v.delete("index"),p.filter(E=>E).forEach(E=>v.append("index",E));let b=v.toString();h.search=b?`?${b}`:""}}return(!i||i===".")&&f.route.index&&(h.search=h.search?h.search.replace(/^\?/,"?index&"):"?index"),u!=="/"&&(h.pathname=h.pathname==="/"?u:ma([u,h.pathname])),fi(h)}function ay(i,c={}){let u=w.useContext(xh);qe(u!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=Ch("useViewTransitionState"),f=gi(i,{relative:c.relative});if(!u.isTransitioning)return!1;let h=ha(u.currentLocation.pathname,o)||u.currentLocation.pathname,x=ha(u.nextLocation.pathname,o)||u.nextLocation.pathname;return Gr(f.pathname,x)!=null||Gr(f.pathname,h)!=null}[...kx];/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ly=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ny=i=>i.replace(/^([A-Z])|[\s-_]+(\w)/g,(c,u,o)=>o?o.toUpperCase():u.toLowerCase()),Zm=i=>{const c=ny(i);return c.charAt(0).toUpperCase()+c.slice(1)},Th=(...i)=>i.filter((c,u,o)=>!!c&&c.trim()!==""&&o.indexOf(c)===u).join(" ").trim(),iy=i=>{for(const c in i)if(c.startsWith("aria-")||c==="role"||c==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var ry={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sy=w.forwardRef(({color:i="currentColor",size:c=24,strokeWidth:u=2,absoluteStrokeWidth:o,className:f="",children:h,iconNode:x,...v},p)=>w.createElement("svg",{ref:p,...ry,width:c,height:c,stroke:i,strokeWidth:o?Number(u)*24/Number(c):u,className:Th("lucide",f),...!h&&!iy(v)&&{"aria-hidden":"true"},...v},[...x.map(([g,b])=>w.createElement(g,b)),...Array.isArray(h)?h:[h]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _e=(i,c)=>{const u=w.forwardRef(({className:o,...f},h)=>w.createElement(sy,{ref:h,iconNode:c,className:Th(`lucide-${ly(Zm(i))}`,`lucide-${i}`,o),...f}));return u.displayName=Zm(i),u};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cy=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],Qa=_e("arrow-left",cy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uy=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],oy=_e("calendar",uy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dy=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],fy=_e("check",dy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const my=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],gu=_e("circle-check-big",my);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hy=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],Br=_e("credit-card",hy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gy=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],py=_e("dollar-sign",gy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xy=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],yy=_e("eye",xy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vy=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],by=_e("funnel",vy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jy=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]],Ny=_e("grid-3x3",jy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sy=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],Ey=_e("heart",Sy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wy=[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]],Ay=_e("list",wy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cy=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],Rh=_e("log-out",Cy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ty=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],tn=_e("map-pin",Ty);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ry=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],zy=_e("menu",Ry);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oy=[["path",{d:"M5 12h14",key:"1ays0h"}]],zh=_e("minus",Oy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const My=[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]],ml=_e("package",My);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _y=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Zr=_e("plus",_y);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dy=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],Uy=_e("save",Dy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ly=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Yr=_e("search",Ly);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hy=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],ky=_e("settings",Hy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qy=[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]],By=_e("share-2",qy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gy=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],Yy=_e("shield",Gy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vy=[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]],Xy=_e("shopping-bag",Vy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qy=[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]],Vr=_e("shopping-cart",Qy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zy=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],Ky=_e("square-pen",Zy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jy=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Kr=_e("trash-2",Jy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $y=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],Oh=_e("user",$y);function Km(i,c){if(typeof i=="function")return i(c);i!=null&&(i.current=c)}function Fy(...i){return c=>{let u=!1;const o=i.map(f=>{const h=Km(f,c);return!u&&typeof h=="function"&&(u=!0),h});if(u)return()=>{for(let f=0;f<o.length;f++){const h=o[f];typeof h=="function"?h():Km(i[f],null)}}}}function Wy(i){const c=Iy(i),u=w.forwardRef((o,f)=>{const{children:h,...x}=o,v=w.Children.toArray(h),p=v.find(tv);if(p){const g=p.props.children,b=v.map(E=>E===p?w.Children.count(g)>1?w.Children.only(null):w.isValidElement(g)?g.props.children:null:E);return s.jsx(c,{...x,ref:f,children:w.isValidElement(g)?w.cloneElement(g,void 0,b):null})}return s.jsx(c,{...x,ref:f,children:h})});return u.displayName=`${i}.Slot`,u}var Py=Wy("Slot");function Iy(i){const c=w.forwardRef((u,o)=>{const{children:f,...h}=u;if(w.isValidElement(f)){const x=lv(f),v=av(h,f.props);return f.type!==w.Fragment&&(v.ref=o?Fy(o,x):x),w.cloneElement(f,v)}return w.Children.count(f)>1?w.Children.only(null):null});return c.displayName=`${i}.SlotClone`,c}var ev=Symbol("radix.slottable");function tv(i){return w.isValidElement(i)&&typeof i.type=="function"&&"__radixId"in i.type&&i.type.__radixId===ev}function av(i,c){const u={...c};for(const o in c){const f=i[o],h=c[o];/^on[A-Z]/.test(o)?f&&h?u[o]=(...v)=>{const p=h(...v);return f(...v),p}:f&&(u[o]=f):o==="style"?u[o]={...f,...h}:o==="className"&&(u[o]=[f,h].filter(Boolean).join(" "))}return{...i,...u}}function lv(i){let c=Object.getOwnPropertyDescriptor(i.props,"ref")?.get,u=c&&"isReactWarning"in c&&c.isReactWarning;return u?i.ref:(c=Object.getOwnPropertyDescriptor(i,"ref")?.get,u=c&&"isReactWarning"in c&&c.isReactWarning,u?i.props.ref:i.props.ref||i.ref)}function Mh(i){var c,u,o="";if(typeof i=="string"||typeof i=="number")o+=i;else if(typeof i=="object")if(Array.isArray(i)){var f=i.length;for(c=0;c<f;c++)i[c]&&(u=Mh(i[c]))&&(o&&(o+=" "),o+=u)}else for(u in i)i[u]&&(o&&(o+=" "),o+=u);return o}function _h(){for(var i,c,u=0,o="",f=arguments.length;u<f;u++)(i=arguments[u])&&(c=Mh(i))&&(o&&(o+=" "),o+=c);return o}const Jm=i=>typeof i=="boolean"?`${i}`:i===0?"0":i,$m=_h,Dh=(i,c)=>u=>{var o;if(c?.variants==null)return $m(i,u?.class,u?.className);const{variants:f,defaultVariants:h}=c,x=Object.keys(f).map(g=>{const b=u?.[g],E=h?.[g];if(b===null)return null;const j=Jm(b)||Jm(E);return f[g][j]}),v=u&&Object.entries(u).reduce((g,b)=>{let[E,j]=b;return j===void 0||(g[E]=j),g},{}),p=c==null||(o=c.compoundVariants)===null||o===void 0?void 0:o.reduce((g,b)=>{let{class:E,className:j,...G}=b;return Object.entries(G).every(M=>{let[D,H]=M;return Array.isArray(H)?H.includes({...h,...v}[D]):{...h,...v}[D]===H})?[...g,E,j]:g},[]);return $m(i,x,p,u?.class,u?.className)},Du="-",nv=i=>{const c=rv(i),{conflictingClassGroups:u,conflictingClassGroupModifiers:o}=i;return{getClassGroupId:x=>{const v=x.split(Du);return v[0]===""&&v.length!==1&&v.shift(),Uh(v,c)||iv(x)},getConflictingClassGroupIds:(x,v)=>{const p=u[x]||[];return v&&o[x]?[...p,...o[x]]:p}}},Uh=(i,c)=>{if(i.length===0)return c.classGroupId;const u=i[0],o=c.nextPart.get(u),f=o?Uh(i.slice(1),o):void 0;if(f)return f;if(c.validators.length===0)return;const h=i.join(Du);return c.validators.find(({validator:x})=>x(h))?.classGroupId},Fm=/^\[(.+)\]$/,iv=i=>{if(Fm.test(i)){const c=Fm.exec(i)[1],u=c?.substring(0,c.indexOf(":"));if(u)return"arbitrary.."+u}},rv=i=>{const{theme:c,classGroups:u}=i,o={nextPart:new Map,validators:[]};for(const f in u)Nu(u[f],o,f,c);return o},Nu=(i,c,u,o)=>{i.forEach(f=>{if(typeof f=="string"){const h=f===""?c:Wm(c,f);h.classGroupId=u;return}if(typeof f=="function"){if(sv(f)){Nu(f(o),c,u,o);return}c.validators.push({validator:f,classGroupId:u});return}Object.entries(f).forEach(([h,x])=>{Nu(x,Wm(c,h),u,o)})})},Wm=(i,c)=>{let u=i;return c.split(Du).forEach(o=>{u.nextPart.has(o)||u.nextPart.set(o,{nextPart:new Map,validators:[]}),u=u.nextPart.get(o)}),u},sv=i=>i.isThemeGetter,cv=i=>{if(i<1)return{get:()=>{},set:()=>{}};let c=0,u=new Map,o=new Map;const f=(h,x)=>{u.set(h,x),c++,c>i&&(c=0,o=u,u=new Map)};return{get(h){let x=u.get(h);if(x!==void 0)return x;if((x=o.get(h))!==void 0)return f(h,x),x},set(h,x){u.has(h)?u.set(h,x):f(h,x)}}},Su="!",Eu=":",uv=Eu.length,ov=i=>{const{prefix:c,experimentalParseClassName:u}=i;let o=f=>{const h=[];let x=0,v=0,p=0,g;for(let M=0;M<f.length;M++){let D=f[M];if(x===0&&v===0){if(D===Eu){h.push(f.slice(p,M)),p=M+uv;continue}if(D==="/"){g=M;continue}}D==="["?x++:D==="]"?x--:D==="("?v++:D===")"&&v--}const b=h.length===0?f:f.substring(p),E=dv(b),j=E!==b,G=g&&g>p?g-p:void 0;return{modifiers:h,hasImportantModifier:j,baseClassName:E,maybePostfixModifierPosition:G}};if(c){const f=c+Eu,h=o;o=x=>x.startsWith(f)?h(x.substring(f.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:x,maybePostfixModifierPosition:void 0}}if(u){const f=o;o=h=>u({className:h,parseClassName:f})}return o},dv=i=>i.endsWith(Su)?i.substring(0,i.length-1):i.startsWith(Su)?i.substring(1):i,fv=i=>{const c=Object.fromEntries(i.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const f=[];let h=[];return o.forEach(x=>{x[0]==="["||c[x]?(f.push(...h.sort(),x),h=[]):h.push(x)}),f.push(...h.sort()),f}},mv=i=>({cache:cv(i.cacheSize),parseClassName:ov(i),sortModifiers:fv(i),...nv(i)}),hv=/\s+/,gv=(i,c)=>{const{parseClassName:u,getClassGroupId:o,getConflictingClassGroupIds:f,sortModifiers:h}=c,x=[],v=i.trim().split(hv);let p="";for(let g=v.length-1;g>=0;g-=1){const b=v[g],{isExternal:E,modifiers:j,hasImportantModifier:G,baseClassName:M,maybePostfixModifierPosition:D}=u(b);if(E){p=b+(p.length>0?" "+p:p);continue}let H=!!D,z=o(H?M.substring(0,D):M);if(!z){if(!H){p=b+(p.length>0?" "+p:p);continue}if(z=o(M),!z){p=b+(p.length>0?" "+p:p);continue}H=!1}const P=h(j).join(":"),I=G?P+Su:P,ce=I+z;if(x.includes(ce))continue;x.push(ce);const F=f(z,H);for(let ne=0;ne<F.length;++ne){const pe=F[ne];x.push(I+pe)}p=b+(p.length>0?" "+p:p)}return p};function pv(){let i=0,c,u,o="";for(;i<arguments.length;)(c=arguments[i++])&&(u=Lh(c))&&(o&&(o+=" "),o+=u);return o}const Lh=i=>{if(typeof i=="string")return i;let c,u="";for(let o=0;o<i.length;o++)i[o]&&(c=Lh(i[o]))&&(u&&(u+=" "),u+=c);return u};function xv(i,...c){let u,o,f,h=x;function x(p){const g=c.reduce((b,E)=>E(b),i());return u=mv(g),o=u.cache.get,f=u.cache.set,h=v,v(p)}function v(p){const g=o(p);if(g)return g;const b=gv(p,u);return f(p,b),b}return function(){return h(pv.apply(null,arguments))}}const $e=i=>{const c=u=>u[i]||[];return c.isThemeGetter=!0,c},Hh=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,kh=/^\((?:(\w[\w-]*):)?(.+)\)$/i,yv=/^\d+\/\d+$/,vv=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,bv=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,jv=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Nv=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Sv=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Il=i=>yv.test(i),oe=i=>!!i&&!Number.isNaN(Number(i)),Xa=i=>!!i&&Number.isInteger(Number(i)),pu=i=>i.endsWith("%")&&oe(i.slice(0,-1)),fa=i=>vv.test(i),Ev=()=>!0,wv=i=>bv.test(i)&&!jv.test(i),qh=()=>!1,Av=i=>Nv.test(i),Cv=i=>Sv.test(i),Tv=i=>!J(i)&&!$(i),Rv=i=>rn(i,Yh,qh),J=i=>Hh.test(i),fl=i=>rn(i,Vh,wv),xu=i=>rn(i,Dv,oe),Pm=i=>rn(i,Bh,qh),zv=i=>rn(i,Gh,Cv),Mr=i=>rn(i,Xh,Av),$=i=>kh.test(i),oi=i=>sn(i,Vh),Ov=i=>sn(i,Uv),Im=i=>sn(i,Bh),Mv=i=>sn(i,Yh),_v=i=>sn(i,Gh),_r=i=>sn(i,Xh,!0),rn=(i,c,u)=>{const o=Hh.exec(i);return o?o[1]?c(o[1]):u(o[2]):!1},sn=(i,c,u=!1)=>{const o=kh.exec(i);return o?o[1]?c(o[1]):u:!1},Bh=i=>i==="position"||i==="percentage",Gh=i=>i==="image"||i==="url",Yh=i=>i==="length"||i==="size"||i==="bg-size",Vh=i=>i==="length",Dv=i=>i==="number",Uv=i=>i==="family-name",Xh=i=>i==="shadow",Lv=()=>{const i=$e("color"),c=$e("font"),u=$e("text"),o=$e("font-weight"),f=$e("tracking"),h=$e("leading"),x=$e("breakpoint"),v=$e("container"),p=$e("spacing"),g=$e("radius"),b=$e("shadow"),E=$e("inset-shadow"),j=$e("text-shadow"),G=$e("drop-shadow"),M=$e("blur"),D=$e("perspective"),H=$e("aspect"),z=$e("ease"),P=$e("animate"),I=()=>["auto","avoid","all","avoid-page","page","left","right","column"],ce=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],F=()=>[...ce(),$,J],ne=()=>["auto","hidden","clip","visible","scroll"],pe=()=>["auto","contain","none"],V=()=>[$,J,p],te=()=>[Il,"full","auto",...V()],xe=()=>[Xa,"none","subgrid",$,J],Ue=()=>["auto",{span:["full",Xa,$,J]},Xa,$,J],Se=()=>[Xa,"auto",$,J],Be=()=>["auto","min","max","fr",$,J],lt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Ce=()=>["start","end","center","stretch","center-safe","end-safe"],_=()=>["auto",...V()],X=()=>[Il,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...V()],B=()=>[i,$,J],Ee=()=>[...ce(),Im,Pm,{position:[$,J]}],N=()=>["no-repeat",{repeat:["","x","y","space","round"]}],q=()=>["auto","cover","contain",Mv,Rv,{size:[$,J]}],Q=()=>[pu,oi,fl],Y=()=>["","none","full",g,$,J],Z=()=>["",oe,oi,fl],fe=()=>["solid","dashed","dotted","double"],re=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],be=()=>[oe,pu,Im,Pm],Oe=()=>["","none",M,$,J],yt=()=>["none",oe,$,J],xa=()=>["none",oe,$,J],ya=()=>[oe,$,J],va=()=>[Il,"full",...V()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[fa],breakpoint:[fa],color:[Ev],container:[fa],"drop-shadow":[fa],ease:["in","out","in-out"],font:[Tv],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[fa],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[fa],shadow:[fa],spacing:["px",oe],text:[fa],"text-shadow":[fa],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Il,J,$,H]}],container:["container"],columns:[{columns:[oe,J,$,v]}],"break-after":[{"break-after":I()}],"break-before":[{"break-before":I()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:F()}],overflow:[{overflow:ne()}],"overflow-x":[{"overflow-x":ne()}],"overflow-y":[{"overflow-y":ne()}],overscroll:[{overscroll:pe()}],"overscroll-x":[{"overscroll-x":pe()}],"overscroll-y":[{"overscroll-y":pe()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:te()}],"inset-x":[{"inset-x":te()}],"inset-y":[{"inset-y":te()}],start:[{start:te()}],end:[{end:te()}],top:[{top:te()}],right:[{right:te()}],bottom:[{bottom:te()}],left:[{left:te()}],visibility:["visible","invisible","collapse"],z:[{z:[Xa,"auto",$,J]}],basis:[{basis:[Il,"full","auto",v,...V()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[oe,Il,"auto","initial","none",J]}],grow:[{grow:["",oe,$,J]}],shrink:[{shrink:["",oe,$,J]}],order:[{order:[Xa,"first","last","none",$,J]}],"grid-cols":[{"grid-cols":xe()}],"col-start-end":[{col:Ue()}],"col-start":[{"col-start":Se()}],"col-end":[{"col-end":Se()}],"grid-rows":[{"grid-rows":xe()}],"row-start-end":[{row:Ue()}],"row-start":[{"row-start":Se()}],"row-end":[{"row-end":Se()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Be()}],"auto-rows":[{"auto-rows":Be()}],gap:[{gap:V()}],"gap-x":[{"gap-x":V()}],"gap-y":[{"gap-y":V()}],"justify-content":[{justify:[...lt(),"normal"]}],"justify-items":[{"justify-items":[...Ce(),"normal"]}],"justify-self":[{"justify-self":["auto",...Ce()]}],"align-content":[{content:["normal",...lt()]}],"align-items":[{items:[...Ce(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Ce(),{baseline:["","last"]}]}],"place-content":[{"place-content":lt()}],"place-items":[{"place-items":[...Ce(),"baseline"]}],"place-self":[{"place-self":["auto",...Ce()]}],p:[{p:V()}],px:[{px:V()}],py:[{py:V()}],ps:[{ps:V()}],pe:[{pe:V()}],pt:[{pt:V()}],pr:[{pr:V()}],pb:[{pb:V()}],pl:[{pl:V()}],m:[{m:_()}],mx:[{mx:_()}],my:[{my:_()}],ms:[{ms:_()}],me:[{me:_()}],mt:[{mt:_()}],mr:[{mr:_()}],mb:[{mb:_()}],ml:[{ml:_()}],"space-x":[{"space-x":V()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":V()}],"space-y-reverse":["space-y-reverse"],size:[{size:X()}],w:[{w:[v,"screen",...X()]}],"min-w":[{"min-w":[v,"screen","none",...X()]}],"max-w":[{"max-w":[v,"screen","none","prose",{screen:[x]},...X()]}],h:[{h:["screen","lh",...X()]}],"min-h":[{"min-h":["screen","lh","none",...X()]}],"max-h":[{"max-h":["screen","lh",...X()]}],"font-size":[{text:["base",u,oi,fl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,$,xu]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",pu,J]}],"font-family":[{font:[Ov,J,c]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[f,$,J]}],"line-clamp":[{"line-clamp":[oe,"none",$,xu]}],leading:[{leading:[h,...V()]}],"list-image":[{"list-image":["none",$,J]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",$,J]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:B()}],"text-color":[{text:B()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...fe(),"wavy"]}],"text-decoration-thickness":[{decoration:[oe,"from-font","auto",$,fl]}],"text-decoration-color":[{decoration:B()}],"underline-offset":[{"underline-offset":[oe,"auto",$,J]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:V()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",$,J]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",$,J]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Ee()}],"bg-repeat":[{bg:N()}],"bg-size":[{bg:q()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Xa,$,J],radial:["",$,J],conic:[Xa,$,J]},_v,zv]}],"bg-color":[{bg:B()}],"gradient-from-pos":[{from:Q()}],"gradient-via-pos":[{via:Q()}],"gradient-to-pos":[{to:Q()}],"gradient-from":[{from:B()}],"gradient-via":[{via:B()}],"gradient-to":[{to:B()}],rounded:[{rounded:Y()}],"rounded-s":[{"rounded-s":Y()}],"rounded-e":[{"rounded-e":Y()}],"rounded-t":[{"rounded-t":Y()}],"rounded-r":[{"rounded-r":Y()}],"rounded-b":[{"rounded-b":Y()}],"rounded-l":[{"rounded-l":Y()}],"rounded-ss":[{"rounded-ss":Y()}],"rounded-se":[{"rounded-se":Y()}],"rounded-ee":[{"rounded-ee":Y()}],"rounded-es":[{"rounded-es":Y()}],"rounded-tl":[{"rounded-tl":Y()}],"rounded-tr":[{"rounded-tr":Y()}],"rounded-br":[{"rounded-br":Y()}],"rounded-bl":[{"rounded-bl":Y()}],"border-w":[{border:Z()}],"border-w-x":[{"border-x":Z()}],"border-w-y":[{"border-y":Z()}],"border-w-s":[{"border-s":Z()}],"border-w-e":[{"border-e":Z()}],"border-w-t":[{"border-t":Z()}],"border-w-r":[{"border-r":Z()}],"border-w-b":[{"border-b":Z()}],"border-w-l":[{"border-l":Z()}],"divide-x":[{"divide-x":Z()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":Z()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...fe(),"hidden","none"]}],"divide-style":[{divide:[...fe(),"hidden","none"]}],"border-color":[{border:B()}],"border-color-x":[{"border-x":B()}],"border-color-y":[{"border-y":B()}],"border-color-s":[{"border-s":B()}],"border-color-e":[{"border-e":B()}],"border-color-t":[{"border-t":B()}],"border-color-r":[{"border-r":B()}],"border-color-b":[{"border-b":B()}],"border-color-l":[{"border-l":B()}],"divide-color":[{divide:B()}],"outline-style":[{outline:[...fe(),"none","hidden"]}],"outline-offset":[{"outline-offset":[oe,$,J]}],"outline-w":[{outline:["",oe,oi,fl]}],"outline-color":[{outline:B()}],shadow:[{shadow:["","none",b,_r,Mr]}],"shadow-color":[{shadow:B()}],"inset-shadow":[{"inset-shadow":["none",E,_r,Mr]}],"inset-shadow-color":[{"inset-shadow":B()}],"ring-w":[{ring:Z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:B()}],"ring-offset-w":[{"ring-offset":[oe,fl]}],"ring-offset-color":[{"ring-offset":B()}],"inset-ring-w":[{"inset-ring":Z()}],"inset-ring-color":[{"inset-ring":B()}],"text-shadow":[{"text-shadow":["none",j,_r,Mr]}],"text-shadow-color":[{"text-shadow":B()}],opacity:[{opacity:[oe,$,J]}],"mix-blend":[{"mix-blend":[...re(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":re()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[oe]}],"mask-image-linear-from-pos":[{"mask-linear-from":be()}],"mask-image-linear-to-pos":[{"mask-linear-to":be()}],"mask-image-linear-from-color":[{"mask-linear-from":B()}],"mask-image-linear-to-color":[{"mask-linear-to":B()}],"mask-image-t-from-pos":[{"mask-t-from":be()}],"mask-image-t-to-pos":[{"mask-t-to":be()}],"mask-image-t-from-color":[{"mask-t-from":B()}],"mask-image-t-to-color":[{"mask-t-to":B()}],"mask-image-r-from-pos":[{"mask-r-from":be()}],"mask-image-r-to-pos":[{"mask-r-to":be()}],"mask-image-r-from-color":[{"mask-r-from":B()}],"mask-image-r-to-color":[{"mask-r-to":B()}],"mask-image-b-from-pos":[{"mask-b-from":be()}],"mask-image-b-to-pos":[{"mask-b-to":be()}],"mask-image-b-from-color":[{"mask-b-from":B()}],"mask-image-b-to-color":[{"mask-b-to":B()}],"mask-image-l-from-pos":[{"mask-l-from":be()}],"mask-image-l-to-pos":[{"mask-l-to":be()}],"mask-image-l-from-color":[{"mask-l-from":B()}],"mask-image-l-to-color":[{"mask-l-to":B()}],"mask-image-x-from-pos":[{"mask-x-from":be()}],"mask-image-x-to-pos":[{"mask-x-to":be()}],"mask-image-x-from-color":[{"mask-x-from":B()}],"mask-image-x-to-color":[{"mask-x-to":B()}],"mask-image-y-from-pos":[{"mask-y-from":be()}],"mask-image-y-to-pos":[{"mask-y-to":be()}],"mask-image-y-from-color":[{"mask-y-from":B()}],"mask-image-y-to-color":[{"mask-y-to":B()}],"mask-image-radial":[{"mask-radial":[$,J]}],"mask-image-radial-from-pos":[{"mask-radial-from":be()}],"mask-image-radial-to-pos":[{"mask-radial-to":be()}],"mask-image-radial-from-color":[{"mask-radial-from":B()}],"mask-image-radial-to-color":[{"mask-radial-to":B()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":ce()}],"mask-image-conic-pos":[{"mask-conic":[oe]}],"mask-image-conic-from-pos":[{"mask-conic-from":be()}],"mask-image-conic-to-pos":[{"mask-conic-to":be()}],"mask-image-conic-from-color":[{"mask-conic-from":B()}],"mask-image-conic-to-color":[{"mask-conic-to":B()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Ee()}],"mask-repeat":[{mask:N()}],"mask-size":[{mask:q()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",$,J]}],filter:[{filter:["","none",$,J]}],blur:[{blur:Oe()}],brightness:[{brightness:[oe,$,J]}],contrast:[{contrast:[oe,$,J]}],"drop-shadow":[{"drop-shadow":["","none",G,_r,Mr]}],"drop-shadow-color":[{"drop-shadow":B()}],grayscale:[{grayscale:["",oe,$,J]}],"hue-rotate":[{"hue-rotate":[oe,$,J]}],invert:[{invert:["",oe,$,J]}],saturate:[{saturate:[oe,$,J]}],sepia:[{sepia:["",oe,$,J]}],"backdrop-filter":[{"backdrop-filter":["","none",$,J]}],"backdrop-blur":[{"backdrop-blur":Oe()}],"backdrop-brightness":[{"backdrop-brightness":[oe,$,J]}],"backdrop-contrast":[{"backdrop-contrast":[oe,$,J]}],"backdrop-grayscale":[{"backdrop-grayscale":["",oe,$,J]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[oe,$,J]}],"backdrop-invert":[{"backdrop-invert":["",oe,$,J]}],"backdrop-opacity":[{"backdrop-opacity":[oe,$,J]}],"backdrop-saturate":[{"backdrop-saturate":[oe,$,J]}],"backdrop-sepia":[{"backdrop-sepia":["",oe,$,J]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":V()}],"border-spacing-x":[{"border-spacing-x":V()}],"border-spacing-y":[{"border-spacing-y":V()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",$,J]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[oe,"initial",$,J]}],ease:[{ease:["linear","initial",z,$,J]}],delay:[{delay:[oe,$,J]}],animate:[{animate:["none",P,$,J]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[D,$,J]}],"perspective-origin":[{"perspective-origin":F()}],rotate:[{rotate:yt()}],"rotate-x":[{"rotate-x":yt()}],"rotate-y":[{"rotate-y":yt()}],"rotate-z":[{"rotate-z":yt()}],scale:[{scale:xa()}],"scale-x":[{"scale-x":xa()}],"scale-y":[{"scale-y":xa()}],"scale-z":[{"scale-z":xa()}],"scale-3d":["scale-3d"],skew:[{skew:ya()}],"skew-x":[{"skew-x":ya()}],"skew-y":[{"skew-y":ya()}],transform:[{transform:[$,J,"","none","gpu","cpu"]}],"transform-origin":[{origin:F()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:va()}],"translate-x":[{"translate-x":va()}],"translate-y":[{"translate-y":va()}],"translate-z":[{"translate-z":va()}],"translate-none":["translate-none"],accent:[{accent:B()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:B()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",$,J]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":V()}],"scroll-mx":[{"scroll-mx":V()}],"scroll-my":[{"scroll-my":V()}],"scroll-ms":[{"scroll-ms":V()}],"scroll-me":[{"scroll-me":V()}],"scroll-mt":[{"scroll-mt":V()}],"scroll-mr":[{"scroll-mr":V()}],"scroll-mb":[{"scroll-mb":V()}],"scroll-ml":[{"scroll-ml":V()}],"scroll-p":[{"scroll-p":V()}],"scroll-px":[{"scroll-px":V()}],"scroll-py":[{"scroll-py":V()}],"scroll-ps":[{"scroll-ps":V()}],"scroll-pe":[{"scroll-pe":V()}],"scroll-pt":[{"scroll-pt":V()}],"scroll-pr":[{"scroll-pr":V()}],"scroll-pb":[{"scroll-pb":V()}],"scroll-pl":[{"scroll-pl":V()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",$,J]}],fill:[{fill:["none",...B()]}],"stroke-w":[{stroke:[oe,oi,fl,xu]}],stroke:[{stroke:["none",...B()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Hv=xv(Lv);function Ft(...i){return Hv(_h(i))}function ke(i){return new Intl.NumberFormat("es-PE",{style:"currency",currency:"PEN"}).format(i)}function Qh(i){return new Intl.DateTimeFormat("es-PE",{year:"numeric",month:"long",day:"numeric"}).format(new Date(i))}const kv=Dh("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transform hover:scale-105 active:scale-95",{variants:{variant:{default:"bg-electric-orange-500 text-white hover:bg-electric-orange-600 hover:shadow-lg",secondary:"bg-cobalt-blue-500 text-white hover:bg-cobalt-blue-600 hover:shadow-lg",accent:"bg-soft-magenta-500 text-white hover:bg-soft-magenta-600 hover:shadow-lg",success:"bg-lime-green-500 text-white hover:bg-lime-green-600 hover:shadow-lg",destructive:"bg-red-500 text-white hover:bg-red-600 hover:shadow-lg",outline:"border border-electric-orange-500 text-electric-orange-500 hover:bg-electric-orange-500 hover:text-white",ghost:"hover:bg-electric-orange-100 hover:text-electric-orange-600",link:"text-electric-orange-500 underline-offset-4 hover:underline"},size:{default:"h-12 px-6 py-3",sm:"h-9 rounded-lg px-3",lg:"h-14 rounded-xl px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),W=ga.forwardRef(({className:i,variant:c,size:u,asChild:o=!1,...f},h)=>{const x=o?Py:"button";return s.jsx(x,{className:Ft(kv({variant:c,size:u,className:i})),ref:h,...f})});W.displayName="Button";const qv=Dh("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-electric-orange-500 text-white hover:bg-electric-orange-600",secondary:"border-transparent bg-cobalt-blue-500 text-white hover:bg-cobalt-blue-600",accent:"border-transparent bg-soft-magenta-500 text-white hover:bg-soft-magenta-600",success:"border-transparent bg-lime-green-500 text-white hover:bg-lime-green-600",destructive:"border-transparent bg-red-500 text-white hover:bg-red-600",outline:"text-foreground border-gray-200"}},defaultVariants:{variant:"default"}});function Za({className:i,variant:c,...u}){return s.jsx("div",{className:Ft(qv({variant:c}),i),...u})}const Zh=w.createContext(),Bv=(i,c)=>{switch(c.type){case"ADD_ITEM":return i.items.find(o=>o.id===c.payload.id)?{...i,items:i.items.map(o=>o.id===c.payload.id?{...o,quantity:o.quantity+1}:o)}:{...i,items:[...i.items,{...c.payload,quantity:1}]};case"REMOVE_ITEM":return{...i,items:i.items.filter(o=>o.id!==c.payload)};case"UPDATE_QUANTITY":return{...i,items:i.items.map(o=>o.id===c.payload.id?{...o,quantity:c.payload.quantity}:o).filter(o=>o.quantity>0)};case"CLEAR_CART":return{...i,items:[]};case"LOAD_CART":return{...i,items:c.payload||[]};default:return i}},Gv=({children:i})=>{const[c,u]=w.useReducer(Bv,{items:[]});w.useEffect(()=>{const b=localStorage.getItem("fuxion-cart");b&&u({type:"LOAD_CART",payload:JSON.parse(b)})},[]),w.useEffect(()=>{localStorage.setItem("fuxion-cart",JSON.stringify(c.items))},[c.items]);const o=b=>{u({type:"ADD_ITEM",payload:b})},f=b=>{u({type:"REMOVE_ITEM",payload:b})},h=(b,E)=>{u({type:"UPDATE_QUANTITY",payload:{id:b,quantity:E}})},x=()=>{u({type:"CLEAR_CART"})},v=()=>c.items.reduce((b,E)=>b+E.quantity,0),p=()=>c.items.reduce((b,E)=>b+E.price*E.quantity,0),g={items:c.items,addItem:o,removeItem:f,updateQuantity:h,clearCart:x,getTotalItems:v,getTotalPrice:p};return s.jsx(Zh.Provider,{value:g,children:i})},cn=()=>{const i=w.useContext(Zh);if(!i)throw new Error("useCart must be used within a CartProvider");return i},Yv="modulepreload",Vv=function(i){return"/"+i},eh={},Uu=function(c,u,o){let f=Promise.resolve();if(u&&u.length>0){let p=function(g){return Promise.all(g.map(b=>Promise.resolve(b).then(E=>({status:"fulfilled",value:E}),E=>({status:"rejected",reason:E}))))};document.getElementsByTagName("link");const x=document.querySelector("meta[property=csp-nonce]"),v=x?.nonce||x?.getAttribute("nonce");f=p(u.map(g=>{if(g=Vv(g),g in eh)return;eh[g]=!0;const b=g.endsWith(".css"),E=b?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${g}"]${E}`))return;const j=document.createElement("link");if(j.rel=b?"stylesheet":Yv,b||(j.as="script"),j.crossOrigin="",j.href=g,v&&j.setAttribute("nonce",v),document.head.appendChild(j),b)return new Promise((G,M)=>{j.addEventListener("load",G),j.addEventListener("error",()=>M(new Error(`Unable to preload CSS for ${g}`)))})}))}function h(x){const v=new Event("vite:preloadError",{cancelable:!0});if(v.payload=x,window.dispatchEvent(v),!v.defaultPrevented)throw x}return f.then(x=>{for(const v of x||[])v.status==="rejected"&&h(v.reason);return c().catch(h)})};/*! Capacitor: https://capacitorjs.com/ - MIT License */var an;(function(i){i.Unimplemented="UNIMPLEMENTED",i.Unavailable="UNAVAILABLE"})(an||(an={}));class yu extends Error{constructor(c,u,o){super(c),this.message=c,this.code=u,this.data=o}}const Xv=i=>{var c,u;return i?.androidBridge?"android":!((u=(c=i?.webkit)===null||c===void 0?void 0:c.messageHandlers)===null||u===void 0)&&u.bridge?"ios":"web"},Qv=i=>{const c=i.CapacitorCustomPlatform||null,u=i.Capacitor||{},o=u.Plugins=u.Plugins||{},f=()=>c!==null?c.name:Xv(i),h=()=>f()!=="web",x=E=>{const j=g.get(E);return!!(j?.platforms.has(f())||v(E))},v=E=>{var j;return(j=u.PluginHeaders)===null||j===void 0?void 0:j.find(G=>G.name===E)},p=E=>i.console.error(E),g=new Map,b=(E,j={})=>{const G=g.get(E);if(G)return console.warn(`Capacitor plugin "${E}" already registered. Cannot register plugins twice.`),G.proxy;const M=f(),D=v(E);let H;const z=async()=>(!H&&M in j?H=typeof j[M]=="function"?H=await j[M]():H=j[M]:c!==null&&!H&&"web"in j&&(H=typeof j.web=="function"?H=await j.web():H=j.web),H),P=(V,te)=>{var xe,Ue;if(D){const Se=D?.methods.find(Be=>te===Be.name);if(Se)return Se.rtype==="promise"?Be=>u.nativePromise(E,te.toString(),Be):(Be,lt)=>u.nativeCallback(E,te.toString(),Be,lt);if(V)return(xe=V[te])===null||xe===void 0?void 0:xe.bind(V)}else{if(V)return(Ue=V[te])===null||Ue===void 0?void 0:Ue.bind(V);throw new yu(`"${E}" plugin is not implemented on ${M}`,an.Unimplemented)}},I=V=>{let te;const xe=(...Ue)=>{const Se=z().then(Be=>{const lt=P(Be,V);if(lt){const Ce=lt(...Ue);return te=Ce?.remove,Ce}else throw new yu(`"${E}.${V}()" is not implemented on ${M}`,an.Unimplemented)});return V==="addListener"&&(Se.remove=async()=>te()),Se};return xe.toString=()=>`${V.toString()}() { [capacitor code] }`,Object.defineProperty(xe,"name",{value:V,writable:!1,configurable:!1}),xe},ce=I("addListener"),F=I("removeListener"),ne=(V,te)=>{const xe=ce({eventName:V},te),Ue=async()=>{const Be=await xe;F({eventName:V,callbackId:Be},te)},Se=new Promise(Be=>xe.then(()=>Be({remove:Ue})));return Se.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await Ue()},Se},pe=new Proxy({},{get(V,te){switch(te){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return D?ne:ce;case"removeListener":return F;default:return I(te)}}});return o[E]=pe,g.set(E,{name:E,proxy:pe,platforms:new Set([...Object.keys(j),...D?[M]:[]])}),pe};return u.convertFileSrc||(u.convertFileSrc=E=>E),u.getPlatform=f,u.handleError=p,u.isNativePlatform=h,u.isPluginAvailable=x,u.registerPlugin=b,u.Exception=yu,u.DEBUG=!!u.DEBUG,u.isLoggingEnabled=!!u.isLoggingEnabled,u},Zv=i=>i.Capacitor=Qv(i),wu=Zv(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),pi=wu.registerPlugin;class Kh{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(c,u){let o=!1;this.listeners[c]||(this.listeners[c]=[],o=!0),this.listeners[c].push(u);const h=this.windowListeners[c];h&&!h.registered&&this.addWindowListener(h),o&&this.sendRetainedArgumentsForEvent(c);const x=async()=>this.removeListener(c,u);return Promise.resolve({remove:x})}async removeAllListeners(){this.listeners={};for(const c in this.windowListeners)this.removeWindowListener(this.windowListeners[c]);this.windowListeners={}}notifyListeners(c,u,o){const f=this.listeners[c];if(!f){if(o){let h=this.retainedEventArguments[c];h||(h=[]),h.push(u),this.retainedEventArguments[c]=h}return}f.forEach(h=>h(u))}hasListeners(c){var u;return!!(!((u=this.listeners[c])===null||u===void 0)&&u.length)}registerWindowListener(c,u){this.windowListeners[u]={registered:!1,windowEventName:c,pluginEventName:u,handler:o=>{this.notifyListeners(u,o)}}}unimplemented(c="not implemented"){return new wu.Exception(c,an.Unimplemented)}unavailable(c="not available"){return new wu.Exception(c,an.Unavailable)}async removeListener(c,u){const o=this.listeners[c];if(!o)return;const f=o.indexOf(u);this.listeners[c].splice(f,1),this.listeners[c].length||this.removeWindowListener(this.windowListeners[c])}addWindowListener(c){window.addEventListener(c.windowEventName,c.handler),c.registered=!0}removeWindowListener(c){c&&(window.removeEventListener(c.windowEventName,c.handler),c.registered=!1)}sendRetainedArgumentsForEvent(c){const u=this.retainedEventArguments[c];u&&(delete this.retainedEventArguments[c],u.forEach(o=>{this.notifyListeners(c,o)}))}}const th=i=>encodeURIComponent(i).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),ah=i=>i.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class Kv extends Kh{async getCookies(){const c=document.cookie,u={};return c.split(";").forEach(o=>{if(o.length<=0)return;let[f,h]=o.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");f=ah(f).trim(),h=ah(h).trim(),u[f]=h}),u}async setCookie(c){try{const u=th(c.key),o=th(c.value),f=`; expires=${(c.expires||"").replace("expires=","")}`,h=(c.path||"/").replace("path=",""),x=c.url!=null&&c.url.length>0?`domain=${c.url}`:"";document.cookie=`${u}=${o||""}${f}; path=${h}; ${x};`}catch(u){return Promise.reject(u)}}async deleteCookie(c){try{document.cookie=`${c.key}=; Max-Age=0`}catch(u){return Promise.reject(u)}}async clearCookies(){try{const c=document.cookie.split(";")||[];for(const u of c)document.cookie=u.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(c){return Promise.reject(c)}}async clearAllCookies(){try{await this.clearCookies()}catch(c){return Promise.reject(c)}}}pi("CapacitorCookies",{web:()=>new Kv});const Jv=async i=>new Promise((c,u)=>{const o=new FileReader;o.onload=()=>{const f=o.result;c(f.indexOf(",")>=0?f.split(",")[1]:f)},o.onerror=f=>u(f),o.readAsDataURL(i)}),$v=(i={})=>{const c=Object.keys(i);return Object.keys(i).map(f=>f.toLocaleLowerCase()).reduce((f,h,x)=>(f[h]=i[c[x]],f),{})},Fv=(i,c=!0)=>i?Object.entries(i).reduce((o,f)=>{const[h,x]=f;let v,p;return Array.isArray(x)?(p="",x.forEach(g=>{v=c?encodeURIComponent(g):g,p+=`${h}=${v}&`}),p.slice(0,-1)):(v=c?encodeURIComponent(x):x,p=`${h}=${v}`),`${o}&${p}`},"").substr(1):null,Wv=(i,c={})=>{const u=Object.assign({method:i.method||"GET",headers:i.headers},c),f=$v(i.headers)["content-type"]||"";if(typeof i.data=="string")u.body=i.data;else if(f.includes("application/x-www-form-urlencoded")){const h=new URLSearchParams;for(const[x,v]of Object.entries(i.data||{}))h.set(x,v);u.body=h.toString()}else if(f.includes("multipart/form-data")||i.data instanceof FormData){const h=new FormData;if(i.data instanceof FormData)i.data.forEach((v,p)=>{h.append(p,v)});else for(const v of Object.keys(i.data))h.append(v,i.data[v]);u.body=h;const x=new Headers(u.headers);x.delete("content-type"),u.headers=x}else(f.includes("application/json")||typeof i.data=="object")&&(u.body=JSON.stringify(i.data));return u};class Pv extends Kh{async request(c){const u=Wv(c,c.webFetchExtra),o=Fv(c.params,c.shouldEncodeUrlParams),f=o?`${c.url}?${o}`:c.url,h=await fetch(f,u),x=h.headers.get("content-type")||"";let{responseType:v="text"}=h.ok?c:{};x.includes("application/json")&&(v="json");let p,g;switch(v){case"arraybuffer":case"blob":g=await h.blob(),p=await Jv(g);break;case"json":p=await h.json();break;case"document":case"text":default:p=await h.text()}const b={};return h.headers.forEach((E,j)=>{b[j]=E}),{data:p,headers:b,status:h.status,url:h.url}}async get(c){return this.request(Object.assign(Object.assign({},c),{method:"GET"}))}async post(c){return this.request(Object.assign(Object.assign({},c),{method:"POST"}))}async put(c){return this.request(Object.assign(Object.assign({},c),{method:"PUT"}))}async patch(c){return this.request(Object.assign(Object.assign({},c),{method:"PATCH"}))}async delete(c){return this.request(Object.assign(Object.assign({},c),{method:"DELETE"}))}}pi("CapacitorHttp",{web:()=>new Pv});var lh;(function(i){i.IndexedDbLocal="INDEXED_DB_LOCAL",i.InMemory="IN_MEMORY",i.BrowserLocal="BROWSER_LOCAL",i.BrowserSession="BROWSER_SESSION"})(lh||(lh={}));var nh;(function(i){i.APPLE="apple.com",i.FACEBOOK="facebook.com",i.GAME_CENTER="gc.apple.com",i.GITHUB="github.com",i.GOOGLE="google.com",i.MICROSOFT="microsoft.com",i.PLAY_GAMES="playgames.google.com",i.TWITTER="twitter.com",i.YAHOO="yahoo.com",i.PASSWORD="password",i.PHONE="phone"})(nh||(nh={}));const Dr=pi("FirebaseAuthentication",{web:()=>Uu(()=>import("./web-zIPXU54N.js"),__vite__mapDeps([0,1])).then(i=>new i.FirebaseAuthenticationWeb)}),Ur={async signInWithGoogle(){try{const i=await Dr.signInWithGoogle();return{user:i.user,credential:i.credential}}catch(i){throw console.error("Error signing in with Google:",i),i}},async signOut(){try{await Dr.signOut()}catch(i){throw console.error("Error signing out:",i),i}},async getCurrentUser(){try{return(await Dr.getCurrentUser()).user}catch(i){return console.error("Error getting current user:",i),null}},onAuthStateChanged(i){return Dr.addListener("authStateChange",i)}},Jh=w.createContext(),Iv=({children:i})=>{const[c,u]=w.useState(null),[o,f]=w.useState(!0);w.useEffect(()=>{(async()=>{try{const b=await Ur.getCurrentUser();u(b)}catch(b){console.error("Error checking current user:",b)}finally{f(!1)}})();const g=Ur.onAuthStateChanged(b=>{u(b.user),f(!1)});return()=>{g&&typeof g.remove=="function"&&g.remove()}},[]);const v={user:c,loading:o,signInWithGoogle:async()=>{try{f(!0);const p=await Ur.signInWithGoogle();return u(p.user),p}catch(p){throw console.error("Error signing in:",p),p}finally{f(!1)}},signOut:async()=>{try{f(!0),await Ur.signOut(),u(null)}catch(p){throw console.error("Error signing out:",p),p}finally{f(!1)}},isAuthenticated:!!c};return s.jsx(Jh.Provider,{value:v,children:i})},Ka=()=>{const i=w.useContext(Jh);if(!i)throw new Error("useAuth must be used within an AuthProvider");return i},eb=()=>{const i=xt(),{getTotalItems:c}=cn(),{user:u,isAuthenticated:o}=Ka(),f=c(),h=()=>{i(o?"/my-profile":"/auth/login")};return s.jsx("header",{className:"sticky top-0 z-50 w-full border-b border-gray-200 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"flex h-16 items-center justify-between",children:[s.jsx(_u,{to:"/",className:"flex items-center space-x-2",children:s.jsx("div",{className:"gradient-primary rounded-xl p-2",children:s.jsx("span",{className:"text-xl font-bold text-white",children:"FuXion"})})}),s.jsx("div",{className:"hidden md:flex flex-1 max-w-md mx-8",children:s.jsxs("div",{className:"relative w-full",children:[s.jsx(Yr,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),s.jsx("input",{type:"text",placeholder:"Buscar productos...",className:"w-full rounded-xl border border-gray-200 bg-white pl-10 pr-4 py-2 text-sm focus:border-electric-orange-500 focus:outline-none focus:ring-2 focus:ring-electric-orange-500/20"})]})}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx(W,{variant:"ghost",size:"icon",className:"md:hidden",children:s.jsx(Yr,{className:"h-5 w-5"})}),s.jsxs(W,{variant:"ghost",size:"icon",onClick:h,className:"relative",children:[o&&u?.photoURL?s.jsx("img",{src:u.photoURL,alt:u.displayName,className:"h-6 w-6 rounded-full object-cover"}):s.jsx(Oh,{className:"h-5 w-5"}),o&&s.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-lime-green-500 rounded-full border-2 border-white"})]}),s.jsxs(W,{variant:"ghost",size:"icon",className:"relative",onClick:()=>i("/cart"),children:[s.jsx(Vr,{className:"h-5 w-5"}),f>0&&s.jsx(Za,{variant:"default",className:"absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center",children:f})]}),s.jsx(W,{variant:"ghost",size:"icon",className:"md:hidden",children:s.jsx(zy,{className:"h-5 w-5"})})]})]})})})},tb=({children:i})=>s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(eb,{}),s.jsx("main",{className:"flex-1",children:i})]}),ge=ga.forwardRef(({className:i,...c},u)=>s.jsx("div",{ref:u,className:Ft("rounded-2xl border border-gray-100 bg-white shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-1",i),...c}));ge.displayName="Card";const ct=ga.forwardRef(({className:i,...c},u)=>s.jsx("div",{ref:u,className:Ft("flex flex-col space-y-1.5 p-6",i),...c}));ct.displayName="CardHeader";const ut=ga.forwardRef(({className:i,...c},u)=>s.jsx("h3",{ref:u,className:Ft("text-2xl font-bold leading-none tracking-tight text-gray-900",i),...c}));ut.displayName="CardTitle";const ab=ga.forwardRef(({className:i,...c},u)=>s.jsx("p",{ref:u,className:Ft("text-sm text-gray-600",i),...c}));ab.displayName="CardDescription";const ye=ga.forwardRef(({className:i,...c},u)=>s.jsx("div",{ref:u,className:Ft("p-6 pt-0",i),...c}));ye.displayName="CardContent";const $h=ga.forwardRef(({className:i,...c},u)=>s.jsx("div",{ref:u,className:Ft("flex items-center p-6 pt-0",i),...c}));$h.displayName="CardFooter";const lb=({healthNeed:i})=>{const c=xt(),u=()=>{c(`/products?category=${i.category}`)};return s.jsxs(ge,{className:"cursor-pointer overflow-hidden group animate-enter hover:scale-105 transition-all duration-300",onClick:u,children:[s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:Ft("h-32 w-full",i.color)}),s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:s.jsx("img",{src:i.image,alt:i.product,className:"w-20 h-20 object-contain group-hover:scale-110 transition-transform duration-300"})})]}),s.jsxs("div",{className:"p-6 space-y-3",children:[s.jsx("h3",{className:"text-sm font-bold text-gray-800 leading-tight",children:i.title}),s.jsx("p",{className:"text-2xl font-black text-gray-900 tracking-tight",children:i.product}),s.jsx("div",{className:"pt-2",children:s.jsx("span",{className:"text-sm font-semibold text-electric-orange-500 group-hover:text-electric-orange-600 transition-colors",children:"Ver productos →"})})]})]})},Fh=[{id:"estrenimiento",title:"ESTREÑIMIENTO",product:"PRUNEX 1",color:"bg-red-600",category:"digestivo",image:"https://placehold.co/150x150/dc2626/FFFFFF/PNG?text=PRUNEX"},{id:"gastritis",title:"GASTRITIS / ACIDEZ REFLUJOS",product:"OMEGAFIT",color:"bg-orange-500",category:"digestivo",image:"https://placehold.co/150x150/f97316/FFFFFF/PNG?text=OMEGAFIT"},{id:"quema-grasa",title:"QUEMA GRASA",product:"THERMO T5",color:"bg-blue-500",category:"perdida-peso",image:"https://placehold.co/150x150/3b82f6/FFFFFF/PNG?text=THERMO+T5"},{id:"colageno",title:"COLÁGENO PARA TU PIEL",product:"BEAUTY-IN",color:"bg-pink-500",category:"belleza",image:"https://placehold.co/150x150/ec4899/FFFFFF/PNG?text=BEAUTY-IN"},{id:"energia",title:"ENERGÍA TODO EL DÍA",product:"VITA XTRA+",color:"bg-blue-800",category:"energia",image:"https://placehold.co/150x150/1e40af/FFFFFF/PNG?text=VITA+XTRA%2B"},{id:"vias-respiratorias",title:"LIMPIA TUS VÍAS RESPIRATORIAS",product:"YERBA LIFE",color:"bg-green-600",category:"respiratorio",image:"https://placehold.co/150x150/16a34a/FFFFFF/PNG?text=YERBA+LIFE"},{id:"cerebro",title:"RELAJA TU CEREBRO",product:"NO STRESS",color:"bg-yellow-600",category:"bienestar",image:"https://placehold.co/150x150/eab308/FFFFFF/PNG?text=NO+STRESS"},{id:"sangre",title:"LIMPIA TU SANGRE",product:"Q BALANCE",color:"bg-lime-500",category:"detox",image:"https://placehold.co/150x150/84cc16/FFFFFF/PNG?text=Q+BALANCE"},{id:"defensas",title:"SUBE TUS DEFENSAS",product:"VITAMINAS",color:"bg-cyan-500",category:"inmunidad",image:"https://placehold.co/150x150/06b6d4/FFFFFF/PNG?text=VITAMINAS"},{id:"concentracion",title:"MEJORA TU CONCENTRACIÓN",product:"ON",color:"bg-purple-600",category:"mental",image:"https://placehold.co/150x150/9333ea/FFFFFF/PNG?text=ON"}],nb=()=>s.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100",children:[s.jsxs("section",{className:"relative overflow-hidden bg-gradient-to-r from-electric-orange-500 via-soft-magenta-500 to-cobalt-blue-500 py-20",children:[s.jsx("div",{className:"absolute inset-0 bg-black/10"}),s.jsxs("div",{className:"relative container mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[s.jsxs("h1",{className:"text-4xl md:text-6xl lg:text-7xl font-black text-white mb-6 animate-fade-in",children:["¿Qué te gustaría",s.jsx("br",{}),s.jsx("span",{className:"text-lime-green-400",children:"mejorar"})," en tu",s.jsx("br",{}),"salud hoy?"]}),s.jsx("p",{className:"text-xl md:text-2xl text-white/90 font-medium max-w-3xl mx-auto animate-slide-up",children:"Descubre productos naturales que transformarán tu bienestar"})]}),s.jsx("div",{className:"absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full animate-pulse-soft"}),s.jsx("div",{className:"absolute bottom-10 right-10 w-32 h-32 bg-white/5 rounded-full animate-bounce-gentle"}),s.jsx("div",{className:"absolute top-1/2 left-1/4 w-16 h-16 bg-lime-green-400/20 rounded-full animate-pulse-soft"})]}),s.jsx("section",{className:"py-16",children:s.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[s.jsxs("div",{className:"text-center mb-12",children:[s.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Encuentra tu solución perfecta"}),s.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Selecciona la necesidad que más te identifique y descubre nuestros productos especializados"})]}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Fh.map((i,c)=>s.jsx("div",{className:"animate-enter-delayed",style:{animationDelay:`${c*.1}s`},children:s.jsx(lb,{healthNeed:i})},i.id))})]})}),s.jsx("section",{className:"py-16 bg-gradient-to-r from-cobalt-blue-500 to-electric-orange-500",children:s.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[s.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"¿No encuentras lo que buscas?"}),s.jsx("p",{className:"text-xl text-white/90 mb-8 max-w-2xl mx-auto",children:"Explora nuestro catálogo completo de productos naturales para tu bienestar"}),s.jsx("button",{className:"btn-success text-lg px-8 py-4",children:"Ver todos los productos"})]})})]}),ib=({product:i})=>{const c=xt(),{addItem:u}=cn(),o=x=>{x.stopPropagation(),u(i)},f=()=>{c(`/product/${i.id}`)},h=i.imageUrl||`https://placehold.co/300x300/f3f4f6/9ca3af/PNG?text=${encodeURIComponent(i.name||"Producto")}`;return s.jsxs(ge,{className:"cursor-pointer group overflow-hidden animate-enter",onClick:f,children:[s.jsxs("div",{className:"relative overflow-hidden bg-gray-100",children:[s.jsx("img",{src:h,alt:i.name,className:"w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"}),i.discount&&s.jsxs(Za,{variant:"destructive",className:"absolute top-3 left-3",children:["-",i.discount,"%"]}),s.jsx(W,{size:"icon",className:"absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300",onClick:o,children:s.jsx(Vr,{className:"h-4 w-4"})})]}),s.jsxs(ye,{className:"p-4",children:[i.category&&s.jsx(Za,{variant:"outline",className:"mb-2 text-xs",children:i.category}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-electric-orange-600 transition-colors",children:i.name}),i.description&&s.jsx("p",{className:"text-sm text-gray-600 mb-3 line-clamp-2",children:i.description}),s.jsxs("div",{className:"flex items-center space-x-2",children:[i.originalPrice&&i.originalPrice>i.price&&s.jsx("span",{className:"text-sm text-gray-400 line-through",children:ke(i.originalPrice)}),s.jsx("span",{className:"text-lg font-bold text-electric-orange-600",children:ke(i.price)})]})]}),s.jsx($h,{className:"p-4 pt-0",children:s.jsxs(W,{className:"w-full",onClick:o,children:[s.jsx(Vr,{className:"h-4 w-4 mr-2"}),"Agregar al carrito"]})})]})},st=ga.forwardRef(({className:i,type:c,...u},o)=>s.jsx("input",{type:c,className:Ft("flex h-12 w-full rounded-xl border border-gray-200 bg-white px-4 py-3 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-electric-orange-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200",i),ref:o,...u}));st.displayName="Input";const Ht=pi("FirebaseFirestore",{web:()=>Uu(()=>import("./web-CkwtYyrc.js"),__vite__mapDeps([2,1])).then(i=>new i.FirebaseFirestoreWeb)}),Au={async getAll(){try{return(await Ht.getCollection({reference:"products"})).snapshots.map(c=>({id:c.id,...c.data}))}catch(i){return console.error("Error fetching products:",i),[]}},async getById(i){try{const c=await Ht.getDocument({reference:`products/${i}`});return{id:c.snapshot.id,...c.snapshot.data}}catch(c){return console.error("Error fetching product:",c),null}},async getByCategory(i){try{return(await Ht.getCollection({reference:"products",compositeFilter:{type:"and",queryConstraints:[{type:"where",fieldPath:"category",opStr:"==",value:i}]}})).snapshots.map(u=>({id:u.id,...u.data}))}catch(c){return console.error("Error fetching products by category:",c),[]}}},di={async create(i){try{return(await Ht.addDocument({reference:"orders",data:{...i,orderDate:new Date().toISOString(),status:"awaiting_admin_validation"}})).reference.id}catch(c){throw console.error("Error creating order:",c),c}},async getAll(){try{return(await Ht.getCollection({reference:"orders"})).snapshots.map(c=>({id:c.id,...c.data}))}catch(i){return console.error("Error fetching orders:",i),[]}},async getById(i){try{const c=await Ht.getDocument({reference:`orders/${i}`});return{id:c.snapshot.id,...c.snapshot.data}}catch(c){return console.error("Error fetching order:",c),null}},async updateStatus(i,c,u=null){try{const o={status:c};u&&(o.trackingNumber=u),await Ht.updateDocument({reference:`orders/${i}`,data:o})}catch(o){throw console.error("Error updating order:",o),o}},async delete(i){try{await Ht.deleteDocument({reference:`orders/${i}`})}catch(c){throw console.error("Error deleting order:",c),c}}},en={async getAll(i){try{return(await Ht.getCollection({reference:`users/${i}/addresses`})).snapshots.map(u=>({id:u.id,...u.data}))}catch(c){return console.error("Error fetching addresses:",c),[]}},async create(i,c){try{return(await Ht.addDocument({reference:`users/${i}/addresses`,data:c})).reference.id}catch(u){throw console.error("Error creating address:",u),u}},async update(i,c,u){try{await Ht.updateDocument({reference:`users/${i}/addresses/${c}`,data:u})}catch(o){throw console.error("Error updating address:",o),o}},async delete(i,c){try{await Ht.deleteDocument({reference:`users/${i}/addresses/${c}`})}catch(u){throw console.error("Error deleting address:",u),u}}},rb=(i=null)=>{const[c,u]=w.useState([]),[o,f]=w.useState(!0),[h,x]=w.useState(null);return w.useEffect(()=>{(async()=>{try{f(!0),x(null);let p;i?p=await Au.getByCategory(i):p=await Au.getAll(),u(p)}catch(p){x(p.message),console.error("Error fetching products:",p)}finally{f(!1)}})()},[i]),{products:c,loading:o,error:h}},sb=i=>{const[c,u]=w.useState(null),[o,f]=w.useState(!0),[h,x]=w.useState(null);return w.useEffect(()=>{(async()=>{if(i)try{f(!0),x(null);const p=await Au.getById(i);u(p)}catch(p){x(p.message),console.error("Error fetching product:",p)}finally{f(!1)}})()},[i]),{product:c,loading:o,error:h}},cb=()=>{const[i]=Wx(),c=i.get("category"),[u,o]=w.useState(""),[f,h]=w.useState("grid"),{products:x,loading:v,error:p}=rb(c),g=x.filter(E=>E.name?.toLowerCase().includes(u.toLowerCase())||E.description?.toLowerCase().includes(u.toLowerCase())),b=c?Fh.find(E=>E.category===c):null;return v?s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-electric-orange-500 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-600",children:"Cargando productos..."})]})}):p?s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsxs("p",{className:"text-red-600 mb-4",children:["Error al cargar productos: ",p]}),s.jsx(W,{onClick:()=>window.location.reload(),children:"Intentar de nuevo"})]})}):s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("section",{className:"bg-gradient-to-r from-electric-orange-500 to-soft-magenta-500 py-12",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"text-center text-white",children:b?s.jsxs(s.Fragment,{children:[s.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:b.title}),s.jsxs("p",{className:"text-xl opacity-90",children:["Productos especializados para ",b.product]})]}):s.jsxs(s.Fragment,{children:[s.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:"Todos los Productos"}),s.jsx("p",{className:"text-xl opacity-90",children:"Descubre nuestra gama completa de productos naturales"})]})})})}),s.jsx("section",{className:"py-8 bg-white border-b",children:s.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[s.jsxs("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[s.jsxs("div",{className:"relative flex-1 max-w-md",children:[s.jsx(Yr,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),s.jsx(st,{type:"text",placeholder:"Buscar productos...",value:u,onChange:E=>o(E.target.value),className:"pl-10"})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("span",{className:"text-sm text-gray-600",children:[g.length," productos encontrados"]}),s.jsxs("div",{className:"flex border rounded-lg overflow-hidden",children:[s.jsx(W,{variant:f==="grid"?"default":"ghost",size:"sm",onClick:()=>h("grid"),className:"rounded-none",children:s.jsx(Ny,{className:"h-4 w-4"})}),s.jsx(W,{variant:f==="list"?"default":"ghost",size:"sm",onClick:()=>h("list"),className:"rounded-none",children:s.jsx(Ay,{className:"h-4 w-4"})})]}),s.jsxs(W,{variant:"outline",size:"sm",children:[s.jsx(by,{className:"h-4 w-4 mr-2"}),"Filtros"]})]})]}),c&&s.jsxs("div",{className:"mt-4 flex items-center space-x-2",children:[s.jsx("span",{className:"text-sm text-gray-600",children:"Filtros activos:"}),s.jsxs(Za,{variant:"secondary",children:["Categoría: ",b?.title||c]})]})]})}),s.jsx("section",{className:"py-12",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:g.length===0?s.jsxs("div",{className:"text-center py-12",children:[s.jsx("p",{className:"text-gray-600 text-lg mb-4",children:"No se encontraron productos que coincidan con tu búsqueda."}),s.jsx(W,{onClick:()=>o(""),children:"Limpiar búsqueda"})]}):s.jsx("div",{className:`grid gap-6 ${f==="grid"?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4":"grid-cols-1"}`,children:g.map((E,j)=>s.jsx("div",{className:"animate-enter-delayed",style:{animationDelay:`${j*.05}s`},children:s.jsx(ib,{product:E})},E.id))})})})]})},ub=()=>{const{id:i}=bh(),c=xt(),{product:u,loading:o,error:f}=sb(i),{addItem:h}=cn(),[x,v]=w.useState(1),p=()=>{for(let E=0;E<x;E++)h(u)},g=E=>{v(Math.max(1,x+E))};if(o)return s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-electric-orange-500 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-600",children:"Cargando producto..."})]})});if(f||!u)return s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-red-600 mb-4",children:"Producto no encontrado"}),s.jsx(W,{onClick:()=>c("/products"),children:"Ver todos los productos"})]})});const b=u.imageUrl||`https://placehold.co/600x600/f3f4f6/9ca3af/PNG?text=${encodeURIComponent(u.name)}`;return s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-white border-b",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4",children:s.jsxs(W,{variant:"ghost",onClick:()=>c(-1),className:"flex items-center space-x-2",children:[s.jsx(Qa,{className:"h-4 w-4"}),s.jsx("span",{children:"Volver"})]})})}),s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsx("div",{className:"aspect-square rounded-2xl overflow-hidden bg-white shadow-lg",children:s.jsx("img",{src:b,alt:u.name,className:"w-full h-full object-cover"})}),s.jsx("div",{className:"grid grid-cols-4 gap-2",children:[1,2,3,4].map(E=>s.jsx("div",{className:"aspect-square rounded-lg overflow-hidden bg-gray-100 border-2 border-transparent hover:border-electric-orange-500 cursor-pointer transition-colors",children:s.jsx("img",{src:b,alt:`${u.name} ${E}`,className:"w-full h-full object-cover"})},E))})]}),s.jsxs("div",{className:"space-y-6",children:[u.category&&s.jsx(Za,{variant:"outline",className:"text-sm",children:u.category}),s.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900",children:u.name}),s.jsxs("div",{className:"flex items-center space-x-4",children:[u.originalPrice&&u.originalPrice>u.price&&s.jsx("span",{className:"text-xl text-gray-400 line-through",children:ke(u.originalPrice)}),s.jsx("span",{className:"text-3xl font-bold text-electric-orange-600",children:ke(u.price)}),u.originalPrice&&u.originalPrice>u.price&&s.jsxs(Za,{variant:"destructive",children:["-",Math.round((u.originalPrice-u.price)/u.originalPrice*100),"%"]})]}),u.description&&s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Descripción"}),s.jsx("p",{className:"text-gray-600 leading-relaxed",children:u.description})]}),u.usage&&s.jsx(ge,{children:s.jsxs(ye,{className:"p-4",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Modo de uso"}),s.jsx("p",{className:"text-gray-600",children:u.usage})]})}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Cantidad:"}),s.jsxs("div",{className:"flex items-center border border-gray-200 rounded-lg",children:[s.jsx(W,{variant:"ghost",size:"icon",onClick:()=>g(-1),disabled:x<=1,className:"h-10 w-10 rounded-none",children:s.jsx(zh,{className:"h-4 w-4"})}),s.jsx("span",{className:"px-4 py-2 text-center min-w-[3rem]",children:x}),s.jsx(W,{variant:"ghost",size:"icon",onClick:()=>g(1),className:"h-10 w-10 rounded-none",children:s.jsx(Zr,{className:"h-4 w-4"})})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs(W,{size:"lg",className:"w-full text-lg",onClick:p,children:[s.jsx(Vr,{className:"h-5 w-5 mr-2"}),"Agregar al carrito - ",ke(u.price*x)]}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsxs(W,{variant:"outline",size:"lg",className:"flex-1",children:[s.jsx(Ey,{className:"h-5 w-5 mr-2"}),"Favoritos"]}),s.jsxs(W,{variant:"outline",size:"lg",className:"flex-1",children:[s.jsx(By,{className:"h-5 w-5 mr-2"}),"Compartir"]})]})]}),u.features&&s.jsx(ge,{children:s.jsxs(ye,{className:"p-4",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Características"}),s.jsx("ul",{className:"space-y-2",children:u.features.map((E,j)=>s.jsxs("li",{className:"flex items-center text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-electric-orange-500 rounded-full mr-3"}),E]},j))})]})})]})]})})]})},ob=({item:i})=>{const{updateQuantity:c,removeItem:u}=cn(),o=h=>{h<=0?u(i.id):c(i.id,h)},f=i.imageUrl||`https://placehold.co/150x150/f3f4f6/9ca3af/PNG?text=${encodeURIComponent(i.name)}`;return s.jsx(ge,{className:"overflow-hidden",children:s.jsx(ye,{className:"p-4",children:s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("img",{src:f,alt:i.name,className:"w-16 h-16 object-cover rounded-lg"})}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("h3",{className:"text-sm font-semibold text-gray-900 truncate",children:i.name}),i.category&&s.jsx("p",{className:"text-xs text-gray-500 mt-1",children:i.category}),s.jsx("p",{className:"text-sm font-bold text-electric-orange-600 mt-1",children:ke(i.price)})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(W,{variant:"outline",size:"icon",onClick:()=>o(i.quantity-1),className:"h-8 w-8",children:s.jsx(zh,{className:"h-3 w-3"})}),s.jsx("span",{className:"text-sm font-medium min-w-[2rem] text-center",children:i.quantity}),s.jsx(W,{variant:"outline",size:"icon",onClick:()=>o(i.quantity+1),className:"h-8 w-8",children:s.jsx(Zr,{className:"h-3 w-3"})})]}),s.jsx("div",{className:"text-right",children:s.jsx("p",{className:"text-sm font-bold text-gray-900",children:ke(i.price*i.quantity)})}),s.jsx(W,{variant:"ghost",size:"icon",onClick:()=>u(i.id),className:"h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50",children:s.jsx(Kr,{className:"h-4 w-4"})})]})})})},db=()=>{const i=xt(),{items:c,clearCart:u,getTotalItems:o,getTotalPrice:f}=cn(),h=o(),x=f(),v=x>100?0:15,p=x+v;return c.length===0?s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-white border-b",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4",children:s.jsxs(W,{variant:"ghost",onClick:()=>i(-1),className:"flex items-center space-x-2",children:[s.jsx(Qa,{className:"h-4 w-4"}),s.jsx("span",{children:"Continuar comprando"})]})})}),s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-16",children:s.jsxs("div",{className:"text-center max-w-md mx-auto",children:[s.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(Xy,{className:"h-12 w-12 text-gray-400"})}),s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Tu carrito está vacío"}),s.jsx("p",{className:"text-gray-600 mb-8",children:"Agrega algunos productos para comenzar tu compra"}),s.jsx(W,{size:"lg",onClick:()=>i("/products"),children:"Explorar productos"})]})})]}):s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-white border-b",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs(W,{variant:"ghost",onClick:()=>i(-1),className:"flex items-center space-x-2",children:[s.jsx(Qa,{className:"h-4 w-4"}),s.jsx("span",{children:"Continuar comprando"})]}),s.jsxs("h1",{className:"text-2xl font-bold text-gray-900",children:["Carrito de Compras (",h,")"]}),s.jsxs(W,{variant:"outline",onClick:u,className:"flex items-center space-x-2 text-red-600 hover:text-red-700",children:[s.jsx(Kr,{className:"h-4 w-4"}),s.jsx("span",{className:"hidden sm:inline",children:"Vaciar carrito"})]})]})})}),s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"lg:col-span-2 space-y-4",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Productos en tu carrito"}),c.map(g=>s.jsx("div",{className:"animate-enter",children:s.jsx(ob,{item:g})},g.id))]}),s.jsx("div",{className:"lg:col-span-1",children:s.jsxs(ge,{className:"sticky top-8",children:[s.jsx(ct,{children:s.jsx(ut,{children:"Resumen del pedido"})}),s.jsxs(ye,{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsxs("span",{children:["Subtotal (",h," productos)"]}),s.jsx("span",{children:ke(x)})]}),s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Envío"}),s.jsx("span",{className:v===0?"text-green-600 font-medium":"",children:v===0?"Gratis":ke(v)})]}),v>0&&s.jsxs("p",{className:"text-xs text-gray-500",children:["Envío gratis en compras mayores a ",ke(100)]})]}),s.jsx("hr",{}),s.jsxs("div",{className:"flex justify-between text-lg font-bold",children:[s.jsx("span",{children:"Total"}),s.jsx("span",{className:"text-electric-orange-600",children:ke(p)})]}),s.jsx(W,{size:"lg",className:"w-full",onClick:()=>i("/checkout"),children:"Proceder al pago"}),s.jsx("div",{className:"text-center",children:s.jsx("p",{className:"text-xs text-gray-500",children:"🔒 Pago seguro con encriptación SSL"})}),s.jsx(W,{variant:"outline",className:"w-full",onClick:()=>i("/products"),children:"Continuar comprando"})]})]})})]})})]})},fb=()=>{const i=xt(),{user:c,signOut:u,isAuthenticated:o}=Ka(),f=async()=>{try{await u(),i("/")}catch(h){console.error("Error signing out:",h)}};return o?s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-gradient-to-r from-electric-orange-500 to-soft-magenta-500 py-12",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"text-center text-white",children:[s.jsx("div",{className:"w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4",children:c?.photoURL?s.jsx("img",{src:c.photoURL,alt:c.displayName,className:"w-20 h-20 rounded-full object-cover"}):s.jsx(Oh,{className:"h-12 w-12"})}),s.jsx("h1",{className:"text-3xl font-bold mb-2",children:c?.displayName||"Usuario"}),s.jsx("p",{className:"text-white/90",children:c?.email})]})})}),s.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[s.jsx(ge,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>i("/my-profile/addresses"),children:s.jsxs(ye,{className:"p-6 text-center",children:[s.jsx("div",{className:"w-12 h-12 bg-electric-orange-100 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(tn,{className:"h-6 w-6 text-electric-orange-600"})}),s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Mis Direcciones"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"Gestiona tus direcciones de envío"})]})}),s.jsx(ge,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>i("/my-profile/orders"),children:s.jsxs(ye,{className:"p-6 text-center",children:[s.jsx("div",{className:"w-12 h-12 bg-cobalt-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(ml,{className:"h-6 w-6 text-cobalt-blue-600"})}),s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Mis Pedidos"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"Revisa el estado de tus pedidos"})]})}),s.jsx(ge,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>i("/my-profile/settings"),children:s.jsxs(ye,{className:"p-6 text-center",children:[s.jsx("div",{className:"w-12 h-12 bg-soft-magenta-100 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(ky,{className:"h-6 w-6 text-soft-magenta-600"})}),s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Configuración"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"Ajusta tus preferencias"})]})})]}),s.jsx("div",{className:"mt-8 text-center",children:s.jsxs(W,{variant:"outline",onClick:f,className:"flex items-center space-x-2",children:[s.jsx(Rh,{className:"h-4 w-4"}),s.jsx("span",{children:"Cerrar Sesión"})]})})]})]}):s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs(ge,{className:"w-full max-w-md",children:[s.jsx(ct,{className:"text-center",children:s.jsx(ut,{children:"Iniciar Sesión"})}),s.jsxs(ye,{className:"space-y-4",children:[s.jsx("p",{className:"text-gray-600 text-center",children:"Inicia sesión para acceder a tu perfil"}),s.jsx(W,{className:"w-full",onClick:()=>i("/auth/login"),children:"Iniciar Sesión"})]})]})})},mb=()=>{const i=xt(),{user:c,isAuthenticated:u}=Ka(),[o,f]=w.useState([]),[h,x]=w.useState(!0),[v,p]=w.useState(!1),[g,b]=w.useState(null),[E,j]=w.useState({name:"",street:"",city:"",state:"",zipCode:"",country:"Perú",isDefault:!1});w.useEffect(()=>{u&&c&&G()},[u,c]);const G=async()=>{try{x(!0);const z=await en.getAll(c.uid);f(z)}catch(z){console.error("Error loading addresses:",z)}finally{x(!1)}},M=async z=>{z.preventDefault();try{g?await en.update(c.uid,g.id,E):await en.create(c.uid,E),p(!1),b(null),j({name:"",street:"",city:"",state:"",zipCode:"",country:"Perú",isDefault:!1}),G()}catch(P){console.error("Error saving address:",P)}},D=z=>{b(z),j(z),p(!0)},H=async z=>{if(window.confirm("¿Estás seguro de que quieres eliminar esta dirección?"))try{await en.delete(c.uid,z),G()}catch(P){console.error("Error deleting address:",P)}};return u?s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-white border-b",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs(W,{variant:"ghost",onClick:()=>i("/my-profile"),className:"flex items-center space-x-2",children:[s.jsx(Qa,{className:"h-4 w-4"}),s.jsx("span",{children:"Volver al perfil"})]}),s.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Mis Direcciones"}),s.jsxs(W,{onClick:()=>p(!0),className:"flex items-center space-x-2",children:[s.jsx(Zr,{className:"h-4 w-4"}),s.jsx("span",{children:"Nueva dirección"})]})]})})}),s.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[v&&s.jsxs(ge,{className:"mb-8",children:[s.jsx(ct,{children:s.jsx(ut,{children:g?"Editar Dirección":"Nueva Dirección"})}),s.jsx(ye,{children:s.jsxs("form",{onSubmit:M,className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(st,{placeholder:"Nombre completo",value:E.name,onChange:z=>j({...E,name:z.target.value}),required:!0}),s.jsx(st,{placeholder:"Dirección completa",value:E.street,onChange:z=>j({...E,street:z.target.value}),required:!0}),s.jsx(st,{placeholder:"Ciudad",value:E.city,onChange:z=>j({...E,city:z.target.value}),required:!0}),s.jsx(st,{placeholder:"Departamento/Estado",value:E.state,onChange:z=>j({...E,state:z.target.value}),required:!0}),s.jsx(st,{placeholder:"Código postal",value:E.zipCode,onChange:z=>j({...E,zipCode:z.target.value}),required:!0}),s.jsx(st,{placeholder:"País",value:E.country,onChange:z=>j({...E,country:z.target.value}),required:!0})]}),s.jsxs("div",{className:"flex justify-end space-x-4",children:[s.jsx(W,{type:"button",variant:"outline",onClick:()=>{p(!1),b(null)},children:"Cancelar"}),s.jsx(W,{type:"submit",children:g?"Actualizar":"Guardar"})]})]})})]}),h?s.jsx("div",{className:"text-center py-8",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-electric-orange-500 mx-auto"})}):o.length===0?s.jsxs("div",{className:"text-center py-12",children:[s.jsx(tn,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No tienes direcciones guardadas"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Agrega una dirección para facilitar tus compras"}),s.jsx(W,{onClick:()=>p(!0),children:"Agregar primera dirección"})]}):s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:o.map(z=>s.jsx(ge,{className:"relative",children:s.jsxs(ye,{className:"p-6",children:[s.jsxs("div",{className:"flex justify-between items-start mb-4",children:[s.jsx("h3",{className:"font-semibold text-gray-900",children:z.name}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx(W,{variant:"ghost",size:"icon",onClick:()=>D(z),children:s.jsx(Ky,{className:"h-4 w-4"})}),s.jsx(W,{variant:"ghost",size:"icon",onClick:()=>H(z.id),className:"text-red-500 hover:text-red-700",children:s.jsx(Kr,{className:"h-4 w-4"})})]})]}),s.jsxs("div",{className:"text-gray-600 space-y-1",children:[s.jsx("p",{children:z.street}),s.jsxs("p",{children:[z.city,", ",z.state]}),s.jsxs("p",{children:[z.zipCode,", ",z.country]})]})]})},z.id))})]})]}):s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs(ge,{className:"w-full max-w-md",children:[s.jsx(ct,{className:"text-center",children:s.jsx(ut,{children:"Acceso Requerido"})}),s.jsxs(ye,{className:"space-y-4",children:[s.jsx("p",{className:"text-gray-600 text-center",children:"Inicia sesión para gestionar tus direcciones"}),s.jsx(W,{className:"w-full",onClick:()=>i("/auth/login"),children:"Iniciar Sesión"})]})]})})},hb=()=>{const i=xt(),c=pa(),{signInWithGoogle:u,loading:o}=Ka(),[f,h]=w.useState(""),x=c.state?.from?.pathname||"/",v=async()=>{try{h(""),await u(),i(x,{replace:!0})}catch(g){console.error("Error signing in:",g),h("Error al iniciar sesión. Por favor, intenta de nuevo.")}},p=()=>{i(x==="/auth/login"?"/cart":x,{replace:!0})};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-electric-orange-50 to-soft-magenta-50 flex items-center justify-center p-4",children:s.jsxs("div",{className:"w-full max-w-md",children:[s.jsxs(W,{variant:"ghost",onClick:()=>i(-1),className:"mb-6 flex items-center space-x-2",children:[s.jsx(Qa,{className:"h-4 w-4"}),s.jsx("span",{children:"Volver"})]}),s.jsxs(ge,{className:"shadow-xl",children:[s.jsxs(ct,{className:"text-center space-y-4",children:[s.jsx("div",{className:"gradient-primary rounded-xl p-4 w-fit mx-auto",children:s.jsx("span",{className:"text-2xl font-bold text-white",children:"FuXion"})}),s.jsx(ut,{className:"text-2xl font-bold text-gray-900",children:"Bienvenido de vuelta"}),s.jsx("p",{className:"text-gray-600",children:"Inicia sesión para acceder a tu cuenta y continuar con tu compra"})]}),s.jsxs(ye,{className:"space-y-6",children:[f&&s.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:s.jsx("p",{className:"text-red-600 text-sm",children:f})}),s.jsx(W,{onClick:v,disabled:o,className:"w-full h-12 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center justify-center space-x-3",variant:"outline",children:o?s.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-gray-700"}):s.jsxs(s.Fragment,{children:[s.jsxs("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[s.jsx("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),s.jsx("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),s.jsx("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),s.jsx("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),s.jsx("span",{children:"Continuar con Google"})]})}),s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx("div",{className:"w-full border-t border-gray-300"})}),s.jsx("div",{className:"relative flex justify-center text-sm",children:s.jsx("span",{className:"px-2 bg-white text-gray-500",children:"o"})})]}),s.jsx(W,{onClick:p,variant:"outline",className:"w-full h-12",children:"Continuar como invitado"}),s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[s.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Beneficios de tener una cuenta:"}),s.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[s.jsx("li",{children:"• Guarda tus direcciones de envío"}),s.jsx("li",{children:"• Historial de pedidos"}),s.jsx("li",{children:"• Ofertas exclusivas"}),s.jsx("li",{children:"• Checkout más rápido"})]})]})]})]}),s.jsxs("p",{className:"text-center text-sm text-gray-500 mt-6",children:["Al continuar, aceptas nuestros"," ",s.jsx("a",{href:"#",className:"text-electric-orange-600 hover:underline",children:"Términos de Servicio"})," ","y"," ",s.jsx("a",{href:"#",className:"text-electric-orange-600 hover:underline",children:"Política de Privacidad"})]})]})})},gb=({selectedAddress:i,onAddressSelect:c,allowGuest:u=!1})=>{const{user:o,isAuthenticated:f}=Ka(),[h,x]=w.useState([]),[v,p]=w.useState(!1),[g,b]=w.useState(!1),[E,j]=w.useState({name:"",street:"",city:"",state:"",zipCode:"",country:"Perú"});w.useEffect(()=>{f&&o&&G()},[f,o]);const G=async()=>{try{p(!0);const D=await en.getAll(o.uid);x(D),D.length>0&&!i&&c(D[0])}catch(D){console.error("Error loading addresses:",D)}finally{p(!1)}},M=async D=>{D.preventDefault();try{if(f&&o){const z={id:await en.create(o.uid,E),...E};x([...h,z]),c(z)}else{const H={id:"guest",...E};c(H)}b(!1),j({name:"",street:"",city:"",state:"",zipCode:"",country:"Perú"})}catch(H){console.error("Error saving address:",H)}};return!f&&!u?s.jsx(ge,{children:s.jsxs(ye,{className:"p-6 text-center",children:[s.jsx(tn,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Inicia sesión para usar direcciones guardadas"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"O continúa como invitado e ingresa tu dirección"}),s.jsx(W,{onClick:()=>b(!0),children:"Ingresar dirección"})]})}):s.jsxs("div",{className:"space-y-4",children:[f&&h.length>0&&s.jsxs("div",{className:"space-y-3",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Direcciones guardadas"}),h.map(D=>s.jsx(ge,{className:`cursor-pointer transition-all ${i?.id===D.id?"ring-2 ring-electric-orange-500 bg-electric-orange-50":"hover:shadow-md"}`,onClick:()=>c(D),children:s.jsx(ye,{className:"p-4",children:s.jsxs("div",{className:"flex items-start justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:D.name}),s.jsx("p",{className:"text-gray-600 text-sm mt-1",children:D.street}),s.jsxs("p",{className:"text-gray-600 text-sm",children:[D.city,", ",D.state," ",D.zipCode]}),s.jsx("p",{className:"text-gray-600 text-sm",children:D.country})]}),i?.id===D.id&&s.jsx(fy,{className:"h-5 w-5 text-electric-orange-600"})]})})},D.id))]}),g?s.jsx(ge,{children:s.jsxs(ye,{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:f?"Nueva dirección":"Dirección de envío"}),s.jsxs("form",{onSubmit:M,className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(st,{placeholder:"Nombre completo",value:E.name,onChange:D=>j({...E,name:D.target.value}),required:!0}),s.jsx(st,{placeholder:"Dirección completa",value:E.street,onChange:D=>j({...E,street:D.target.value}),required:!0}),s.jsx(st,{placeholder:"Ciudad",value:E.city,onChange:D=>j({...E,city:D.target.value}),required:!0}),s.jsx(st,{placeholder:"Departamento/Estado",value:E.state,onChange:D=>j({...E,state:D.target.value}),required:!0}),s.jsx(st,{placeholder:"Código postal",value:E.zipCode,onChange:D=>j({...E,zipCode:D.target.value}),required:!0}),s.jsx(st,{placeholder:"País",value:E.country,onChange:D=>j({...E,country:D.target.value}),required:!0})]}),s.jsxs("div",{className:"flex justify-end space-x-4",children:[s.jsx(W,{type:"button",variant:"outline",onClick:()=>b(!1),children:"Cancelar"}),s.jsx(W,{type:"submit",children:f?"Guardar y usar":"Usar esta dirección"})]})]})]})}):s.jsxs(W,{variant:"outline",onClick:()=>b(!0),className:"w-full flex items-center justify-center space-x-2",children:[s.jsx(Zr,{className:"h-4 w-4"}),s.jsx("span",{children:f?"Agregar nueva dirección":"Ingresar dirección de envío"})]})]})};var ih;(function(i){i.Loaded="applePayLoaded",i.FailedToLoad="applePayFailedToLoad",i.Completed="applePayCompleted",i.Canceled="applePayCanceled",i.Failed="applePayFailed",i.DidSelectShippingContact="applePayDidSelectShippingContact",i.DidCreatePaymentMethod="applePayDidCreatePaymentMethod"})(ih||(ih={}));var rh;(function(i){i.Loaded="googlePayLoaded",i.FailedToLoad="googlePayFailedToLoad",i.Completed="googlePayCompleted",i.Canceled="googlePayCanceled",i.Failed="googlePayFailed"})(rh||(rh={}));var sh;(function(i){i.Loaded="paymentFlowLoaded",i.FailedToLoad="paymentFlowFailedToLoad",i.Opened="paymentFlowOpened",i.Created="paymentFlowCreated",i.Completed="paymentFlowCompleted",i.Canceled="paymentFlowCanceled",i.Failed="paymentFlowFailed"})(sh||(sh={}));var ch;(function(i){i.Loaded="paymentSheetLoaded",i.FailedToLoad="paymentSheetFailedToLoad",i.Completed="paymentSheetCompleted",i.Canceled="paymentSheetCanceled",i.Failed="paymentSheetFailed"})(ch||(ch={}));const Lr=pi("Stripe",{web:()=>Uu(()=>import("./web-DepcfB8I.js"),[]).then(i=>new i.StripeWeb)}),Hr={async initialize(){try{await Lr.initialize({publishableKey:"pk_test_your_publishable_key_here"})}catch(i){throw console.error("Error initializing Stripe:",i),i}},async createPaymentSheet(i,c="usd"){try{const u={id:"pi_"+Math.random().toString(36).substr(2,9),client_secret:"pi_"+Math.random().toString(36).substr(2,9)+"_secret_"+Math.random().toString(36).substr(2,9),amount:i*100,currency:c};return await Lr.createPaymentSheet({paymentIntentClientSecret:u.client_secret,merchantDisplayName:"FuXion",style:"alwaysDark",allowsDelayedPaymentMethods:!0}),u}catch(u){throw console.error("Error creating payment sheet:",u),u}},async presentPaymentSheet(){try{return await Lr.presentPaymentSheet()}catch(i){throw console.error("Error presenting payment sheet:",i),i}},async confirmPaymentSheet(){try{return await Lr.confirmPaymentSheet()}catch(i){throw console.error("Error confirming payment:",i),i}}},pb=()=>{const i=xt(),{items:c,getTotalPrice:u,clearCart:o}=cn(),{user:f,isAuthenticated:h}=Ka(),[x,v]=w.useState(1),[p,g]=w.useState(null),[b,E]=w.useState(!1),[j,G]=w.useState(!1),[M,D]=w.useState(null),H=u(),z=H>100?0:15,P=H+z;w.useEffect(()=>{c.length===0&&!j&&i("/cart")},[c,i,j]),w.useEffect(()=>{Hr.initialize().catch(console.error)},[]);const I=()=>{if(!p){alert("Por favor selecciona una dirección de envío");return}v(2)},ce=async()=>{try{E(!0);const ne=await Hr.createPaymentSheet(P);if(await Hr.presentPaymentSheet(),(await Hr.confirmPaymentSheet()).paymentResult==="Completed"){const V={userId:f?.uid||null,products:c.map(xe=>({id:xe.id,name:xe.name,price:xe.price,quantity:xe.quantity,imageUrl:xe.imageUrl})),shippingAddress:p,totalAmount:P,stripeTransactionId:ne.id,status:"awaiting_admin_validation"},te=await di.create(V);D(te),G(!0),o(),v(3)}}catch(ne){console.error("Payment error:",ne),alert("Error al procesar el pago. Por favor intenta de nuevo.")}finally{E(!1)}},F=[{number:1,title:"Dirección de envío",icon:tn},{number:2,title:"Método de pago",icon:Br},{number:3,title:"Confirmación",icon:gu}];return j?s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsx(ge,{className:"w-full max-w-md",children:s.jsxs(ye,{className:"p-8 text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-lime-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(gu,{className:"h-8 w-8 text-lime-green-600"})}),s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"¡Pedido confirmado!"}),s.jsxs("p",{className:"text-gray-600 mb-6",children:["Tu pedido #",M?.slice(-8)," ha sido recibido y está siendo procesado."]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx(W,{className:"w-full",onClick:()=>i("/my-profile/orders"),children:"Ver mis pedidos"}),s.jsx(W,{variant:"outline",className:"w-full",onClick:()=>i("/"),children:"Continuar comprando"})]})]})})}):s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-white border-b",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4",children:s.jsxs(W,{variant:"ghost",onClick:()=>i("/cart"),className:"flex items-center space-x-2",children:[s.jsx(Qa,{className:"h-4 w-4"}),s.jsx("span",{children:"Volver al carrito"})]})})}),s.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsx("div",{className:"mb-8",children:s.jsx("div",{className:"flex items-center justify-center space-x-8",children:F.map(ne=>{const pe=ne.icon,V=x===ne.number,te=x>ne.number;return s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${te?"bg-lime-green-500 text-white":V?"bg-electric-orange-500 text-white":"bg-gray-200 text-gray-500"}`,children:te?s.jsx(gu,{className:"h-5 w-5"}):s.jsx(pe,{className:"h-5 w-5"})}),s.jsx("span",{className:`text-sm font-medium ${V?"text-electric-orange-600":"text-gray-500"}`,children:ne.title})]},ne.number)})})}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"lg:col-span-2",children:[x===1&&s.jsxs(ge,{children:[s.jsx(ct,{children:s.jsxs(ut,{className:"flex items-center space-x-2",children:[s.jsx(tn,{className:"h-5 w-5"}),s.jsx("span",{children:"Dirección de envío"})]})}),s.jsxs(ye,{children:[s.jsx(gb,{selectedAddress:p,onAddressSelect:g,allowGuest:!0}),p&&s.jsx("div",{className:"mt-6",children:s.jsx(W,{onClick:I,className:"w-full",children:"Continuar al pago"})})]})]}),x===2&&s.jsxs(ge,{children:[s.jsx(ct,{children:s.jsxs(ut,{className:"flex items-center space-x-2",children:[s.jsx(Br,{className:"h-5 w-5"}),s.jsx("span",{children:"Método de pago"})]})}),s.jsxs(ye,{className:"space-y-6",children:[s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[s.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Enviar a:"}),s.jsxs("p",{className:"text-gray-600 text-sm",children:[p?.name,s.jsx("br",{}),p?.street,s.jsx("br",{}),p?.city,", ",p?.state," ",p?.zipCode]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:"Método de pago"}),s.jsx(ge,{className:"border-electric-orange-200 bg-electric-orange-50",children:s.jsx(ye,{className:"p-4",children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Br,{className:"h-6 w-6 text-electric-orange-600"}),s.jsxs("div",{children:[s.jsx("p",{className:"font-medium text-gray-900",children:"Tarjeta de crédito/débito"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Procesado de forma segura por Stripe"})]})]})})})]}),s.jsx(W,{onClick:ce,disabled:b,className:"w-full",size:"lg",children:b?s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),s.jsx("span",{children:"Procesando..."})]}):`Pagar ${ke(P)}`}),s.jsx("p",{className:"text-xs text-gray-500 text-center",children:"🔒 Tu información de pago está protegida con encriptación SSL"})]})]})]}),s.jsx("div",{className:"lg:col-span-1",children:s.jsxs(ge,{className:"sticky top-8",children:[s.jsx(ct,{children:s.jsxs(ut,{className:"flex items-center space-x-2",children:[s.jsx(ml,{className:"h-5 w-5"}),s.jsx("span",{children:"Resumen del pedido"})]})}),s.jsxs(ye,{className:"space-y-4",children:[s.jsx("div",{className:"space-y-3",children:c.map(ne=>s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("img",{src:ne.imageUrl||`https://placehold.co/50x50/f3f4f6/9ca3af/PNG?text=${encodeURIComponent(ne.name)}`,alt:ne.name,className:"w-12 h-12 object-cover rounded-lg"}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:ne.name}),s.jsxs("p",{className:"text-sm text-gray-500",children:["Cantidad: ",ne.quantity]})]}),s.jsx("p",{className:"text-sm font-medium text-gray-900",children:ke(ne.price*ne.quantity)})]},ne.id))}),s.jsx("hr",{}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Subtotal"}),s.jsx("span",{children:ke(H)})]}),s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Envío"}),s.jsx("span",{className:z===0?"text-green-600 font-medium":"",children:z===0?"Gratis":ke(z)})]})]}),s.jsx("hr",{}),s.jsxs("div",{className:"flex justify-between text-lg font-bold",children:[s.jsx("span",{children:"Total"}),s.jsx("span",{className:"text-electric-orange-600",children:ke(P)})]})]})]})})]})]})]})},uh=()=>{const i=xt(),{signInWithGoogle:c,loading:u}=Ka(),[o,f]=w.useState(""),h=async()=>{try{f(""),(await c()).user&&i("/admin/orders")}catch(x){console.error("Error signing in:",x),f("Error al iniciar sesión como administrador. Por favor, intenta de nuevo.")}};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 flex items-center justify-center p-4",children:s.jsxs("div",{className:"w-full max-w-md",children:[s.jsxs(W,{variant:"ghost",onClick:()=>i("/"),className:"mb-6 flex items-center space-x-2 text-white hover:text-gray-300",children:[s.jsx(Qa,{className:"h-4 w-4"}),s.jsx("span",{children:"Volver al inicio"})]}),s.jsxs(ge,{className:"shadow-2xl border-gray-700 bg-gray-800",children:[s.jsxs(ct,{className:"text-center space-y-4",children:[s.jsx("div",{className:"bg-electric-orange-500 rounded-xl p-4 w-fit mx-auto",children:s.jsx(Yy,{className:"h-8 w-8 text-white"})}),s.jsx(ut,{className:"text-2xl font-bold text-white",children:"Panel de Administración"}),s.jsx("p",{className:"text-gray-300",children:"Acceso restringido solo para operadores autorizados"})]}),s.jsxs(ye,{className:"space-y-6",children:[o&&s.jsx("div",{className:"bg-red-900/50 border border-red-700 rounded-lg p-3",children:s.jsx("p",{className:"text-red-300 text-sm",children:o})}),s.jsx(W,{onClick:h,disabled:u,className:"w-full h-12 bg-electric-orange-500 hover:bg-electric-orange-600 text-white flex items-center justify-center space-x-3",children:u?s.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):s.jsxs(s.Fragment,{children:[s.jsxs("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[s.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),s.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),s.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),s.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),s.jsx("span",{children:"Iniciar sesión con Google"})]})}),s.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[s.jsx("h4",{className:"font-semibold text-white mb-2",children:"🔒 Acceso Seguro"}),s.jsxs("ul",{className:"text-sm text-gray-300 space-y-1",children:[s.jsx("li",{children:"• Solo personal autorizado"}),s.jsx("li",{children:"• Todas las acciones son registradas"}),s.jsx("li",{children:"• Acceso monitoreado 24/7"})]})]})]})]}),s.jsx("p",{className:"text-center text-sm text-gray-400 mt-6",children:"Si no tienes acceso autorizado, por favor contacta al administrador del sistema"})]})})},Wh=()=>{const[i,c]=w.useState([]),[u,o]=w.useState(!0),[f,h]=w.useState(null),x=async()=>{try{o(!0),h(null);const g=await di.getAll();c(g)}catch(g){h(g.message),console.error("Error fetching orders:",g)}finally{o(!1)}};return w.useEffect(()=>{x()},[]),{orders:i,loading:u,error:f,loadOrders:x,updateOrderStatus:async(g,b,E=null)=>{try{await di.updateStatus(g,b,E),await x()}catch(j){throw console.error("Error updating order status:",j),j}},deleteOrder:async g=>{try{await di.delete(g),await x()}catch(b){throw console.error("Error deleting order:",b),b}}}},xb=i=>{const[c,u]=w.useState(null),[o,f]=w.useState(!0),[h,x]=w.useState(null);return w.useEffect(()=>{(async()=>{if(i)try{f(!0),x(null);const p=await di.getById(i);u(p)}catch(p){x(p.message),console.error("Error fetching order:",p)}finally{f(!1)}})()},[i]),{order:c,loading:o,error:h}},yb={awaiting_admin_validation:"bg-yellow-100 text-yellow-800",paid:"bg-blue-100 text-blue-800",shipped:"bg-green-100 text-green-800",delivered:"bg-lime-100 text-lime-800",cancelled:"bg-red-100 text-red-800"},vb={awaiting_admin_validation:"Pendiente validación",paid:"Pagado",shipped:"Enviado",delivered:"Entregado",cancelled:"Cancelado"},bb=()=>{const i=xt(),{signOut:c}=Ka(),{orders:u,loading:o,error:f}=Wh(),[h,x]=w.useState(""),[v,p]=w.useState("all"),g=async()=>{try{await c(),i("/admin/login")}catch(j){console.error("Error signing out:",j)}},b=u.filter(j=>{const G=j.id?.toLowerCase().includes(h.toLowerCase())||j.shippingAddress?.name?.toLowerCase().includes(h.toLowerCase())||j.stripeTransactionId?.toLowerCase().includes(h.toLowerCase()),M=v==="all"||j.status===v;return G&&M}),E={total:u.length,pending:u.filter(j=>j.status==="awaiting_admin_validation").length,paid:u.filter(j=>j.status==="paid").length,shipped:u.filter(j=>j.status==="shipped").length,totalRevenue:u.reduce((j,G)=>j+(G.totalAmount||0),0)};return o?s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-electric-orange-500 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-600",children:"Cargando órdenes..."})]})}):f?s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsxs("p",{className:"text-red-600 mb-4",children:["Error al cargar órdenes: ",f]}),s.jsx(W,{onClick:()=>window.location.reload(),children:"Intentar de nuevo"})]})}):s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-white border-b",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Panel de Administración"}),s.jsx("p",{className:"text-gray-600",children:"Gestión de órdenes y pedidos"})]}),s.jsxs(W,{variant:"outline",onClick:g,className:"flex items-center space-x-2",children:[s.jsx(Rh,{className:"h-4 w-4"}),s.jsx("span",{children:"Cerrar sesión"})]})]})})}),s.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8",children:[s.jsx(ge,{children:s.jsx(ye,{className:"p-6",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx(ml,{className:"h-8 w-8 text-electric-orange-500"}),s.jsxs("div",{className:"ml-4",children:[s.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Órdenes"}),s.jsx("p",{className:"text-2xl font-bold text-gray-900",children:E.total})]})]})})}),s.jsx(ge,{children:s.jsx(ye,{className:"p-6",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx(oy,{className:"h-8 w-8 text-yellow-500"}),s.jsxs("div",{className:"ml-4",children:[s.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Pendientes"}),s.jsx("p",{className:"text-2xl font-bold text-gray-900",children:E.pending})]})]})})}),s.jsx(ge,{children:s.jsx(ye,{className:"p-6",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx(ml,{className:"h-8 w-8 text-blue-500"}),s.jsxs("div",{className:"ml-4",children:[s.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Pagadas"}),s.jsx("p",{className:"text-2xl font-bold text-gray-900",children:E.paid})]})]})})}),s.jsx(ge,{children:s.jsx(ye,{className:"p-6",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx(ml,{className:"h-8 w-8 text-green-500"}),s.jsxs("div",{className:"ml-4",children:[s.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Enviadas"}),s.jsx("p",{className:"text-2xl font-bold text-gray-900",children:E.shipped})]})]})})}),s.jsx(ge,{children:s.jsx(ye,{className:"p-6",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx(py,{className:"h-8 w-8 text-lime-500"}),s.jsxs("div",{className:"ml-4",children:[s.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Ingresos"}),s.jsx("p",{className:"text-2xl font-bold text-gray-900",children:ke(E.totalRevenue)})]})]})})})]}),s.jsx(ge,{className:"mb-6",children:s.jsx(ye,{className:"p-6",children:s.jsxs("div",{className:"flex flex-col md:flex-row gap-4 items-center",children:[s.jsxs("div",{className:"relative flex-1",children:[s.jsx(Yr,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"}),s.jsx(st,{type:"text",placeholder:"Buscar por ID, cliente o transacción...",value:h,onChange:j=>x(j.target.value),className:"pl-10"})]}),s.jsxs("select",{value:v,onChange:j=>p(j.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-orange-500",children:[s.jsx("option",{value:"all",children:"Todos los estados"}),s.jsx("option",{value:"awaiting_admin_validation",children:"Pendiente validación"}),s.jsx("option",{value:"paid",children:"Pagado"}),s.jsx("option",{value:"shipped",children:"Enviado"}),s.jsx("option",{value:"delivered",children:"Entregado"}),s.jsx("option",{value:"cancelled",children:"Cancelado"})]}),s.jsxs("span",{className:"text-sm text-gray-600",children:[b.length," órdenes encontradas"]})]})})}),s.jsxs(ge,{children:[s.jsx(ct,{children:s.jsx(ut,{children:"Órdenes"})}),s.jsx(ye,{children:b.length===0?s.jsxs("div",{className:"text-center py-8",children:[s.jsx(ml,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-600",children:"No se encontraron órdenes"})]}):s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"w-full",children:[s.jsx("thead",{children:s.jsxs("tr",{className:"border-b",children:[s.jsx("th",{className:"text-left py-3 px-4 font-semibold text-gray-900",children:"ID"}),s.jsx("th",{className:"text-left py-3 px-4 font-semibold text-gray-900",children:"Cliente"}),s.jsx("th",{className:"text-left py-3 px-4 font-semibold text-gray-900",children:"Fecha"}),s.jsx("th",{className:"text-left py-3 px-4 font-semibold text-gray-900",children:"Total"}),s.jsx("th",{className:"text-left py-3 px-4 font-semibold text-gray-900",children:"Estado"}),s.jsx("th",{className:"text-left py-3 px-4 font-semibold text-gray-900",children:"Stripe ID"}),s.jsx("th",{className:"text-left py-3 px-4 font-semibold text-gray-900",children:"Acciones"})]})}),s.jsx("tbody",{children:b.map(j=>s.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[s.jsx("td",{className:"py-3 px-4",children:s.jsx("span",{className:"font-mono text-sm",children:j.id?.slice(-8)})}),s.jsx("td",{className:"py-3 px-4",children:s.jsxs("div",{children:[s.jsx("p",{className:"font-medium text-gray-900",children:j.shippingAddress?.name||"N/A"}),s.jsx("p",{className:"text-sm text-gray-500",children:j.shippingAddress?.city})]})}),s.jsx("td",{className:"py-3 px-4 text-sm text-gray-600",children:j.orderDate?Qh(j.orderDate):"N/A"}),s.jsx("td",{className:"py-3 px-4 font-semibold text-gray-900",children:ke(j.totalAmount||0)}),s.jsx("td",{className:"py-3 px-4",children:s.jsx(Za,{className:yb[j.status]||"bg-gray-100 text-gray-800",children:vb[j.status]||j.status})}),s.jsx("td",{className:"py-3 px-4",children:s.jsx("span",{className:"font-mono text-xs text-gray-600",children:j.stripeTransactionId?.slice(-8)||"N/A"})}),s.jsx("td",{className:"py-3 px-4",children:s.jsxs(W,{variant:"outline",size:"sm",onClick:()=>i(`/admin/order/${j.id}`),className:"flex items-center space-x-1",children:[s.jsx(yy,{className:"h-3 w-3"}),s.jsx("span",{children:"Ver"})]})})]},j.id))})]})})})]})]})]})},oh=[{value:"awaiting_admin_validation",label:"Pendiente validación",color:"bg-yellow-100 text-yellow-800"},{value:"paid",label:"Pagado",color:"bg-blue-100 text-blue-800"},{value:"shipped",label:"Enviado",color:"bg-green-100 text-green-800"},{value:"delivered",label:"Entregado",color:"bg-lime-100 text-lime-800"},{value:"cancelled",label:"Cancelado",color:"bg-red-100 text-red-800"}],jb=()=>{const{id:i}=bh(),c=xt(),{order:u,loading:o,error:f}=xb(i),{updateOrderStatus:h,deleteOrder:x}=Wh(),[v,p]=w.useState(""),[g,b]=w.useState(""),[E,j]=w.useState(!1);ga.useEffect(()=>{u&&(p(u.status),b(u.trackingNumber||""))},[u]);const G=async()=>{try{j(!0),await h(i,v,g||null),alert("Estado actualizado correctamente")}catch(H){console.error("Error updating status:",H),alert("Error al actualizar el estado")}finally{j(!1)}},M=async()=>{if(window.confirm("¿Estás seguro de que quieres eliminar esta orden? Esta acción no se puede deshacer."))try{await x(i),alert("Orden eliminada correctamente"),c("/admin/orders")}catch(H){console.error("Error deleting order:",H),alert("Error al eliminar la orden")}};if(o)return s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-electric-orange-500 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-600",children:"Cargando orden..."})]})});if(f||!u)return s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-red-600 mb-4",children:"Orden no encontrada"}),s.jsx(W,{onClick:()=>c("/admin/orders"),children:"Volver a órdenes"})]})});const D=oh.find(H=>H.value===u.status);return s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-white border-b",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs(W,{variant:"ghost",onClick:()=>c("/admin/orders"),className:"flex items-center space-x-2",children:[s.jsx(Qa,{className:"h-4 w-4"}),s.jsx("span",{children:"Volver a órdenes"})]}),s.jsxs("div",{children:[s.jsxs("h1",{className:"text-2xl font-bold text-gray-900",children:["Orden #",u.id?.slice(-8)]}),s.jsx("p",{className:"text-gray-600",children:u.orderDate?Qh(u.orderDate):"Fecha no disponible"})]})]}),s.jsxs(W,{variant:"destructive",onClick:M,className:"flex items-center space-x-2",children:[s.jsx(Kr,{className:"h-4 w-4"}),s.jsx("span",{children:"Eliminar orden"})]})]})})}),s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[s.jsxs(ge,{children:[s.jsx(ct,{children:s.jsxs(ut,{className:"flex items-center space-x-2",children:[s.jsx(ml,{className:"h-5 w-5"}),s.jsx("span",{children:"Estado de la orden"})]})}),s.jsxs(ye,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Estado actual:"}),s.jsx(Za,{className:D?.color,children:D?.label})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cambiar estado"}),s.jsx("select",{value:v,onChange:H=>p(H.target.value),className:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-orange-500",children:oh.map(H=>s.jsx("option",{value:H.value,children:H.label},H.value))})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Número de seguimiento (opcional)"}),s.jsx(st,{type:"text",placeholder:"Ej: TRK123456789",value:g,onChange:H=>b(H.target.value)})]})]}),s.jsxs(W,{onClick:G,disabled:E||v===u.status,className:"flex items-center space-x-2",children:[E?s.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):s.jsx(Uy,{className:"h-4 w-4"}),s.jsx("span",{children:"Actualizar estado"})]})]})]}),s.jsxs(ge,{children:[s.jsx(ct,{children:s.jsx(ut,{children:"Productos"})}),s.jsx(ye,{children:s.jsx("div",{className:"space-y-4",children:u.products?.map((H,z)=>s.jsxs("div",{className:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg",children:[s.jsx("img",{src:H.imageUrl||`https://placehold.co/80x80/f3f4f6/9ca3af/PNG?text=${encodeURIComponent(H.name)}`,alt:H.name,className:"w-16 h-16 object-cover rounded-lg"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:H.name}),s.jsxs("p",{className:"text-sm text-gray-600",children:["Cantidad: ",H.quantity]}),s.jsxs("p",{className:"text-sm font-medium text-electric-orange-600",children:[ke(H.price)," c/u"]})]}),s.jsx("div",{className:"text-right",children:s.jsx("p",{className:"font-semibold text-gray-900",children:ke(H.price*H.quantity)})})]},z))})})]})]}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs(ge,{children:[s.jsx(ct,{children:s.jsx(ut,{children:"Resumen"})}),s.jsxs(ye,{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Subtotal:"}),s.jsx("span",{children:ke((u.totalAmount||0)-15)})]}),s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Envío:"}),s.jsx("span",{children:ke(15)})]}),s.jsx("hr",{}),s.jsxs("div",{className:"flex justify-between font-semibold",children:[s.jsx("span",{children:"Total:"}),s.jsx("span",{className:"text-electric-orange-600",children:ke(u.totalAmount||0)})]})]})]}),s.jsxs(ge,{children:[s.jsx(ct,{children:s.jsxs(ut,{className:"flex items-center space-x-2",children:[s.jsx(tn,{className:"h-5 w-5"}),s.jsx("span",{children:"Dirección de envío"})]})}),s.jsx(ye,{children:u.shippingAddress?s.jsxs("div",{className:"text-sm text-gray-600 space-y-1",children:[s.jsx("p",{className:"font-semibold text-gray-900",children:u.shippingAddress.name}),s.jsx("p",{children:u.shippingAddress.street}),s.jsxs("p",{children:[u.shippingAddress.city,", ",u.shippingAddress.state]}),s.jsxs("p",{children:[u.shippingAddress.zipCode,", ",u.shippingAddress.country]})]}):s.jsx("p",{className:"text-gray-500",children:"No hay dirección disponible"})})]}),s.jsxs(ge,{children:[s.jsx(ct,{children:s.jsxs(ut,{className:"flex items-center space-x-2",children:[s.jsx(Br,{className:"h-5 w-5"}),s.jsx("span",{children:"Información de pago"})]})}),s.jsx(ye,{children:s.jsxs("div",{className:"text-sm text-gray-600 space-y-2",children:[s.jsxs("div",{children:[s.jsx("span",{className:"font-medium",children:"ID de transacción Stripe:"}),s.jsx("p",{className:"font-mono text-xs break-all",children:u.stripeTransactionId||"No disponible"})]}),u.trackingNumber&&s.jsxs("div",{children:[s.jsx("span",{className:"font-medium",children:"Número de seguimiento:"}),s.jsx("p",{className:"font-mono text-xs",children:u.trackingNumber})]})]})})]})]})]})})]})};function Nb(){return s.jsx(Iv,{children:s.jsx(Gv,{children:s.jsx(Zx,{children:s.jsx(tb,{children:s.jsxs(jx,{children:[s.jsx(dt,{path:"/",element:s.jsx(nb,{})}),s.jsx(dt,{path:"/products",element:s.jsx(cb,{})}),s.jsx(dt,{path:"/product/:id",element:s.jsx(ub,{})}),s.jsx(dt,{path:"/cart",element:s.jsx(db,{})}),s.jsx(dt,{path:"/auth/login",element:s.jsx(hb,{})}),s.jsx(dt,{path:"/my-profile",element:s.jsx(fb,{})}),s.jsx(dt,{path:"/my-profile/addresses",element:s.jsx(mb,{})}),s.jsx(dt,{path:"/checkout",element:s.jsx(pb,{})}),s.jsx(dt,{path:"/my-profile/orders",element:s.jsx("div",{className:"p-8 text-center",children:"Orders - Coming Soon"})}),s.jsx(dt,{path:"/my-profile/settings",element:s.jsx("div",{className:"p-8 text-center",children:"Settings - Coming Soon"})}),s.jsx(dt,{path:"/admin/login",element:s.jsx(uh,{})}),s.jsx(dt,{path:"/admin/orders",element:s.jsx(bb,{})}),s.jsx(dt,{path:"/admin/order/:id",element:s.jsx(jb,{})}),s.jsx(dt,{path:"/admin/*",element:s.jsx(uh,{})})]})})})})})}zp.createRoot(document.getElementById("root")).render(s.jsx(w.StrictMode,{children:s.jsx(Nb,{})}));export{ih as A,rh as G,nh as P,Kh as W,lh as a,ch as b,sh as c};
