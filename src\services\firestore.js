import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where
} from 'firebase/firestore'
import { db } from '../config/firebase'

// Para Capacitor, usamos el plugin nativo
import { FirebaseFirestore } from '@capacitor-firebase/firestore'
import { Capacitor } from '@capacitor/core'

// Products Service
export const productsService = {
  async getAll() {
    try {
      if (Capacitor.isNativePlatform()) {
        // Usar plugin de Capacitor en móviles
        const result = await FirebaseFirestore.getCollection({
          reference: 'products'
        })
        return result.snapshots.map(snapshot => ({
          id: snapshot.id,
          ...snapshot.data
        }))
      } else {
        // Usar Firebase Web SDK en navegador
        const querySnapshot = await getDocs(collection(db, 'products'))
        return querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
      }
    } catch (error) {
      console.error('Error fetching products:', error)
      return []
    }
  },

  async getById(id) {
    try {
      if (Capacitor.isNativePlatform()) {
        const result = await FirebaseFirestore.getDocument({
          reference: `products/${id}`
        })
        return {
          id: result.snapshot.id,
          ...result.snapshot.data
        }
      } else {
        const docRef = doc(db, 'products', id)
        const docSnap = await getDoc(docRef)
        if (docSnap.exists()) {
          return {
            id: docSnap.id,
            ...docSnap.data()
          }
        }
        return null
      }
    } catch (error) {
      console.error('Error fetching product:', error)
      return null
    }
  },

  async getByCategory(category) {
    try {
      if (Capacitor.isNativePlatform()) {
        const result = await FirebaseFirestore.getCollection({
          reference: 'products',
          compositeFilter: {
            type: 'and',
            queryConstraints: [
              {
                type: 'where',
                fieldPath: 'category',
                opStr: '==',
                value: category
              }
            ]
          }
        })
        return result.snapshots.map(snapshot => ({
          id: snapshot.id,
          ...snapshot.data
        }))
      } else {
        const q = query(collection(db, 'products'), where('category', '==', category))
        const querySnapshot = await getDocs(q)
        return querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
      }
    } catch (error) {
      console.error('Error fetching products by category:', error)
      return []
    }
  }
}

// Orders Service
export const ordersService = {
  async create(orderData) {
    try {
      const data = {
        ...orderData,
        orderDate: new Date().toISOString(),
        status: 'awaiting_admin_validation'
      }

      if (Capacitor.isNativePlatform()) {
        const result = await FirebaseFirestore.addDocument({
          reference: 'orders',
          data
        })
        return result.reference.id
      } else {
        const docRef = await addDoc(collection(db, 'orders'), data)
        return docRef.id
      }
    } catch (error) {
      console.error('Error creating order:', error)
      throw error
    }
  },

  async getAll() {
    try {
      if (Capacitor.isNativePlatform()) {
        const result = await FirebaseFirestore.getCollection({
          reference: 'orders'
        })
        return result.snapshots.map(snapshot => ({
          id: snapshot.id,
          ...snapshot.data
        }))
      } else {
        const querySnapshot = await getDocs(collection(db, 'orders'))
        return querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
      }
    } catch (error) {
      console.error('Error fetching orders:', error)
      return []
    }
  },

  async getById(id) {
    try {
      if (Capacitor.isNativePlatform()) {
        const result = await FirebaseFirestore.getDocument({
          reference: `orders/${id}`
        })
        return {
          id: result.snapshot.id,
          ...result.snapshot.data
        }
      } else {
        const docRef = doc(db, 'orders', id)
        const docSnap = await getDoc(docRef)
        if (docSnap.exists()) {
          return {
            id: docSnap.id,
            ...docSnap.data()
          }
        }
        return null
      }
    } catch (error) {
      console.error('Error fetching order:', error)
      return null
    }
  },

  async updateStatus(id, status, trackingNumber = null) {
    try {
      const updateData = { status }
      if (trackingNumber) {
        updateData.trackingNumber = trackingNumber
      }

      if (Capacitor.isNativePlatform()) {
        await FirebaseFirestore.updateDocument({
          reference: `orders/${id}`,
          data: updateData
        })
      } else {
        const docRef = doc(db, 'orders', id)
        await updateDoc(docRef, updateData)
      }
    } catch (error) {
      console.error('Error updating order:', error)
      throw error
    }
  },

  async delete(id) {
    try {
      if (Capacitor.isNativePlatform()) {
        await FirebaseFirestore.deleteDocument({
          reference: `orders/${id}`
        })
      } else {
        const docRef = doc(db, 'orders', id)
        await deleteDoc(docRef)
      }
    } catch (error) {
      console.error('Error deleting order:', error)
      throw error
    }
  }
}

// User Addresses Service
export const addressesService = {
  async getAll(userId) {
    try {
      if (Capacitor.isNativePlatform()) {
        const result = await FirebaseFirestore.getCollection({
          reference: `users/${userId}/addresses`
        })
        return result.snapshots.map(snapshot => ({
          id: snapshot.id,
          ...snapshot.data
        }))
      } else {
        const querySnapshot = await getDocs(collection(db, 'users', userId, 'addresses'))
        return querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
      }
    } catch (error) {
      console.error('Error fetching addresses:', error)
      return []
    }
  },

  async create(userId, addressData) {
    try {
      if (Capacitor.isNativePlatform()) {
        const result = await FirebaseFirestore.addDocument({
          reference: `users/${userId}/addresses`,
          data: addressData
        })
        return result.reference.id
      } else {
        const docRef = await addDoc(collection(db, 'users', userId, 'addresses'), addressData)
        return docRef.id
      }
    } catch (error) {
      console.error('Error creating address:', error)
      throw error
    }
  },

  async update(userId, addressId, addressData) {
    try {
      if (Capacitor.isNativePlatform()) {
        await FirebaseFirestore.updateDocument({
          reference: `users/${userId}/addresses/${addressId}`,
          data: addressData
        })
      } else {
        const docRef = doc(db, 'users', userId, 'addresses', addressId)
        await updateDoc(docRef, addressData)
      }
    } catch (error) {
      console.error('Error updating address:', error)
      throw error
    }
  },

  async delete(userId, addressId) {
    try {
      if (Capacitor.isNativePlatform()) {
        await FirebaseFirestore.deleteDocument({
          reference: `users/${userId}/addresses/${addressId}`
        })
      } else {
        const docRef = doc(db, 'users', userId, 'addresses', addressId)
        await deleteDoc(docRef)
      }
    } catch (error) {
      console.error('Error deleting address:', error)
      throw error
    }
  }
}
