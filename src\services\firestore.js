import { CapacitorFirestore } from '@capacitor-firebase/firestore'

// Products Service
export const productsService = {
  async getAll() {
    try {
      const result = await CapacitorFirestore.getCollection({
        reference: 'products'
      })
      return result.snapshots.map(snapshot => ({
        id: snapshot.id,
        ...snapshot.data
      }))
    } catch (error) {
      console.error('Error fetching products:', error)
      return []
    }
  },

  async getById(id) {
    try {
      const result = await CapacitorFirestore.getDocument({
        reference: `products/${id}`
      })
      return {
        id: result.snapshot.id,
        ...result.snapshot.data
      }
    } catch (error) {
      console.error('Error fetching product:', error)
      return null
    }
  },

  async getByCategory(category) {
    try {
      const result = await CapacitorFirestore.getCollection({
        reference: 'products',
        compositeFilter: {
          type: 'and',
          queryConstraints: [
            {
              type: 'where',
              fieldPath: 'category',
              opStr: '==',
              value: category
            }
          ]
        }
      })
      return result.snapshots.map(snapshot => ({
        id: snapshot.id,
        ...snapshot.data
      }))
    } catch (error) {
      console.error('Error fetching products by category:', error)
      return []
    }
  }
}

// Orders Service
export const ordersService = {
  async create(orderData) {
    try {
      const result = await CapacitorFirestore.addDocument({
        reference: 'orders',
        data: {
          ...orderData,
          orderDate: new Date().toISOString(),
          status: 'awaiting_admin_validation'
        }
      })
      return result.reference.id
    } catch (error) {
      console.error('Error creating order:', error)
      throw error
    }
  },

  async getAll() {
    try {
      const result = await CapacitorFirestore.getCollection({
        reference: 'orders'
      })
      return result.snapshots.map(snapshot => ({
        id: snapshot.id,
        ...snapshot.data
      }))
    } catch (error) {
      console.error('Error fetching orders:', error)
      return []
    }
  },

  async getById(id) {
    try {
      const result = await CapacitorFirestore.getDocument({
        reference: `orders/${id}`
      })
      return {
        id: result.snapshot.id,
        ...result.snapshot.data
      }
    } catch (error) {
      console.error('Error fetching order:', error)
      return null
    }
  },

  async updateStatus(id, status, trackingNumber = null) {
    try {
      const updateData = { status }
      if (trackingNumber) {
        updateData.trackingNumber = trackingNumber
      }
      
      await CapacitorFirestore.updateDocument({
        reference: `orders/${id}`,
        data: updateData
      })
    } catch (error) {
      console.error('Error updating order:', error)
      throw error
    }
  },

  async delete(id) {
    try {
      await CapacitorFirestore.deleteDocument({
        reference: `orders/${id}`
      })
    } catch (error) {
      console.error('Error deleting order:', error)
      throw error
    }
  }
}

// User Addresses Service
export const addressesService = {
  async getAll(userId) {
    try {
      const result = await CapacitorFirestore.getCollection({
        reference: `users/${userId}/addresses`
      })
      return result.snapshots.map(snapshot => ({
        id: snapshot.id,
        ...snapshot.data
      }))
    } catch (error) {
      console.error('Error fetching addresses:', error)
      return []
    }
  },

  async create(userId, addressData) {
    try {
      const result = await CapacitorFirestore.addDocument({
        reference: `users/${userId}/addresses`,
        data: addressData
      })
      return result.reference.id
    } catch (error) {
      console.error('Error creating address:', error)
      throw error
    }
  },

  async update(userId, addressId, addressData) {
    try {
      await CapacitorFirestore.updateDocument({
        reference: `users/${userId}/addresses/${addressId}`,
        data: addressData
      })
    } catch (error) {
      console.error('Error updating address:', error)
      throw error
    }
  },

  async delete(userId, addressId) {
    try {
      await CapacitorFirestore.deleteDocument({
        reference: `users/${userId}/addresses/${addressId}`
      })
    } catch (error) {
      console.error('Error deleting address:', error)
      throw error
    }
  }
}
