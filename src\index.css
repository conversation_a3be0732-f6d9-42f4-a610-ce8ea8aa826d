@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-white text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Botones audaces y modernos */
  .btn-primary {
    @apply bg-electric-orange-500 hover:bg-electric-orange-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95;
  }

  .btn-secondary {
    @apply bg-cobalt-blue-500 hover:bg-cobalt-blue-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95;
  }

  .btn-accent {
    @apply bg-soft-magenta-500 hover:bg-soft-magenta-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95;
  }

  .btn-success {
    @apply bg-lime-green-500 hover:bg-lime-green-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95;
  }

  /* Cards con estilo moderno */
  .card-modern {
    @apply bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100;
  }

  /* Animaciones de entrada */
  .animate-enter {
    @apply animate-fade-in;
  }

  .animate-enter-delayed {
    @apply animate-slide-up;
    animation-delay: 0.1s;
  }

  /* Gradientes audaces */
  .gradient-primary {
    @apply bg-gradient-to-r from-electric-orange-500 to-electric-orange-600;
  }

  .gradient-secondary {
    @apply bg-gradient-to-r from-cobalt-blue-500 to-cobalt-blue-600;
  }

  .gradient-accent {
    @apply bg-gradient-to-r from-soft-magenta-500 to-soft-magenta-600;
  }

  .gradient-success {
    @apply bg-gradient-to-r from-lime-green-500 to-lime-green-600;
  }
}
