import {
  Persistence,
  ProviderId
} from "./chunk-DAMQH3Q7.js";
import {
  registerPlugin
} from "./chunk-NXBGYFMC.js";
import "./chunk-G3PMV62Z.js";

// node_modules/@capacitor-firebase/authentication/dist/esm/index.js
var FirebaseAuthentication = registerPlugin("FirebaseAuthentication", {
  web: () => import("./web-YCPAKWET.js").then((m) => new m.FirebaseAuthenticationWeb())
});
export {
  FirebaseAuthentication,
  Persistence,
  ProviderId
};
//# sourceMappingURL=@capacitor-firebase_authentication.js.map
