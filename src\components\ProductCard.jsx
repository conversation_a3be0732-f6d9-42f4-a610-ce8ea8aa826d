import React from 'react'
import { useNavigate } from 'react-router-dom'
import { ShoppingCart } from 'lucide-react'
import { <PERSON>, CardContent, CardFooter } from './ui/Card'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { useCart } from '../context/CartContext'
import { formatPrice } from '../lib/utils'

export const ProductCard = ({ product }) => {
  const navigate = useNavigate()
  const { addItem } = useCart()

  const handleAddToCart = (e) => {
    e.stopPropagation()
    addItem(product)
  }

  const handleCardClick = () => {
    navigate(`/product/${product.id}`)
  }

  const imageUrl = product.imageUrl || `https://placehold.co/300x300/f3f4f6/9ca3af/PNG?text=${encodeURIComponent(product.name || 'Producto')}`

  return (
    <Card 
      className="cursor-pointer group overflow-hidden animate-enter"
      onClick={handleCardClick}
    >
      {/* Product Image */}
      <div className="relative overflow-hidden bg-gray-100">
        <img 
          src={imageUrl}
          alt={product.name}
          className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
        />
        
        {/* Discount Badge */}
        {product.discount && (
          <Badge 
            variant="destructive" 
            className="absolute top-3 left-3"
          >
            -{product.discount}%
          </Badge>
        )}
        
        {/* Quick Add Button */}
        <Button
          size="icon"
          className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          onClick={handleAddToCart}
        >
          <ShoppingCart className="h-4 w-4" />
        </Button>
      </div>

      <CardContent className="p-4">
        {/* Category */}
        {product.category && (
          <Badge variant="outline" className="mb-2 text-xs">
            {product.category}
          </Badge>
        )}
        
        {/* Product Name */}
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-electric-orange-600 transition-colors">
          {product.name}
        </h3>
        
        {/* Description */}
        {product.description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {product.description}
          </p>
        )}
        
        {/* Price */}
        <div className="flex items-center space-x-2">
          {product.originalPrice && product.originalPrice > product.price && (
            <span className="text-sm text-gray-400 line-through">
              {formatPrice(product.originalPrice)}
            </span>
          )}
          <span className="text-lg font-bold text-electric-orange-600">
            {formatPrice(product.price)}
          </span>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0">
        <Button 
          className="w-full"
          onClick={handleAddToCart}
        >
          <ShoppingCart className="h-4 w-4 mr-2" />
          Agregar al carrito
        </Button>
      </CardFooter>
    </Card>
  )
}
