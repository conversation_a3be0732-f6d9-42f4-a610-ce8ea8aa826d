import React from 'react'
import { useNavigate } from 'react-router-dom'
import { ShoppingCart, Heart } from 'lucide-react'
import { Card, CardContent, CardFooter } from './ui/Card'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { useCart } from '../context/CartContext'
import { formatPrice } from '../lib/utils'

export const ProductCard = ({ product }) => {
  const navigate = useNavigate()
  const { addItem } = useCart()

  const handleAddToCart = (e) => {
    e.stopPropagation()
    addItem(product)
  }

  const handleCardClick = () => {
    navigate(`/product/${product.id}`)
  }

  const imageUrl = product.imageUrl || `https://placehold.co/300x300/f3f4f6/9ca3af/PNG?text=${encodeURIComponent(product.name || 'Producto')}`

  const discountPercentage = product.originalPrice && product.originalPrice > product.price
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : null

  return (
    <Card
      className="cursor-pointer group overflow-hidden hover-lift"
      onClick={handleCardClick}
    >
      {/* Product Image */}
      <div className="relative overflow-hidden bg-gray-100">
        <img
          src={imageUrl}
          alt={product.name}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />

        {/* Discount Badge */}
        {discountPercentage && (
          <Badge
            variant="destructive"
            className="absolute top-3 left-3"
          >
            -{discountPercentage}%
          </Badge>
        )}

        {/* Action Buttons */}
        <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Button
            size="icon"
            variant="secondary"
            className="h-8 w-8"
            onClick={(e) => {
              e.stopPropagation()
              // Add to wishlist functionality
            }}
          >
            <Heart className="h-4 w-4" />
          </Button>
          <Button
            size="icon"
            className="h-8 w-8"
            onClick={handleAddToCart}
          >
            <ShoppingCart className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <CardContent className="p-4">
        {/* Category */}
        {product.category && (
          <Badge variant="outline" className="mb-2 text-xs capitalize">
            {product.category.replace('-', ' ')}
          </Badge>
        )}

        {/* Product Name */}
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-slate-900 transition-colors">
          {product.name}
        </h3>

        {/* Description */}
        {product.description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {product.description}
          </p>
        )}

        {/* Price */}
        <div className="flex items-center space-x-2 mb-4">
          {product.originalPrice && product.originalPrice > product.price && (
            <span className="text-sm text-gray-500 line-through">
              {formatPrice(product.originalPrice)}
            </span>
          )}
          <span className="text-lg font-bold text-gray-900">
            {formatPrice(product.price)}
          </span>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0">
        <Button
          className="w-full"
          onClick={handleAddToCart}
          variant="outline"
        >
          <ShoppingCart className="h-4 w-4 mr-2" />
          Agregar al carrito
        </Button>
      </CardFooter>
    </Card>
  )
}
