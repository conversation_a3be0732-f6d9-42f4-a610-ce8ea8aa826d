// node_modules/@capacitor-firebase/authentication/dist/esm/definitions.js
var Persistence;
(function(Persistence2) {
  Persistence2["IndexedDbLocal"] = "INDEXED_DB_LOCAL";
  Persistence2["InMemory"] = "IN_MEMORY";
  Persistence2["BrowserLocal"] = "BROWSER_LOCAL";
  Persistence2["BrowserSession"] = "BROWSER_SESSION";
})(Persistence || (Persistence = {}));
var ProviderId;
(function(ProviderId2) {
  ProviderId2["APPLE"] = "apple.com";
  ProviderId2["FACEBOOK"] = "facebook.com";
  ProviderId2["GAME_CENTER"] = "gc.apple.com";
  ProviderId2["GITHUB"] = "github.com";
  ProviderId2["GOOGLE"] = "google.com";
  ProviderId2["MICROSOFT"] = "microsoft.com";
  ProviderId2["PLAY_GAMES"] = "playgames.google.com";
  ProviderId2["TWITTER"] = "twitter.com";
  ProviderId2["YAHOO"] = "yahoo.com";
  ProviderId2["PASSWORD"] = "password";
  ProviderId2["PHONE"] = "phone";
})(ProviderId || (ProviderId = {}));

export {
  Persistence,
  ProviderId
};
//# sourceMappingURL=chunk-DAMQH3Q7.js.map
