import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Layout } from './components/layout/Layout'
import { CartProvider } from './context/CartContext'
import { AuthProvider } from './context/AuthContext'

// Pages
import { HomePage } from './pages/HomePage'
import { ProductsPage } from './pages/ProductsPage'
import { ProductDetailPage } from './pages/ProductDetailPage'
import { CartPage } from './pages/CartPage'
import { ProfilePage } from './pages/ProfilePage'
import { AddressesPage } from './pages/AddressesPage'
import { LoginPage } from './pages/LoginPage'
import { CheckoutPage } from './pages/CheckoutPage'
import { AdminLoginPage } from './pages/admin/AdminLoginPage'
import { AdminOrdersPage } from './pages/admin/AdminOrdersPage'
import { AdminOrderDetailPage } from './pages/admin/AdminOrderDetailPage'

function App() {
  return (
    <AuthProvider>
      <CartProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/products" element={<ProductsPage />} />
              <Route path="/product/:id" element={<ProductDetailPage />} />
              <Route path="/cart" element={<CartPage />} />
              <Route path="/auth/login" element={<LoginPage />} />
              <Route path="/my-profile" element={<ProfilePage />} />
              <Route path="/my-profile/addresses" element={<AddressesPage />} />
              {/* Placeholder routes for future implementation */}
              <Route path="/checkout" element={<CheckoutPage />} />
              <Route path="/my-profile/orders" element={<div className="p-8 text-center">Orders - Coming Soon</div>} />
              <Route path="/my-profile/settings" element={<div className="p-8 text-center">Settings - Coming Soon</div>} />
              <Route path="/admin/login" element={<AdminLoginPage />} />
              <Route path="/admin/orders" element={<AdminOrdersPage />} />
              <Route path="/admin/order/:id" element={<AdminOrderDetailPage />} />
              <Route path="/admin/*" element={<AdminLoginPage />} />
            </Routes>
          </Layout>
        </Router>
      </CartProvider>
    </AuthProvider>
  )
}

export default App
