import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Layout } from './components/layout/Layout'
import { CartProvider } from './context/CartContext'

// Pages
import { HomePage } from './pages/HomePage'
import { ProductsPage } from './pages/ProductsPage'
import { ProductDetailPage } from './pages/ProductDetailPage'
import { CartPage } from './pages/CartPage'

function App() {
  return (
    <CartProvider>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/products" element={<ProductsPage />} />
            <Route path="/product/:id" element={<ProductDetailPage />} />
            <Route path="/cart" element={<CartPage />} />
            {/* Placeholder routes for future implementation */}
            <Route path="/checkout" element={<div className="p-8 text-center">Checkout - Coming Soon</div>} />
            <Route path="/my-profile" element={<div className="p-8 text-center">Profile - Coming Soon</div>} />
            <Route path="/admin/*" element={<div className="p-8 text-center">Admin Panel - Coming Soon</div>} />
          </Routes>
        </Layout>
      </Router>
    </CartProvider>
  )
}

export default App
