import React, { useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { ArrowLeft } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { useAuth } from '../context/AuthContext'

export const LoginPage = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { signInWithGoogle, loading } = useAuth()
  const [error, setError] = useState('')

  // Get the intended destination from location state
  const from = location.state?.from?.pathname || '/'

  const handleGoogleSignIn = async () => {
    try {
      setError('')
      await signInWithGoogle()
      navigate(from, { replace: true })
    } catch (error) {
      console.error('Error signing in:', error)
      setError('Error al iniciar sesión. Por favor, intenta de nuevo.')
    }
  }

  const handleGuestContinue = () => {
    // For guest checkout, redirect to checkout or cart
    navigate(from === '/auth/login' ? '/cart' : from, { replace: true })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-electric-orange-50 to-soft-magenta-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Back Button */}
        <Button 
          variant="ghost" 
          onClick={() => navigate(-1)}
          className="mb-6 flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Volver</span>
        </Button>

        <Card className="shadow-xl">
          <CardHeader className="text-center space-y-4">
            {/* Logo */}
            <div className="gradient-primary rounded-xl p-4 w-fit mx-auto">
              <span className="text-2xl font-bold text-white">FuXion</span>
            </div>
            
            <CardTitle className="text-2xl font-bold text-gray-900">
              Bienvenido de vuelta
            </CardTitle>
            <p className="text-gray-600">
              Inicia sesión para acceder a tu cuenta y continuar con tu compra
            </p>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            {/* Google Sign In */}
            <Button
              onClick={handleGoogleSignIn}
              disabled={loading}
              className="w-full h-12 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center justify-center space-x-3"
              variant="outline"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-700"></div>
              ) : (
                <>
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  <span>Continuar con Google</span>
                </>
              )}
            </Button>

            {/* Divider */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">o</span>
              </div>
            </div>

            {/* Guest Continue */}
            <Button
              onClick={handleGuestContinue}
              variant="outline"
              className="w-full h-12"
            >
              Continuar como invitado
            </Button>

            {/* Benefits */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                Beneficios de tener una cuenta:
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Guarda tus direcciones de envío</li>
                <li>• Historial de pedidos</li>
                <li>• Ofertas exclusivas</li>
                <li>• Checkout más rápido</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <p className="text-center text-sm text-gray-500 mt-6">
          Al continuar, aceptas nuestros{' '}
          <a href="#" className="text-electric-orange-600 hover:underline">
            Términos de Servicio
          </a>{' '}
          y{' '}
          <a href="#" className="text-electric-orange-600 hover:underline">
            Política de Privacidad
          </a>
        </p>
      </div>
    </div>
  )
}
