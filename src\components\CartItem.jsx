import React from 'react'
import { Minus, Plus, Trash2 } from 'lucide-react'
import { Button } from './ui/Button'
import { Card, CardContent } from './ui/Card'
import { useCart } from '../context/CartContext'
import { formatPrice } from '../lib/utils'

export const CartItem = ({ item }) => {
  const { updateQuantity, removeItem } = useCart()

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity <= 0) {
      removeItem(item.id)
    } else {
      updateQuantity(item.id, newQuantity)
    }
  }

  const imageUrl = item.imageUrl || `https://placehold.co/150x150/f3f4f6/9ca3af/PNG?text=${encodeURIComponent(item.name)}`

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-4">
        <div className="flex items-center space-x-4">
          {/* Product Image */}
          <div className="flex-shrink-0">
            <img 
              src={imageUrl}
              alt={item.name}
              className="w-16 h-16 object-cover rounded-lg"
            />
          </div>

          {/* Product Info */}
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 truncate">
              {item.name}
            </h3>
            {item.category && (
              <p className="text-xs text-gray-500 mt-1">
                {item.category}
              </p>
            )}
            <p className="text-sm font-bold text-electric-orange-600 mt-1">
              {formatPrice(item.price)}
            </p>
          </div>

          {/* Quantity Controls */}
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => handleQuantityChange(item.quantity - 1)}
              className="h-8 w-8"
            >
              <Minus className="h-3 w-3" />
            </Button>
            
            <span className="text-sm font-medium min-w-[2rem] text-center">
              {item.quantity}
            </span>
            
            <Button
              variant="outline"
              size="icon"
              onClick={() => handleQuantityChange(item.quantity + 1)}
              className="h-8 w-8"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>

          {/* Total Price */}
          <div className="text-right">
            <p className="text-sm font-bold text-gray-900">
              {formatPrice(item.price * item.quantity)}
            </p>
          </div>

          {/* Remove Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => removeItem(item.id)}
            className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
