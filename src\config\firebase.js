import { initializeApp } from 'firebase/app'
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore'
import { getAuth, connectAuthEmulator } from 'firebase/auth'

// Tu configuración de Firebase
// Los valores se toman de las variables de entorno
const firebaseConfig = {
  apiKey: "AIzaSyAT7x-yBPHkC24grY4bK8a-zK0K7EIgI0I",
  authDomain: "fuxion-a2979.firebaseapp.com",
  projectId: "fuxion-a2979",
  storageBucket: "fuxion-a2979.firebasestorage.app",
  messagingSenderId: "558534925630",
  appId: "1:558534925630:web:fda12498780f20b1f1606f",
  measurementId: "G-LQB5W1DL94"
};

// Inicializar Firebase
const app = initializeApp(firebaseConfig)

// Inicializar servicios
export const db = getFirestore(app)
export const auth = getAuth(app)

// Para desarrollo local (opcional)
// Descomenta estas líneas si quieres usar los emuladores de Firebase
/*
if (import.meta.env.DEV) {
  try {
    connectFirestoreEmulator(db, 'localhost', 8080)
    connectAuthEmulator(auth, 'http://localhost:9099')
  } catch (error) {
    console.log('Emulators already connected')
  }
}
*/

export default app
