import { useState, useEffect } from 'react'
import { ordersService } from '../services/firestore'

export const useOrders = () => {
  const [orders, setOrders] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const loadOrders = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await ordersService.getAll()
      setOrders(data)
    } catch (err) {
      setError(err.message)
      console.error('Error fetching orders:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadOrders()
  }, [])

  const updateOrderStatus = async (orderId, status, trackingNumber = null) => {
    try {
      await ordersService.updateStatus(orderId, status, trackingNumber)
      await loadOrders() // Reload orders
    } catch (error) {
      console.error('Error updating order status:', error)
      throw error
    }
  }

  const deleteOrder = async (orderId) => {
    try {
      await ordersService.delete(orderId)
      await loadOrders() // Reload orders
    } catch (error) {
      console.error('Error deleting order:', error)
      throw error
    }
  }

  return { 
    orders, 
    loading, 
    error, 
    loadOrders, 
    updateOrderStatus, 
    deleteOrder 
  }
}

export const useOrder = (id) => {
  const [order, setOrder] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchOrder = async () => {
      if (!id) return
      
      try {
        setLoading(true)
        setError(null)
        
        const data = await ordersService.getById(id)
        setOrder(data)
      } catch (err) {
        setError(err.message)
        console.error('Error fetching order:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchOrder()
  }, [id])

  return { order, loading, error }
}
